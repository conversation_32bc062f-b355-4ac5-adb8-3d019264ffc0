#!/bin/bash

# RailGPT Complete Deployment Script for Google Cloud Platform
# This script deploys the entire RailGPT application to GCP

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}===== RailGPT GCP Deployment Script =====${NC}"
echo -e "This script will deploy the complete RailGPT application to Google Cloud Platform."
echo

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}Error: Google Cloud SDK (gcloud) is not installed.${NC}"
    echo -e "Please install it from: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Check if user is logged in
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${YELLOW}You are not logged in to Google Cloud.${NC}"
    echo -e "Please run: gcloud auth login"
    exit 1
fi

# Ensure .env file exists
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}Creating .env file from template...${NC}"
    cp deploy_working/.env.example .env
    echo -e "${YELLOW}Please edit the .env file with your API keys before continuing.${NC}"
    echo -e "${YELLOW}You need to add at least:${NC}"
    echo -e "  - SUPABASE_KEY (service role key)"
    echo -e "  - GEMINI_API_KEY"
    exit 1
fi

# Source environment variables
source .env

# Check for required API keys
if [ -z "$SUPABASE_KEY" ] || [ "$SUPABASE_KEY" = "your_actual_supabase_service_key_here" ]; then
    echo -e "${RED}Error: SUPABASE_KEY is not set in .env file${NC}"
    echo -e "Get your service role key from: https://supabase.com/dashboard/project/rkllidjktazafeinezgo/settings/api"
    exit 1
fi

if [ -z "$GEMINI_API_KEY" ] || [ "$GEMINI_API_KEY" = "your_actual_gemini_api_key_here" ]; then
    echo -e "${RED}Error: GEMINI_API_KEY is not set in .env file${NC}"
    echo -e "Get your API key from: https://makersuite.google.com/app/apikey"
    exit 1
fi

# Set project ID if not set
if [ -z "$PROJECT_ID" ]; then
    PROJECT_ID="railgpt-$(date +%s)"
    echo -e "${YELLOW}PROJECT_ID not set. Using generated ID: $PROJECT_ID${NC}"
fi

# Set region if not set
if [ -z "$REGION" ]; then
    REGION="us-central1"
fi

echo -e "${BLUE}Starting deployment with the following configuration:${NC}"
echo -e "  Project ID: $PROJECT_ID"
echo -e "  Region: $REGION"
echo -e "  Domain: ${DOMAIN:-Not configured}"
echo

# Confirm before proceeding
read -p "Continue with deployment? (y/n): " confirm
if [ "$confirm" != "y" ]; then
    echo -e "${YELLOW}Deployment cancelled.${NC}"
    exit 0
fi

echo -e "\n${BLUE}Step 1: Setting up Google Cloud Project${NC}"

# Set the project
gcloud config set project $PROJECT_ID

# Enable required APIs
echo -e "${BLUE}Enabling required Google Cloud APIs...${NC}"
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable storage.googleapis.com
gcloud services enable compute.googleapis.com
gcloud services enable cloudresourcemanager.googleapis.com

echo -e "\n${BLUE}Step 2: Building and Deploying Backend${NC}"

# Create backend Dockerfile if it doesn't exist
if [ ! -f "backend/Dockerfile" ]; then
    echo -e "${YELLOW}Creating backend Dockerfile...${NC}"
    cat > backend/Dockerfile << 'EOF'
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create uploads directory
RUN mkdir -p data/uploads

# Expose port
EXPOSE 8000

# Start the application
CMD ["python", "-m", "uvicorn", "server:app", "--host", "0.0.0.0", "--port", "8000"]
EOF
fi

# Build and deploy backend to Cloud Run
echo -e "${BLUE}Building backend container...${NC}"
cd backend

# Submit build to Cloud Build
gcloud builds submit --tag gcr.io/$PROJECT_ID/railgpt-backend

echo -e "${BLUE}Deploying backend to Cloud Run...${NC}"
gcloud run deploy railgpt-backend \
    --image gcr.io/$PROJECT_ID/railgpt-backend \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --memory 2Gi \
    --cpu 2 \
    --timeout 300 \
    --max-instances 100 \
    --set-env-vars "SUPABASE_KEY=$SUPABASE_KEY,GEMINI_API_KEY=$GEMINI_API_KEY,OPENAI_API_KEY=$OPENAI_API_KEY,GROQ_API_KEY=$GROQ_API_KEY,SUPABASE_URL=$SUPABASE_URL"

# Get backend URL
BACKEND_URL=$(gcloud run services describe railgpt-backend --platform managed --region $REGION --format 'value(status.url)')
echo -e "${GREEN}Backend deployed successfully at: $BACKEND_URL${NC}"

cd ..

echo -e "\n${BLUE}Step 3: Building and Deploying Frontend${NC}"

# Update frontend environment variables
echo -e "${BLUE}Updating frontend configuration...${NC}"
cat > frontend/.env.production << EOF
REACT_APP_API_URL=$BACKEND_URL
REACT_APP_SUPABASE_URL=$SUPABASE_URL
REACT_APP_SUPABASE_ANON_KEY=$SUPABASE_ANON_KEY
EOF

# Build frontend
cd frontend
echo -e "${BLUE}Installing frontend dependencies...${NC}"
npm install

echo -e "${BLUE}Building frontend...${NC}"
npm run build

# Create bucket for frontend hosting
BUCKET_NAME="$PROJECT_ID-railgpt-frontend"
echo -e "${BLUE}Creating storage bucket: $BUCKET_NAME${NC}"
gsutil mb -p $PROJECT_ID gs://$BUCKET_NAME || true

# Configure bucket for website hosting
gsutil web set -m index.html -e index.html gs://$BUCKET_NAME

# Make bucket public
gsutil iam ch allUsers:objectViewer gs://$BUCKET_NAME

# Upload frontend files
echo -e "${BLUE}Uploading frontend files...${NC}"
gsutil -m rsync -r -d build/ gs://$BUCKET_NAME

# Set cache control headers
gsutil -m setmeta -h "Cache-Control:public, max-age=31536000" gs://$BUCKET_NAME/static/**
gsutil -m setmeta -h "Cache-Control:public, max-age=0" gs://$BUCKET_NAME/index.html

FRONTEND_URL="https://storage.googleapis.com/$BUCKET_NAME/index.html"
echo -e "${GREEN}Frontend deployed successfully at: $FRONTEND_URL${NC}"

cd ..

echo -e "\n${BLUE}Step 4: Setting up Load Balancer (Optional)${NC}"

if [ ! -z "$DOMAIN" ]; then
    echo -e "${BLUE}Setting up load balancer for custom domain: $DOMAIN${NC}"
    
    # Create global IP address
    gcloud compute addresses create railgpt-ip --global || true
    
    # Get the IP address
    STATIC_IP=$(gcloud compute addresses describe railgpt-ip --global --format="value(address)")
    
    echo -e "${YELLOW}Please configure your DNS to point $DOMAIN to IP: $STATIC_IP${NC}"
    echo -e "${YELLOW}Then run the following command to complete SSL setup:${NC}"
    echo -e "gcloud compute ssl-certificates create railgpt-ssl --domains=$DOMAIN"
    
    # Create backend service for frontend
    gcloud compute backend-buckets create railgpt-frontend-backend \
        --gcs-bucket-name=$BUCKET_NAME || true
    
    # Create URL map
    gcloud compute url-maps create railgpt-url-map \
        --default-backend-bucket=railgpt-frontend-backend || true
    
    # Create HTTP(S) proxy
    gcloud compute target-https-proxies create railgpt-https-proxy \
        --url-map=railgpt-url-map \
        --ssl-certificates=railgpt-ssl || true
    
    # Create forwarding rule
    gcloud compute forwarding-rules create railgpt-https-rule \
        --address=railgpt-ip \
        --global \
        --target-https-proxy=railgpt-https-proxy \
        --ports=443 || true
    
    echo -e "${GREEN}Load balancer configured. Your site will be available at: https://$DOMAIN${NC}"
else
    echo -e "${YELLOW}No custom domain configured. Skipping load balancer setup.${NC}"
fi

echo -e "\n${BLUE}Step 5: Setting up Monitoring${NC}"

# Create uptime check
gcloud alpha monitoring uptime create \
    --display-name="RailGPT Backend Health Check" \
    --hostname=$(echo $BACKEND_URL | sed 's|https://||') \
    --path=/health \
    --timeout=10s \
    --period=60s || true

echo -e "\n${GREEN}===== DEPLOYMENT COMPLETED SUCCESSFULLY! =====${NC}"
echo
echo -e "${BLUE}Your RailGPT application is now deployed:${NC}"
echo -e "  🚀 Backend API: $BACKEND_URL"
echo -e "  🌐 Frontend: $FRONTEND_URL"
if [ ! -z "$DOMAIN" ]; then
    echo -e "  🔗 Custom Domain: https://$DOMAIN (after DNS configuration)"
fi
echo
echo -e "${BLUE}Next Steps:${NC}"
echo -e "  1. Test your application by visiting the frontend URL"
echo -e "  2. Upload some documents to test the functionality"
echo -e "  3. Add websites for content extraction"
echo -e "  4. Monitor your application in the Google Cloud Console"
echo
echo -e "${BLUE}Useful Commands:${NC}"
echo -e "  📊 View logs: gcloud logs read --service=railgpt-backend --limit=50"
echo -e "  🔄 Update backend: gcloud run deploy railgpt-backend --image gcr.io/$PROJECT_ID/railgpt-backend --region $REGION"
echo -e "  📁 Update frontend: gsutil -m rsync -r -d frontend/build/ gs://$BUCKET_NAME"
echo
echo -e "${GREEN}Deployment completed successfully! 🎉${NC}"
