-- Create category_change_logs table for audit trail
CREATE TABLE IF NOT EXISTS category_change_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    website_id UUID REFERENCES websites(id) ON DELETE CASCADE,
    old_main_category TEXT,
    old_category TEXT,
    old_sub_category TEXT,
    old_minor_category TEXT,
    new_main_category TEXT,
    new_category TEXT,
    new_sub_category TEXT,
    new_minor_category TEXT,
    changed_by TEXT NOT NULL DEFAULT 'system',
    change_reason TEXT,
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure either document_id or website_id is set, but not both
    CONSTRAINT check_entity_type CHECK (
        (document_id IS NOT NULL AND website_id IS NULL) OR
        (document_id IS NULL AND website_id IS NOT NULL)
    )
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_category_change_logs_document_id ON category_change_logs(document_id);
CREATE INDEX IF NOT EXISTS idx_category_change_logs_website_id ON category_change_logs(website_id);
CREATE INDEX IF NOT EXISTS idx_category_change_logs_changed_at ON category_change_logs(changed_at DESC);
CREATE INDEX IF NOT EXISTS idx_category_change_logs_changed_by ON category_change_logs(changed_by);

-- Add comments for documentation
COMMENT ON TABLE category_change_logs IS 'Audit trail for category changes on documents and websites';
COMMENT ON COLUMN category_change_logs.document_id IS 'Reference to documents table (mutually exclusive with website_id)';
COMMENT ON COLUMN category_change_logs.website_id IS 'Reference to websites table (mutually exclusive with document_id)';
COMMENT ON COLUMN category_change_logs.old_main_category IS 'Previous main category value';
COMMENT ON COLUMN category_change_logs.old_category IS 'Previous category value';
COMMENT ON COLUMN category_change_logs.old_sub_category IS 'Previous sub category value';
COMMENT ON COLUMN category_change_logs.old_minor_category IS 'Previous minor category value';
COMMENT ON COLUMN category_change_logs.new_main_category IS 'New main category value';
COMMENT ON COLUMN category_change_logs.new_category IS 'New category value';
COMMENT ON COLUMN category_change_logs.new_sub_category IS 'New sub category value';
COMMENT ON COLUMN category_change_logs.new_minor_category IS 'New minor category value';
COMMENT ON COLUMN category_change_logs.changed_by IS 'User or system that made the change';
COMMENT ON COLUMN category_change_logs.change_reason IS 'Optional reason for the category change';
COMMENT ON COLUMN category_change_logs.changed_at IS 'Timestamp when the change was made';

-- Enable Row Level Security (optional, for multi-tenant scenarios)
-- ALTER TABLE category_change_logs ENABLE ROW LEVEL SECURITY; 