from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Any, Optional
import uuid
import time
from datetime import datetime
from supabase import create_client, Client
import os

from .config import get_supabase_client
from .documents import get_document_by_id
from .llm import generate_response

router = APIRouter()

# Models
class Message(BaseModel):
    role: str
    content: str

class ChatRequest(BaseModel):
    query: str
    session_id: Optional[str] = None
    history: Optional[List[Message]] = []

class Source(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    document_id: Optional[str] = None
    website_id: Optional[str] = None
    filename: Optional[str] = None
    url: Optional[str] = None
    page_numbers: Optional[List[int]] = None
    chunk_indices: Optional[List[int]] = None
    relevance_score: Optional[float] = None

class ChatResponse(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    session_id: Optional[str] = None
    content: str
    document_answer: Optional[str] = None
    website_answer: Optional[str] = None
    sources: Optional[List[Source]] = []
    document_sources: Optional[List[Source]] = []
    website_sources: Optional[List[Source]] = []
    processing_time: float = 0

@router.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest, supabase: Client = Depends(get_supabase_client)):
    start_time = time.time()

    # Generate a session ID if not provided
    session_id = request.session_id or str(uuid.uuid4())

    try:
        # Process the query to get a response
        response_data = await generate_response(request.query, request.history)

        # Process document sources to ensure they have complete information
        document_sources = []
        if response_data.get("document_sources"):
            for source in response_data["document_sources"]:
                # Ensure we have the document details
                if source.get("document_id"):
                    try:
                        # Get the document details to ensure we have the filename
                        doc = await get_document_by_id(source["document_id"], supabase)
                        if doc:
                            source["filename"] = doc.get("filename") or doc.get("display_name") or "Document"
                    except Exception as e:
                        print(f"Error getting document details: {e}")
                document_sources.append(source)

        # Format the response
        chat_response = ChatResponse(
            session_id=session_id,
            content=response_data.get("answer", ""),
            document_answer=response_data.get("document_answer", ""),
            website_answer=response_data.get("website_answer", ""),
            sources=response_data.get("sources", []),
            document_sources=document_sources,
            website_sources=response_data.get("website_sources", []),
            processing_time=time.time() - start_time
        )

        # Save the message in the chat session
        try:
            # First check if session exists
            session_response = supabase.table("chat_sessions").select("*").eq("id", session_id).execute()

            if len(session_response.data) == 0:
                # Create a new session
                supabase.table("chat_sessions").insert({
                    "id": session_id,
                    "title": f"Chat {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                    "messages": [
                        {"role": "user", "content": request.query},
                        {
                            "role": "assistant", 
                            "content": chat_response.content,
                            "document_answer": chat_response.document_answer,
                            "website_answer": chat_response.website_answer,
                            "sources": chat_response.sources,
                            "document_sources": chat_response.document_sources,
                            "website_sources": chat_response.website_sources
                        }
                    ],
                    "updated_at": datetime.now().isoformat()
                }).execute()
            else:
                # Get existing messages
                existing_messages = session_response.data[0].get("messages", [])

                # Add new messages
                existing_messages.append({"role": "user", "content": request.query})
                existing_messages.append({
                    "role": "assistant", 
                    "content": chat_response.content,
                    "document_answer": chat_response.document_answer,
                    "website_answer": chat_response.website_answer,
                    "sources": chat_response.sources,
                    "document_sources": chat_response.document_sources,
                    "website_sources": chat_response.website_sources
                })

                # Update the session
                supabase.table("chat_sessions").update({
                    "messages": existing_messages,
                    "updated_at": datetime.now().isoformat()
                }).eq("id", session_id).execute()

        except Exception as e:
            print(f"Error saving chat message: {e}")

        return chat_response

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing chat request: {str(e)}")

@router.get("/chat/{session_id}", response_model=Dict[str, Any])
async def get_chat_session(session_id: str, supabase: Client = Depends(get_supabase_client)):
    try:
        response = supabase.table("chat_sessions").select("*").eq("id", session_id).execute()

        if not response.data or len(response.data) == 0:
            raise HTTPException(status_code=404, detail="Chat session not found")

        return response.data[0]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving chat session: {str(e)}")
