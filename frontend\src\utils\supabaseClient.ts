import { createClient } from '@supabase/supabase-js';

// Get environment variables with fallbacks
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://rkllidjktazafeinezgo.supabase.co';
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA';

// Create and export the Supabase client
const supabaseClient = createClient(supabaseUrl, supabaseAnonKey);

// Add connection health check
export const checkSupabaseConnection = async () => {
  try {
    // Try to fetch a single row from a known table
    const { data, error } = await supabaseClient
      .from('documents')
      .select('id')
      .limit(1);

    if (error) {
      console.error('Supabase connection error:', error.message);
      return false;
    }

    console.log('Supabase connection successful');
    return true;
  } catch (err) {
    console.error('Supabase connection check failed:', err);
    return false;
  }
};

export default supabaseClient;
