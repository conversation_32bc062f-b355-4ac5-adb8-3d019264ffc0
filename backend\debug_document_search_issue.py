import os
import sys
import logging
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add backend directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def diagnose_document_search():
    """Comprehensive diagnosis of document search issues."""
    
    print("=" * 60)
    print("🔍 DIAGNOSING DOCUMENT SEARCH ISSUES")
    print("=" * 60)
    
    # 1. Check environment variables
    print("\n1. 🔧 CHECKING ENVIRONMENT VARIABLES:")
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    supabase_anon_key = os.getenv("SUPABASE_ANON_KEY")
    gemini_key = os.getenv("GEMINI_API_KEY")
    
    print(f"   SUPABASE_URL: {'✅ Set' if supabase_url else '❌ Missing'}")
    print(f"   SUPABASE_KEY: {'✅ Set' if supabase_key else '❌ Missing'}")
    print(f"   SUPABASE_ANON_KEY: {'✅ Set' if supabase_anon_key else '❌ Missing'}")
    print(f"   GEMINI_API_KEY: {'✅ Set' if gemini_key else '❌ Missing'}")
    
    if not all([supabase_url, supabase_key, gemini_key]):
        print("❌ CRITICAL: Missing required environment variables!")
        return
    
    # 2. Test Supabase connection
    print("\n2. 🔗 TESTING SUPABASE CONNECTION:")
    try:
        from supabase_client import supabase
        
        # Test basic connection
        result = supabase.table("documents").select("id").limit(1).execute()
        print(f"   Basic connection: ✅ Success")
        print(f"   Documents table accessible: ✅ Yes")
        
        # Count documents
        doc_count = supabase.table("documents").select("id", count="exact").execute()
        total_docs = doc_count.count if hasattr(doc_count, 'count') else len(doc_count.data)
        print(f"   Total documents in DB: {total_docs}")
        
        # Count chunks
        chunk_count = supabase.table("document_chunks").select("id", count="exact").execute()
        total_chunks = chunk_count.count if hasattr(chunk_count, 'count') else len(chunk_count.data)
        print(f"   Total document chunks in DB: {total_chunks}")
        
    except Exception as e:
        print(f"   ❌ Supabase connection failed: {str(e)}")
        return
    
    # 3. Test embedding generation
    print("\n3. 🧠 TESTING EMBEDDING GENERATION:")
    try:
        import llm_router
        
        test_text = "What is Indian Railway?"
        embedding = llm_router.generate_embedding(test_text)
        print(f"   Embedding generation: ✅ Success")
        print(f"   Embedding dimension: {len(embedding) if embedding else 'None'}")
        print(f"   Sample values: {embedding[:5] if embedding and len(embedding) >= 5 else 'None'}")
        
    except Exception as e:
        print(f"   ❌ Embedding generation failed: {str(e)}")
        return
    
    # 4. Test document search functions
    print("\n4. 📄 TESTING DOCUMENT SEARCH FUNCTIONS:")
    
    # Test search_supabase_document_chunks
    try:
        from server import search_supabase_document_chunks, generate_embedding
        
        query = "Indian Railway"
        query_embedding = generate_embedding(query)
        
        print(f"   Testing query: '{query}'")
        
        # Test Supabase document search
        doc_chunks = search_supabase_document_chunks(
            query_embedding=query_embedding,
            query_text=query,
            use_hybrid_search=True,
            top_k=5,
            min_threshold=0.1  # Lower threshold for testing
        )
        print(f"   Supabase document search: {'✅ Found ' + str(len(doc_chunks)) + ' chunks' if doc_chunks else '❌ No results'}")
        
        if doc_chunks:
            for i, chunk in enumerate(doc_chunks[:3]):
                sim = chunk.get('similarity', 0)
                filename = chunk.get('filename', 'Unknown')
                print(f"     {i+1}. {filename} (similarity: {sim:.3f})")
        
    except Exception as e:
        print(f"   ❌ Document search function failed: {str(e)}")
    
    # 5. Test direct content search
    print("\n5. 🔍 TESTING DIRECT CONTENT SEARCH:")
    try:
        from server import search_documents_by_content
        
        direct_results = search_documents_by_content("Indian Railway", limit=5)
        print(f"   Direct content search: {'✅ Found ' + str(len(direct_results)) + ' results' if direct_results else '❌ No results'}")
        
        if direct_results:
            for i, result in enumerate(direct_results[:3]):
                filename = result.get('filename', 'Unknown')
                text_preview = result.get('text', '')[:100] + '...' if len(result.get('text', '')) > 100 else result.get('text', '')
                print(f"     {i+1}. {filename}")
                print(f"        Text: {text_preview}")
        
    except Exception as e:
        print(f"   ❌ Direct content search failed: {str(e)}")
    
    # 6. Check global DOCUMENT_CHUNKS
    print("\n6. 💾 CHECKING IN-MEMORY DOCUMENT CHUNKS:")
    try:
        from server import DOCUMENT_CHUNKS
        
        print(f"   In-memory chunks count: {len(DOCUMENT_CHUNKS)}")
        
        document_chunks = [c for c in DOCUMENT_CHUNKS if c.get('source_type') == 'document']
        website_chunks = [c for c in DOCUMENT_CHUNKS if c.get('source_type') == 'website']
        
        print(f"   Document chunks: {len(document_chunks)}")
        print(f"   Website chunks: {len(website_chunks)}")
        
        # Check embedding availability
        chunks_with_embeddings = [c for c in document_chunks if c.get('embedding')]
        print(f"   Document chunks with embeddings: {len(chunks_with_embeddings)}")
        
        if document_chunks:
            print(f"   Sample document chunk files:")
            for i, chunk in enumerate(document_chunks[:3]):
                filename = chunk.get('filename', chunk.get('file_name', 'Unknown'))
                has_embedding = bool(chunk.get('embedding'))
                print(f"     {i+1}. {filename} (embedding: {'✅' if has_embedding else '❌'})")
        
    except Exception as e:
        print(f"   ❌ Failed to check in-memory chunks: {str(e)}")
    
    # 7. Test local document search
    print("\n7. 💻 TESTING LOCAL DOCUMENT SEARCH:")
    try:
        from server import local_document_search, generate_embedding
        
        query = "Indian Railway"
        query_embedding = generate_embedding(query)
        
        local_results = local_document_search(
            query_embedding=query_embedding,
            query_text=query,
            top_k=5,
            min_threshold=0.01  # Very low threshold
        )
        
        print(f"   Local document search: {'✅ Found ' + str(len(local_results)) + ' results' if local_results else '❌ No results'}")
        
        if local_results:
            for i, result in enumerate(local_results[:3]):
                sim = result.get('similarity', 0)
                filename = result.get('filename', 'Unknown')
                print(f"     {i+1}. {filename} (similarity: {sim:.3f})")
        
    except Exception as e:
        print(f"   ❌ Local document search failed: {str(e)}")
    
    # 8. Summary and recommendations
    print("\n8. 📝 SUMMARY AND RECOMMENDATIONS:")
    print("   If documents are not found in queries:")
    print("   - Check if documents are properly uploaded to Supabase")
    print("   - Verify document chunks have valid embeddings")
    print("   - Lower similarity thresholds for testing")
    print("   - Check if RPC functions exist in Supabase")
    print("   - Ensure proper table permissions")
    
    print("\n" + "=" * 60)
    print("✅ DIAGNOSIS COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(diagnose_document_search()) 