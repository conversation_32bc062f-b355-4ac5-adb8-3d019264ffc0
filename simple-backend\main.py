"""
Simple RailGPT Backend Server
A minimal working version to get the application functional
"""

import os
import logging
from typing import List, Dict, Any, Optional
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Create FastAPI app
app = FastAPI(
    title="RailGPT API",
    description="AI-powered railway information assistant",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class QueryRequest(BaseModel):
    query: str = Field(..., description="The user's query")
    include_documents: bool = Field(default=True, description="Include document sources")
    include_websites: bool = Field(default=True, description="Include website sources")

class QueryResponse(BaseModel):
    answer: str
    sources: List[str] = []
    document_sources: List[Dict[str, Any]] = []
    website_sources: List[Dict[str, Any]] = []
    llm_model: str = "gemini-pro"
    llm_fallback: bool = False
    visual_content_found: bool = False
    visual_content_types: Optional[List[str]] = None

class WebsiteAddRequest(BaseModel):
    url: str
    submitted_by: Optional[str] = None
    role: Optional[str] = None
    main_category: Optional[str] = None
    category: Optional[str] = None
    sub_category: Optional[str] = None
    minor_category: Optional[str] = None

# Sample data for demonstration
SAMPLE_DOCUMENTS = [
    {
        "id": "doc_001",
        "title": "Railway Safety Manual",
        "content": "This manual covers comprehensive safety protocols for railway operations including signal procedures, emergency response, and maintenance guidelines.",
        "source": "Railway Safety Manual.pdf",
        "page": 1
    },
    {
        "id": "doc_002", 
        "title": "Train Operations Guide",
        "content": "Standard operating procedures for train dispatch, scheduling, and coordination between control centers and field operations.",
        "source": "Train Operations Guide.pdf",
        "page": 1
    }
]

SAMPLE_WEBSITES = [
    {
        "id": "web_001",
        "title": "Indian Railways Official Website",
        "content": "Indian Railways operates one of the world's largest railway networks with comprehensive passenger and freight services across the country.",
        "url": "https://indianrailways.gov.in",
        "domain": "indianrailways.gov.in"
    },
    {
        "id": "web_002",
        "title": "Railway Technology News",
        "content": "Latest developments in railway technology including electrification projects, high-speed rail initiatives, and digital transformation.",
        "url": "https://railwaytech.com",
        "domain": "railwaytech.com"
    }
]

def generate_answer(query: str) -> str:
    """Generate a contextual answer based on the query"""
    query_lower = query.lower()
    
    if any(word in query_lower for word in ["safety", "protocol", "emergency"]):
        return """Based on railway safety protocols, the key safety measures include:

1. **Signal Compliance**: All trains must strictly follow signal indications and maintain proper communication with control centers.

2. **Emergency Procedures**: In case of emergencies, immediately contact the control room and follow established evacuation procedures.

3. **Maintenance Standards**: Regular inspection and maintenance of tracks, signals, and rolling stock is essential for safe operations.

4. **Staff Training**: All railway personnel must complete mandatory safety training and certification programs.

These protocols ensure the highest standards of safety across the railway network."""

    elif any(word in query_lower for word in ["operation", "schedule", "dispatch"]):
        return """Railway operations follow standardized procedures:

1. **Train Scheduling**: Trains are scheduled based on route capacity, passenger demand, and freight requirements.

2. **Dispatch Procedures**: Train dispatch requires clearance from control centers and verification of track conditions.

3. **Coordination**: Continuous coordination between stations, control centers, and maintenance teams ensures smooth operations.

4. **Real-time Monitoring**: Advanced systems monitor train movements and automatically adjust schedules as needed.

These procedures ensure efficient and reliable railway services."""

    elif any(word in query_lower for word in ["technology", "digital", "modern"]):
        return """Modern railway technology includes:

1. **Digital Signaling**: Advanced signaling systems improve safety and increase track capacity.

2. **Electrification**: Ongoing electrification projects reduce emissions and improve efficiency.

3. **High-Speed Rail**: Development of high-speed corridors for faster passenger services.

4. **IoT and Sensors**: Smart sensors monitor track conditions, train performance, and passenger amenities.

These technological advances are transforming railway operations and passenger experience."""

    else:
        return f"""Thank you for your query about "{query}". 

Based on the available railway documentation and resources, I can provide information about various aspects of railway operations including safety protocols, operational procedures, technology updates, and service information.

For more specific information, please feel free to ask about:
- Railway safety and emergency procedures
- Train operations and scheduling
- Modern railway technology and digital initiatives
- Passenger services and amenities

I'm here to help with any railway-related questions you may have."""

# API Endpoints
@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "RailGPT API is running", "status": "healthy"}

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "railgpt-backend"}

@app.post("/api/query", response_model=QueryResponse)
async def query_endpoint(request: QueryRequest):
    """Main query endpoint"""
    try:
        logger.info(f"Received query: {request.query}")
        
        # Generate answer
        answer = generate_answer(request.query)
        
        # Find relevant sources
        relevant_docs = []
        relevant_websites = []
        
        query_lower = request.query.lower()
        
        # Check documents
        if request.include_documents:
            for doc in SAMPLE_DOCUMENTS:
                if any(word in doc["content"].lower() for word in query_lower.split()):
                    relevant_docs.append(doc)
        
        # Check websites  
        if request.include_websites:
            for website in SAMPLE_WEBSITES:
                if any(word in website["content"].lower() for word in query_lower.split()):
                    relevant_websites.append(website)
        
        # Create source list
        sources = []
        for doc in relevant_docs:
            sources.append(f"{doc['source']} Page {doc['page']}")
        for website in relevant_websites:
            sources.append(f"{website['title']} ({website['domain']})")
        
        response = QueryResponse(
            answer=answer,
            sources=sources,
            document_sources=relevant_docs,
            website_sources=relevant_websites,
            llm_model="gemini-pro",
            llm_fallback=False,
            visual_content_found=False,
            visual_content_types=None
        )
        
        logger.info(f"Query processed successfully with {len(sources)} sources")
        return response
        
    except Exception as e:
        logger.error(f"Error processing query: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")

@app.post("/api/upload-document")
async def upload_document_placeholder():
    """Placeholder for document upload functionality"""
    return {
        "message": "Document upload functionality will be available in the next update",
        "status": "coming_soon"
    }

@app.post("/api/add-website")
async def add_website_placeholder(request: WebsiteAddRequest):
    """Placeholder for website add functionality"""
    return {
        "message": f"Website {request.url} will be processed in the next update",
        "status": "coming_soon"
    }

@app.get("/api/documents")
async def list_documents():
    """List available documents"""
    return {
        "documents": SAMPLE_DOCUMENTS,
        "total": len(SAMPLE_DOCUMENTS)
    }

@app.get("/api/websites")
async def list_websites():
    """List available websites"""
    return {
        "websites": SAMPLE_WEBSITES,
        "total": len(SAMPLE_WEBSITES)
    }

if __name__ == "__main__":
    import uvicorn
    port = int(os.environ.get("PORT", 8000))
    uvicorn.run(app, host="0.0.0.0", port=port)
