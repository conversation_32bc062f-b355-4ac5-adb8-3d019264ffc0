with open('server.py', 'r', encoding='utf-8') as f:
    content = f.read()

# Replace the problematic section
old_pattern = '''            if document_chunks:
                logger.info(f"✅ STEP 1: Found {len(document_chunks)} relevant document chunks")
        except Exception as e:
            logger.error(f"Error searching document chunks: {str(e)}")
            document_chunks = []

        except Exception as e:
            logger.error(f"Error searching document chunks: {str(e)}")
            document_chunks = []

                logger.info(f"✅ STEP 1: Found {len(document_chunks)} relevant document chunks")'''

new_pattern = '''            if document_chunks:
                logger.info(f"✅ STEP 1: Found {len(document_chunks)} relevant document chunks")'''

content = content.replace(old_pattern, new_pattern)

with open('server.py', 'w', encoding='utf-8') as f:
    f.write(content)

print("Fixed server.py syntax error") 