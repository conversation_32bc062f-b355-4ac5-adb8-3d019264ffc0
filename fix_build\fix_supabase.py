#!/usr/bin/env python3
"""
Simple fix script for RailGPT Supabase client error
"""
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def update_supabase_client():
    """Update the supabase_client.py file with the fix"""
    supabase_client_path = os.path.join('backend', 'supabase_client.py')
    
    if not os.path.exists(supabase_client_path):
        logger.error(f"Could not find {supabase_client_path}")
        return False
    
    # Read the current content
    with open(supabase_client_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Create backup
    backup_path = f"{supabase_client_path}.bak2"
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    logger.info(f"Created backup at {backup_path}")
    
    # Find the position to insert the fix
    init_pos = content.find("def __init__")
    client_init_pos = content.find("self.supabase = create_client(self.url, self.key)", init_pos)
    
    if client_init_pos == -1:
        logger.error("Could not find Supabase client initialization code")
        return False
    
    # Split the content
    before = content[:client_init_pos]
    after = content[client_init_pos:]
    
    # Replace the initialization code
    after = after.replace(
        "self.supabase = create_client(self.url, self.key)",
        """try:
            # First try the standard way (compatible with supabase>=2.0.0)
            self.supabase = create_client(self.url, self.key)
        except TypeError as e:
            # Handle version compatibility issues
            if 'proxy' in str(e):
                # If we get a proxy error, try creating with options parameter
                import importlib
                logger.info("Using compatibility mode for Supabase client")
                from supabase._sync.client import SyncClient
                self.supabase = SyncClient.create(self.url, self.key, {})
            else:
                # Re-raise if it's a different error
                raise"""
    )
    
    # Combine and write back
    updated_content = before + after
    with open(supabase_client_path, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    logger.info(f"Updated {supabase_client_path}")
    return True

def main():
    print("\n===================================")
    print("RailGPT Supabase Client Fix Tool")
    print("===================================\n")
    
    success = update_supabase_client()
    
    if success:
        print("\n✅ Successfully updated the Supabase client initialization code!")
        print("\nNext steps to deploy the fix:")
        print("1. Build a new Docker image with the fixed code:")
        print("   gcloud builds submit --tag gcr.io/railchatbot-cb553/railgpt-backend:fixed ./backend")
        print("\n2. Update your Cloud Run service to use the new image:")
        print("   gcloud run services update railgpt-backend --project railchatbot-cb553 --region us-central1 --image gcr.io/railchatbot-cb553/railgpt-backend:fixed")
    else:
        print("\n❌ Failed to update the Supabase client initialization code.")

if __name__ == "__main__":
    main()
