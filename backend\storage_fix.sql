-- Supabase Storage Fix for IR App
-- Run this in your Supabase SQL Editor to fix RLS policy issues

-- 1. Ensure documents bucket exists with proper settings
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'documents',
  'documents',
  true,
  52428800,  -- 50MB
  ARRAY[
    'application/pdf',
    'application/msword', 
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/png',
    'image/jpeg',
    'image/gif'
  ]
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 52428800;

-- 2. Drop existing restrictive policies
DROP POLICY IF EXISTS "Allow authenticated uploads to documents bucket" ON storage.objects;
DROP POLICY IF EXISTS "Allow public downloads from documents bucket" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated updates to documents bucket" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated deletions from documents bucket" ON storage.objects;
DROP POLICY IF EXISTS "Allow anonymous uploads to documents bucket" ON storage.objects;

-- 3. Enable RLS
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- 4. Create permissive policies for uploads and access
CREATE POLICY "Public upload to documents" ON storage.objects
FOR INSERT WITH CHECK (bucket_id = 'documents');

CREATE POLICY "Public read from documents" ON storage.objects  
FOR SELECT USING (bucket_id = 'documents');

CREATE POLICY "Public update documents" ON storage.objects
FOR UPDATE USING (bucket_id = 'documents');

CREATE POLICY "Public delete documents" ON storage.objects
FOR DELETE USING (bucket_id = 'documents');

-- 5. Test the setup
SELECT 'Storage policies created successfully' as status;
