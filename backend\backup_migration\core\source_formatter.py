"""
Source Formatter Module for RailGPT
Handles clean source attribution with deduplication and proper formatting
"""

import logging
import os
from typing import Dict, List, Any, Set
from collections import defaultdict

logger = logging.getLogger(__name__)

class SourceFormatter:
    """
    Formats sources for clean attribution in responses
    """
    
    def __init__(self):
        self.document_viewer_base = "/viewer"
    
    def format_sources_for_response(
        self, 
        chunks: List[Dict[str, Any]], 
        source_type: str
    ) -> List[Dict[str, Any]]:
        """
        Format chunks into clean, deduplicated sources
        """
        try:
            if source_type == "document":
                return self._format_document_sources(chunks)
            elif source_type == "website":
                return self._format_website_sources(chunks)
            else:
                logger.warning(f"Unknown source type: {source_type}")
                return []
                
        except Exception as e:
            logger.error(f"❌ Source formatting error: {str(e)}")
            return []
    
    def _format_document_sources(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Format document chunks into clean sources with page aggregation
        """
        # Group chunks by filename and content type
        source_groups = defaultdict(lambda: {
            'pages': set(),
            'content_types': set(),
            'visual_content': None,
            'filename': '',
            'name': ''
        })
        
        for chunk in chunks:
            filename = chunk.get("filename") or chunk.get("title") or "Unknown.pdf"
            metadata = chunk.get("metadata", {})
            content_type = metadata.get("content_type", "text")
            page = chunk.get("page") or chunk.get("page_number", 1)
            
            # Use filename + content_type as key for visual content
            key = f"{filename}_{content_type}" if content_type != "text" else filename
            
            group = source_groups[key]
            group['pages'].add(page)
            group['content_types'].add(content_type)
            group['filename'] = filename
            group['name'] = os.path.basename(filename)
            
            # Handle visual content
            if content_type != "text" and not group['visual_content']:
                group['visual_content'] = self._format_visual_content(metadata, content_type)
        
        # Convert to source list
        sources = []
        for key, group in source_groups.items():
            pages_list = sorted(list(group['pages']))
            primary_content_type = "text" if "text" in group['content_types'] else list(group['content_types'])[0]
            
            # Create display text
            if len(pages_list) == 1:
                page_text = f"Page {pages_list[0]}"
            else:
                page_text = f"Pages {', '.join(map(str, pages_list))}"
            
            source = {
                "source_type": "document",
                "filename": group['filename'],
                "name": group['name'],
                "page": pages_list[0],  # Primary page for linking
                "pages": pages_list,
                "link": f"{self.document_viewer_base}?file={group['filename']}&page={pages_list[0]}",
                "display_text": f"{group['name']} – {page_text}",
                "content_type": primary_content_type,
                "visual_content": group['visual_content']
            }
            
            sources.append(source)
        
        # Sort sources by filename and page
        sources.sort(key=lambda x: (x['filename'], x['page']))
        return sources
    
    def _format_website_sources(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Format website chunks into clean sources with URL deduplication
        """
        # Group chunks by URL
        url_groups = defaultdict(lambda: {
            'titles': set(),
            'domains': set(),
            'url': ''
        })
        
        for chunk in chunks:
            url = chunk.get("url") or "Unknown website"
            title = chunk.get("title", "")
            domain = chunk.get("domain", "")
            
            group = url_groups[url]
            group['url'] = url
            if title:
                group['titles'].add(title)
            if domain:
                group['domains'].add(domain)
        
        # Convert to source list
        sources = []
        for url, group in url_groups.items():
            # Choose best title
            if group['titles']:
                title = max(group['titles'], key=len)  # Use longest title
            else:
                title = group['url']
            
            source = {
                "source_type": "website",
                "url": group['url'],
                "title": title,
                "display_text": title
            }
            
            sources.append(source)
        
        # Sort sources by title
        sources.sort(key=lambda x: x['title'])
        return sources
    
    def _format_visual_content(self, metadata: Dict[str, Any], content_type: str) -> Dict[str, Any]:
        """
        Format visual content metadata for frontend
        """
        visual_content = {
            "type": content_type,
            "display_type": self._get_display_type(content_type)
        }
        
        # Add type-specific metadata
        if content_type == "table":
            visual_content.update({
                "table_data": metadata.get("table_data"),
                "table_html": metadata.get("table_html")
            })
        elif content_type == "image":
            visual_content.update({
                "image_url": metadata.get("image_url"),
                "image_caption": metadata.get("image_caption"),
                "image_base64": metadata.get("image_base64")
            })
        elif content_type == "chart_diagram":
            visual_content.update({
                "chart_type": metadata.get("chart_type"),
                "chart_data": metadata.get("chart_data"),
                "chart_image": metadata.get("chart_image")
            })
        
        return visual_content
    
    def _get_display_type(self, content_type: str) -> str:
        """
        Get display type for frontend rendering
        """
        display_mapping = {
            "text": "text",
            "table": "html_table",
            "image": "image",
            "chart_diagram": "image"
        }
        return display_mapping.get(content_type, "text")
    
    def deduplicate_sources(self, sources: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Remove duplicate sources while preserving order
        """
        seen = set()
        unique_sources = []
        
        for source in sources:
            # Create unique key based on source type
            if source.get("source_type") == "document":
                key = f"doc_{source.get('filename')}_{source.get('content_type', 'text')}"
            else:
                key = f"web_{source.get('url')}"
            
            if key not in seen:
                seen.add(key)
                unique_sources.append(source)
        
        return unique_sources

# Global source formatter instance
source_formatter = SourceFormatter()

# Convenience functions for backward compatibility
def format_sources_for_response(
    chunks: List[Dict[str, Any]], 
    source_type: str
) -> List[Dict[str, Any]]:
    """Format sources for response"""
    return source_formatter.format_sources_for_response(chunks, source_type)

def format_visual_content_for_frontend(
    metadata: Dict[str, Any], 
    content_type: str
) -> Dict[str, Any]:
    """Format visual content for frontend"""
    return source_formatter._format_visual_content(metadata, content_type) 