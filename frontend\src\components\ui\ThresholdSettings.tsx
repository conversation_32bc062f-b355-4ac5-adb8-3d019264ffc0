import React, { useState } from 'react';

interface ThresholdSettings {
  document_threshold: number;
  website_threshold: number;
}

const ThresholdSettings: React.FC = () => {
  const [settings, setSettings] = useState<ThresholdSettings>({
    document_threshold: 0.1,
    website_threshold: 0.05
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const updateSettings = async () => {
    setLoading(true);
    setMessage('');
    
    try {
      // For now, store in localStorage (can be enhanced to use API later)
      localStorage.setItem('railgpt_thresholds', JSON.stringify(settings));
      setMessage('✅ Thresholds updated! Changes will take effect on next query.');
    } catch (error) {
      setMessage('❌ Error updating thresholds: ' + error);
    } finally {
      setLoading(false);
    }
  };

  const resetToDefaults = () => {
    setSettings({
      document_threshold: 0.1,
      website_threshold: 0.05
    });
    setMessage('🔄 Reset to default values');
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold mb-4">🎛️ Similarity Thresholds</h3>
      
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium mb-2">
            Document Search Threshold: {settings.document_threshold.toFixed(2)}
          </label>
          <input
            type="range"
            min="0"
            max="0.5"
            step="0.01"
            value={settings.document_threshold}
            onChange={(e) => setSettings({...settings, document_threshold: parseFloat(e.target.value)})}
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            Website Search Threshold: {settings.website_threshold.toFixed(2)}
          </label>
          <input
            type="range"
            min="0"
            max="0.3"
            step="0.01"
            value={settings.website_threshold}
            onChange={(e) => setSettings({...settings, website_threshold: parseFloat(e.target.value)})}
            className="w-full"
          />
        </div>

        <div className="flex gap-4">
          <button
            onClick={updateSettings}
            disabled={loading}
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
          >
            {loading ? 'Updating...' : 'Update Thresholds'}
          </button>

          <button
            onClick={resetToDefaults}
            className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
          >
            Reset Defaults
          </button>
        </div>

        {message && (
          <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-700 rounded">
            {message}
          </div>
        )}
      </div>

      <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded">
        <h4 className="font-medium text-yellow-800 dark:text-yellow-200">💡 Quick Guide:</h4>
        <ul className="text-sm text-yellow-700 dark:text-yellow-300 mt-2 space-y-1">
          <li>• <strong>Lower values:</strong> More inclusive (more results)</li>
          <li>• <strong>Higher values:</strong> Stricter matching (fewer results)</li>
          <li>• If always getting LLM fallback, try lowering thresholds</li>
        </ul>
      </div>
    </div>
  );
};

export default ThresholdSettings; 