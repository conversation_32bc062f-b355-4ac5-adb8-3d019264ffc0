"""
Fix source attribution in chat answers.

This script provides a complete fix for source attribution by modifying the 
generate_clean_answer_with_sources function to ensure all page numbers are properly
displayed in source references.
"""

import os
import sys
import logging
import json
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Ensure server.py is accessible
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def patch_server():
    """Apply patches to the server.py file to fix source attribution."""
    server_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "server.py")
    
    if not os.path.exists(server_path):
        logger.error(f"server.py not found at {server_path}")
        return False
    
    # Read the server.py content
    with open(server_path, 'r', encoding='utf-8') as f:
        server_content = f.read()
    
    # Create a backup
    backup_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "server.py.bak")
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(server_content)
    logger.info(f"Created backup at {backup_path}")
    
    # Patches to apply
    
    # 1. First, modify the source key creation to ensure proper page grouping
    source_key_orig = """                # Create unique source key for deduplication (include content type for visual content)
                if content_type != "text":
                    source_key = f"{filename}_{content_type}_{page}"
                else:
                    source_key = f"{filename}"
"""
    
    source_key_replacement = """                # Create unique source key for deduplication (include content type for visual content)
                if content_type != "text":
                    source_key = f"{filename}_{content_type}_{page}"
                else:
                    # Group by filename for text content to ensure all pages are tracked together
                    source_key = f"{filename}"
                    
                logger.info(f"Processing chunk from document '{filename}', page {page}, content_type={content_type}")
"""
    
    # 2. Fix max sources limitation to include all pages
    max_sources_orig = """        # Limit to top 2 most relevant sources for better UX
        max_sources = 2 if source_type == "document" else 2  # Limit both document and website sources"""
    
    max_sources_replacement = """        # Include all document sources to ensure all page references are shown properly
        # For websites, still limit to top 2 most relevant sources for better UX
        max_sources = len(source_relevance) if source_type == "document" else 2
        logger.info(f"Including up to {max_sources} {source_type} sources to show all page references")"""
    
    # 3. Add debug logging for the page sorting
    page_sort_orig = """                # Sort pages and create page reference
                pages = sorted(source_data["pages"])
                if len(pages) == 1:
                    page_ref = f"Page {pages[0]}"
                else:
                    page_ref = f"Pages {', '.join(map(str, pages))}"
"""
    
    page_sort_replacement = """                # Sort pages and create page reference
                pages = sorted(source_data["pages"])
                logger.info(f"Document '{source_data['name']}' has pages: {pages}")
                if len(pages) == 1:
                    page_ref = f"Page {pages[0]}"
                else:
                    page_ref = f"Pages {', '.join(map(str, pages))}"
                logger.info(f"Page reference for '{source_data['name']}': {page_ref}")
"""
    
    # Apply patches
    patched_content = server_content
    
    if source_key_orig in patched_content:
        patched_content = patched_content.replace(source_key_orig, source_key_replacement)
        logger.info("Applied source key patch")
    else:
        logger.error("Could not find source key section to patch")
    
    if max_sources_orig in patched_content:
        patched_content = patched_content.replace(max_sources_orig, max_sources_replacement)
        logger.info("Applied max sources patch")
    else:
        logger.error("Could not find max sources section to patch")
    
    if page_sort_orig in patched_content:
        patched_content = patched_content.replace(page_sort_orig, page_sort_replacement)
        logger.info("Applied page sorting patch")
    else:
        logger.error("Could not find page sorting section to patch")
    
    # Write patched content
    with open(server_path, 'w', encoding='utf-8') as f:
        f.write(patched_content)
    
    logger.info("All patches applied successfully to server.py")
    return True

def test_source_attribution():
    """Test source attribution with proper page numbers."""
    
    # Create mock chunks with different page numbers
    chunks = [
        {
            "document_id": "doc123",
            "filename": "TestDocument.pdf",
            "title": "Test Document",
            "text": "This is page 1 text.",
            "page": 1,
            "page_number": 1,
            "similarity": 0.9,
            "metadata": {"content_type": "text"}
        },
        {
            "document_id": "doc123",
            "filename": "TestDocument.pdf", 
            "title": "Test Document",
            "text": "This is page 2 text.",
            "page": 2,
            "page_number": 2,
            "similarity": 0.85,
            "metadata": {"content_type": "text"}
        },
        {
            "document_id": "doc123",
            "filename": "TestDocument.pdf",
            "title": "Test Document",
            "text": "This is page 3 text.",
            "page": 3,
            "page_number": 3,
            "similarity": 0.8,
            "metadata": {"content_type": "text"} 
        }
    ]
    
    try:
        # Import after patching to get the updated version
        import server
        
        # Test with the updated function
        answer, clean_sources, visual_found, visual_types = server.generate_clean_answer_with_sources(
            query="Test query",
            chunks=chunks,
            source_type="document",
            model_id="gemini-2.0-flash"
        )
        
        # Display results
        logger.info(f"\nGenerated answer: {answer[:100]}...\n")
        
        logger.info("Clean sources:")
        for source in clean_sources:
            source_display = source.get('display_text') or f"{source.get('name')} - Pages {source.get('pages', [])}"
            logger.info(f"- {source_display}")
        
        # Check if it worked properly
        if clean_sources and any("Pages" in source.get('display_text', "") for source in clean_sources):
            logger.info("\n✅ SUCCESS: Source attribution fix is working properly with multiple pages!")
        else:
            logger.error("\n❌ ERROR: Source attribution fix did not work as expected.")
        
    except Exception as e:
        logger.error(f"Error testing source attribution: {str(e)}")

def main():
    """Main function to apply fix and test it."""
    logger.info("Starting source attribution fix")
    
    # Apply patches to server.py
    if patch_server():
        logger.info("Server.py patched successfully, now testing source attribution...")
        test_source_attribution()
    else:
        logger.error("Failed to patch server.py")

if __name__ == "__main__":
    main()
