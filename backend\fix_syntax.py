#!/usr/bin/env python3
"""Fix syntax error in server.py"""

def fix_server_syntax():
    with open('server.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the problematic line and fix it
    lines = content.split('\n')
    fixed_lines = []
    skip_section = False
    
    for i, line in enumerate(lines):
        # Look for the broken try block around line 2302
        if 'if document_chunks:' in line and i > 2290 and i < 2310:
            fixed_lines.append(line)
            # Add the missing logic and except block
            fixed_lines.append('                logger.info(f"✅ STEP 1: Found {len(document_chunks)} relevant document chunks")')
            # Add the missing except block
            fixed_lines.append('        except Exception as e:')
            fixed_lines.append('            logger.error(f"Error searching document chunks: {str(e)}")')
            fixed_lines.append('            document_chunks = []')
            fixed_lines.append('')
            continue
        
        # Skip duplicate imports section
        if line.strip() == 'import os' and i > 2300:
            skip_section = True
            continue
        
        # End of duplicate section when we hit the function definition
        if skip_section and line.strip().startswith('def search_documents_by_title'):
            skip_section = False
            fixed_lines.append(line)
            continue
        
        # Don't add lines in the skip section
        if not skip_section:
            fixed_lines.append(line)
    
    # Write the fixed content
    with open('server.py', 'w', encoding='utf-8') as f:
        f.write('\n'.join(fixed_lines))
    
    print("Fixed syntax error in server.py")

if __name__ == "__main__":
    fix_server_syntax() 