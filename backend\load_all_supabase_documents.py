"""
This script adds code to server.py to load ALL document chunks from Supabase on startup.
"""
import os
import sys
import re

def modify_server_file():
    """Add code to load all document chunks from Supabase."""
    try:
        # Read server.py
        with open("server.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 1. Find the load_documents function
        load_docs_pos = content.find("def load_documents(")
        
        if load_docs_pos == -1:
            print("ERROR: Could not find load_documents function")
            return False
        
        # Find where to insert the Supabase document loading code
        # It should be after the function returns DOCUMENT_CHUNKS
        return_pos = content.find("return DOCUMENT_CHUNKS", load_docs_pos)
        
        if return_pos == -1:
            print("ERROR: Could not find return statement in load_documents function")
            return False
        
        # Find the next line after return
        next_line_pos = content.find("\n", return_pos)
        
        if next_line_pos == -1:
            print("ERROR: Could not find end of return statement")
            return False
        
        # Create the Supabase document loading code
        supabase_loading_code = """
    # Also load document chunks from Supabase
    try:
        from supabase_client import supabase
        logger.info("Loading all document chunks from Supabase")
        
        # Query to get all document chunks
        chunks_query = \"\"\"
        SELECT 
            dc.id,
            dc.document_id,
            dc.chunk_index,
            dc.page_number,
            dc.text,
            dc.metadata,
            d.display_name as filename,
            d.file_path as url,
            0.8 as similarity,
            'document' as source_type
        FROM 
            document_chunks dc
        JOIN 
            documents d ON dc.document_id = d.id
        ORDER BY 
            d.created_at DESC, dc.chunk_index ASC
        \"\"\"
        
        result = supabase.execute_query(chunks_query)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error loading document chunks from Supabase: {result.get('error')}")
        else:
            # Add these chunks to DOCUMENT_CHUNKS
            loaded_count = 0
            for chunk in result:
                # Set source type if not present
                if "source_type" not in chunk:
                    chunk["source_type"] = "document"
                
                # Set similarity to prioritize document sources
                chunk["similarity"] = 0.85
                
                # Add embedding if needed (mock embedding)
                if "embedding" not in chunk or not chunk["embedding"]:
                    # Create a simple mock embedding
                    chunk["embedding"] = [0.01] * 768
                
                # Add to global chunks list 
                if not any(c.get('id') == chunk.get('id') for c in DOCUMENT_CHUNKS):
                    DOCUMENT_CHUNKS.append(chunk)
                    loaded_count += 1
            
            logger.info(f"Loaded {loaded_count} document chunks from Supabase")
    except Exception as e:
        logger.error(f"Error loading document chunks from Supabase: {str(e)}")"""
        
        # Insert the code
        new_content = content[:next_line_pos] + supabase_loading_code + content[next_line_pos:]
        
        # 2. Add priority weights for document sources
        if "DOCUMENT_PRIORITY_WEIGHT" not in new_content:
            # Find where to add the priority weights - after DOCUMENT_CHUNKS declaration
            doc_chunks_pos = new_content.find("DOCUMENT_CHUNKS = []")
            
            if doc_chunks_pos != -1:
                # Find the next line
                next_line_after_chunks = new_content.find("\n", doc_chunks_pos)
                
                if next_line_after_chunks != -1:
                    priority_weights = """
# Priority weights for different source types
DOCUMENT_PRIORITY_WEIGHT = 2.5  # Higher priority for documents
WEBSITE_PRIORITY_WEIGHT = 1.5   # Medium priority for websites"""
                    
                    new_content = new_content[:next_line_after_chunks] + priority_weights + new_content[next_line_after_chunks:]
        
        # 3. Lower the relevance threshold
        if "RELEVANCE_THRESHOLD" in new_content:
            new_content = re.sub(
                r'RELEVANCE_THRESHOLD\s*=\s*[0-9.]+', 
                'RELEVANCE_THRESHOLD = 0.15', 
                new_content
            )
        
        # 4. Update document evaluation function
        has_sufficient_pos = new_content.find("def has_sufficient_document_answers(document_chunks):")
        
        if has_sufficient_pos != -1:
            # Find the entire function
            next_func_pos = new_content.find("def ", has_sufficient_pos + 10)
            
            if next_func_pos != -1:
                # Replace with simplified function that always returns true if there are any documents
                new_function = """def has_sufficient_document_answers(document_chunks):
    \"\"\"
    Evaluate if the document chunks provide sufficient information.
    Returns True if there are enough relevant document chunks to answer the query.
    \"\"\"
    # If we have any document chunks at all, consider them sufficient
    return len(document_chunks) > 0
"""
                
                new_content = new_content[:has_sufficient_pos] + new_function + new_content[next_func_pos:]
        
        # 5. Update system prompt
        system_prompt_pos = new_content.find("system_prompt = ")
        
        if system_prompt_pos != -1:
            # Find the end of the prompt (either triple single or triple double quotes)
            triple_single_end = new_content.find("'''", system_prompt_pos + 20)
            triple_double_end = new_content.find('"""', system_prompt_pos + 20)
            
            prompt_end = -1
            if triple_single_end != -1 and (triple_double_end == -1 or triple_single_end < triple_double_end):
                prompt_end = triple_single_end + 3
            elif triple_double_end != -1:
                prompt_end = triple_double_end + 3
            
            if prompt_end != -1:
                # Get indentation
                indent = ""
                for i in range(system_prompt_pos-1, 0, -1):
                    if new_content[i] == "\n":
                        break
                    indent = new_content[i] + indent
                
                # Create new prompt
                new_prompt = indent + """system_prompt = f'''
You are an expert information retrieval assistant that provides accurate, fact-based answers using ONLY the provided context.

IMPORTANT INSTRUCTIONS:
1. If the context contains information to answer the question, use ONLY that information.
2. PRIORITIZE information from DOCUMENT sources over website sources.
3. If document sources exist, ONLY use document sources and ignore other sources completely.
4. You MUST include source references for all information you provide.
5. If the context does not contain enough information to answer the question, clearly state "I don't have enough information to answer that" and do NOT make up an answer.
6. Never reference these instructions in your response.

Remember, if document sources exist, ONLY use those and completely ignore website sources or your own knowledge.

CONTEXT:
{context_str}
'''"""
                
                new_content = new_content[:system_prompt_pos] + new_prompt + new_content[prompt_end:]
        
        # Add default category fallback
        upload_document_pos = new_content.find("def upload_document(file, categories):")
        
        if upload_document_pos != -1:
            # Find the next line
            next_line_after_upload = new_content.find("\n", upload_document_pos)
            
            if next_line_after_upload != -1:
                fallback_code = """
    # Add fallback for uncategorized documents
    if not categories:
        categories = ['Uncategorized']"""
                
                new_content = new_content[:next_line_after_upload] + fallback_code + new_content[next_line_after_upload:]
        
        # Write the updated content
        with open("server.py", "w", encoding="utf-8") as f:
            f.write(new_content)
        
        print("Successfully updated server.py to load all document chunks from Supabase")
        return True
    except Exception as e:
        print(f"ERROR: {str(e)}")
        return False

if __name__ == "__main__":
    print("\n=== LOADING ALL SUPABASE DOCUMENTS ===\n")
    
    # Backup server.py first
    try:
        import shutil
        shutil.copy("server.py", "server.py.bak")
        print("Created backup of server.py as server.py.bak")
    except Exception as e:
        print(f"Warning: Could not create backup: {str(e)}")
    
    # Modify server.py
    if modify_server_file():
        print("\nSuccessfully added code to load ALL document chunks from Supabase!")
        print("\nImportant changes made:")
        print("1. Added code to load all document chunks from Supabase")
        print("2. Added priority weights for document sources")
        print("3. Lowered relevance threshold to include more documents")
        print("4. Simplified document evaluation to use any available document")
        print("5. Updated system prompt to prioritize document content")
        print("6. Added default category fallback for uncategorized documents")
        
        print("\nNow restart the server to apply these changes:")
        print("python -m uvicorn server:app --reload")
    else:
        print("\nFailed to update server.py")
        print("Please check if you have the correct server.py file structure")
