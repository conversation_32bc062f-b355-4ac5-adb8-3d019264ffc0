import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import SourceCitation from './SourceCitation';
import { Loader2 } from 'lucide-react';

export interface ChatMessageProps {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  loading?: boolean;
  sources?: Array<any>;
  document_sources?: Array<any>;
  website_sources?: Array<any>;
  document_answer?: string;
  website_answer?: string;
}

const ChatMessage: React.FC<ChatMessageProps> = ({
  content,
  sender,
  loading,
  sources,
  document_sources,
  website_sources,
  document_answer,
  website_answer
}) => {
  // Determine if it's a user or AI message
  const isUser = sender === 'user';

  // Set background color based on sender
  const bgColor = isUser ? 'bg-blue-50' : 'bg-gray-50';

  // Set text alignment based on sender
  const textAlign = isUser ? 'text-right' : 'text-left';

  // Set primary content based on availability
  const primaryContent = content || document_answer || website_answer || '';

  // Check if document sources should be displayed
  const hasDocumentSources = (document_sources && document_sources.length > 0) || 
    (sources && sources.length > 0 && sources.some(s => s.document_id || s.filename));

  // Check if website sources should be displayed
  const hasWebsiteSources = (website_sources && website_sources.length > 0) ||
    (sources && sources.length > 0 && sources.some(s => s.website_id || s.url));

  return (
    <div className={`p-4 rounded-lg mb-4 ${bgColor}`}>
      <div className={`${textAlign}`}>
        <div className="font-semibold mb-1">
          {isUser ? 'You' : 'RailGPT'}
        </div>

        {loading ? (
          <div className="flex items-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Generating response...</span>
          </div>
        ) : (
          <div className="markdown-content">
            <ReactMarkdown remarkPlugins={[remarkGfm]}>
              {primaryContent}
            </ReactMarkdown>
          </div>
        )}

        {/* Show sources only for AI messages */}
        {!isUser && !loading && (hasDocumentSources || hasWebsiteSources) && (
          <SourceCitation 
            sources={sources || []} 
            documentSources={document_sources} 
            websiteSources={website_sources} 
          />
        )}
      </div>
    </div>
  );
};

export default ChatMessage;
