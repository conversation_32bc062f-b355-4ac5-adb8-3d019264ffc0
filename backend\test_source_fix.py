#!/usr/bin/env python3
"""
Test script for the source tracking fix
"""

import requests
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_source_tracking():
    """Test the source tracking fix with a real query"""
    
    # Test query
    test_query = "What is ACP in railways?"
    
    logger.info(f"Testing query: {test_query}")
    
    try:
        response = requests.post('http://localhost:8000/api/query', 
                               json={'query': test_query, 'fallback_enabled': False})
        
        if response.status_code == 200:
            result = response.json()
            
            logger.info(f"Answer: {result.get('answer', '')[:100]}...")
            logger.info(f"LLM Fallback: {result.get('llm_fallback')}")
            logger.info(f"Document sources: {len(result.get('document_sources', []))}")
            logger.info(f"Website sources: {len(result.get('website_sources', []))}")
            
            # Check sources
            for i, source in enumerate(result.get('document_sources', []), 1):
                logger.info(f"  Doc {i}: {source.get('display_text')}")
                
            for i, source in enumerate(result.get('website_sources', []), 1):
                logger.info(f"  Web {i}: {source.get('display_text')}")
                
            # Verify fix worked
            if result.get('llm_fallback') and (result.get('document_sources') or result.get('website_sources')):
                logger.error("BUG: LLM fallback used but sources present!")
            elif not result.get('llm_fallback') and not (result.get('document_sources') or result.get('website_sources')):
                logger.error("BUG: No fallback but no sources!")
            else:
                logger.info("Source tracking working correctly")
                
        else:
            logger.error(f"Request failed: {response.status_code}")
            
    except Exception as e:
        logger.error(f"Test failed: {str(e)}")

if __name__ == "__main__":
    test_source_tracking()
