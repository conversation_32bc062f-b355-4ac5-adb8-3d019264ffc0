import React, { useState } from 'react';
import { Website } from '../../types/websites';


interface WebsitesTableProps {
  websites: Website[];
  onView: (website: Website) => void;
  onRetry: (website: Website) => void;
  onDelete: (website: Website) => void;
  onCategoryUpdate?: (updatedWebsite: Website) => void;
  onChangeCategory?: (website: Website) => void;
  onBulkChangeCategory?: (websiteIds: string[]) => void;
}

const WebsitesTable: React.FC<WebsitesTableProps> = ({
  websites,
  onView,
  onRetry,
  onDelete,
  onCategoryUpdate,
  onChangeCategory,
  onBulkChangeCategory,
}) => {
  const [sortField, setSortField] = useState<keyof Website>('extractedAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [searchQuery, setSearchQuery] = useState('');
  const [domainFilter, setDomainFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedWebsites, setSelectedWebsites] = useState<string[]>([]);



  // Handle sorting
  const handleSort = (field: keyof Website) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Toggle selection of all websites
  const toggleSelectAll = (sites: Website[]) => {
    if (selectedWebsites.length === sites.length) {
      setSelectedWebsites([]);
    } else {
      setSelectedWebsites(sites.map(site => site.id));
    }
  };

  // Toggle selection of a single website
  const toggleSelectWebsite = (id: string) => {
    if (selectedWebsites.includes(id)) {
      setSelectedWebsites(selectedWebsites.filter(siteId => siteId !== id));
    } else {
      setSelectedWebsites([...selectedWebsites, id]);
    }
  };

  // Handle batch operations
  const handleBatchOperation = (operation: 'delete' | 'retry') => {
    if (selectedWebsites.length === 0) return;

    const selectedSites = websites.filter(site => selectedWebsites.includes(site.id));

    if (operation === 'delete') {
      if (window.confirm(`Are you sure you want to delete ${selectedWebsites.length} selected website(s)?`)) {
        selectedSites.forEach(site => onDelete(site));
        setSelectedWebsites([]);
      }
    } else if (operation === 'retry') {
      selectedSites.forEach(site => onRetry(site));
      setSelectedWebsites([]);
    }
  };

  // Filter websites based on search query and filters
  const filteredWebsites = websites.filter((site) => {
    // Search query filter (URLs or domains)
    if (
      searchQuery &&
      !site.url.toLowerCase().includes(searchQuery.toLowerCase()) &&
      !site.domain.toLowerCase().includes(searchQuery.toLowerCase())
    ) {
      return false;
    }

    // Domain filter
    if (
      domainFilter &&
      site.domain !== domainFilter &&
      site.domainCategory !== domainFilter
    ) {
      return false;
    }

    // Status filter
    if (statusFilter && site.status !== statusFilter) {
      return false;
    }

    return true;
  });

  // Sort websites
  const sortedWebsites = [...filteredWebsites].sort((a, b) => {
    // Use optional chaining and nullish coalescing to handle undefined
    const aValue = a[sortField] ?? '';
    const bValue = b[sortField] ?? '';
    
    if (aValue < bValue) {
      return sortDirection === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortDirection === 'asc' ? 1 : -1;
    }
    return 0;
  });

  // Format the date for display
  const formatDate = (dateStr: string | null | undefined) => {
    if (!dateStr) return 'N/A';
    
    try {
      // Check if dateStr is a valid date
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) {
        return 'Invalid date';
      }
      
      return new Intl.DateTimeFormat('en-IN', {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      }).format(date);
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  };

  // Render status badge
  const renderStatusBadge = (status: Website['status']) => {
    let bgColor;
    switch (status) {
      case 'Success':
        bgColor = 'bg-green-100 text-green-800';
        break;
      case 'Failed':
        bgColor = 'bg-red-100 text-red-800';
        break;
      case 'Manual Required':
        bgColor = 'bg-yellow-100 text-yellow-800';
        break;
      default:
        bgColor = 'bg-gray-100 text-gray-800';
    }

    return (
      <span className={`${bgColor} px-2 py-1 rounded-full text-xs font-medium`}>
        {status}
      </span>
    );
  };

  // Get unique domains for filtering
  const uniqueDomains = Array.from(
    new Set(
      websites.flatMap((site) => [
        site.domain,
        site.domainCategory || '',
      ]).filter(Boolean)
    )
  );

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold">Manage Websites</h2>
        
        {/* Filters and Search */}
        <div className="flex flex-col md:flex-row gap-4 mt-4">
          <div className="md:w-1/3">
            <input
              type="text"
              placeholder="Search URLs or domains..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="md:w-1/3">
            <select
              value={domainFilter}
              onChange={(e) => setDomainFilter(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Domains</option>
              {uniqueDomains.map((domain) => (
                <option key={domain as string} value={domain as string}>
                  {domain as string}
                </option>
              ))}
            </select>
          </div>
          
          <div className="md:w-1/3">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Statuses</option>
              <option value="Success">Success</option>
              <option value="Failed">Failed</option>
              <option value="Manual Required">Manual Required</option>
            </select>
          </div>
        </div>

        <div className="flex justify-between items-center mt-4">
          <div className="text-sm text-gray-500">
            Showing {filteredWebsites.length} of {websites.length} websites
          </div>
          
          {/* Bulk Actions */}
          {selectedWebsites.length > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">
                {selectedWebsites.length} selected
              </span>
              <button
                onClick={() => onBulkChangeCategory && onBulkChangeCategory(selectedWebsites)}
                className="px-3 py-1 bg-green-100 text-green-700 rounded-md hover:bg-green-200 text-sm"
                title="Change category for selected websites"
              >
                Change Category
              </button>
              <button
                onClick={() => handleBatchOperation('retry')}
                className="px-3 py-1 bg-yellow-100 text-yellow-700 rounded-md hover:bg-yellow-200 text-sm"
                title="Retry selected websites"
              >
                Retry
              </button>
              <button
                onClick={() => handleBatchOperation('delete')}
                className="px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 text-sm"
                title="Delete selected websites"
              >
                Delete
              </button>
            </div>
          )}
        </div>
      </div>
      
      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-3 py-3 text-center">
                <input
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                  checked={selectedWebsites.length > 0 && selectedWebsites.length === filteredWebsites.length}
                  onChange={() => toggleSelectAll(filteredWebsites)}
                />
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('url')}
              >
                URL
                {sortField === 'url' && (
                  <span className="ml-1">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('domain')}
              >
                Domain
                {sortField === 'domain' && (
                  <span className="ml-1">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('extractedAt')}
              >
                Extracted At
                {sortField === 'extractedAt' && (
                  <span className="ml-1">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('status')}
              >
                Status
                {sortField === 'status' && (
                  <span className="ml-1">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Categories
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedWebsites.length > 0 ? (
              sortedWebsites.map((website) => (
                <tr key={website.id} className={selectedWebsites.includes(website.id) ? 'bg-blue-50' : 'hover:bg-gray-50'}>
                  <td className="px-3 py-4 whitespace-nowrap text-center">
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                      checked={selectedWebsites.includes(website.id)}
                      onChange={() => toggleSelectWebsite(website.id)}
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-blue-600 hover:underline">
                      <a href={website.url} target="_blank" rel="noopener noreferrer">
                        {website.url.length > 50 ? `${website.url.substring(0, 50)}...` : website.url}
                      </a>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {website.domain}
                    </div>
                    {website.domainCategory && (
                      <div className="text-xs text-gray-500">
                        {website.domainCategory}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(website.extractedAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {renderStatusBadge(website.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {[website.mainCategory, website.category, website.subCategory, website.minorCategory]
                        .filter(Boolean)
                        .join(' > ') || (
                        <span className="text-gray-400 italic">No categories</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex space-x-3">
                      <button
                        onClick={() => onView(website)}
                        className="text-blue-600 hover:text-blue-900"
                        title="View website"
                      >
                        View
                      </button>

                      <button
                        onClick={() => onChangeCategory && onChangeCategory(website)}
                        className="text-green-600 hover:text-green-900"
                        title="Change category"
                      >
                        Category
                      </button>

                      <button
                        onClick={() => onRetry(website)}
                        className="text-yellow-600 hover:text-yellow-900"
                        title="Retry extraction"
                      >
                        Retry
                      </button>
                      <button
                        onClick={() => onDelete(website)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete website"
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">
                  No websites found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>


    </div>
  );
};

export default WebsitesTable;
