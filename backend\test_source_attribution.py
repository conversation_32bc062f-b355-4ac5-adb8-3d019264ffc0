"""
Test script for verifying source attribution in chat answers.
This script demonstrates proper source attribution with correct document names and page numbers.
"""

import sys
import os
import json
import logging
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import Supabase client
try:
    from supabase_client import supabase
except ImportError:
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from supabase_client import supabase

def test_source_attribution():
    """Test proper source attribution with correct document names and page numbers."""
    logger.info("Starting source attribution test")
    
    # 1. First check what documents are in the database
    try:
        query = "SELECT id, display_name, file_name, file_path FROM documents"
        result = supabase.execute_query(query)
        logger.info(f"Found {len(result)} documents in database")
        for doc in result:
            logger.info(f"Document: {doc.get('display_name', 'Unknown')} (ID: {doc.get('id', 'Unknown')})")
    except Exception as e:
        logger.error(f"Error retrieving documents: {str(e)}")
        return
    
    # 2. Check document chunks and their associated page numbers
    try:
        # Get a limited number of chunks to analyze
        query = """
        SELECT dc.id, dc.document_id, dc.page_number, dc.text, 
               d.display_name, d.file_name, d.file_path
        FROM document_chunks dc
        JOIN documents d ON dc.document_id = d.id
        LIMIT 20
        """
        result = supabase.execute_query(query)
        logger.info(f"Found {len(result)} document chunks with page info")
        
        # Group by document name and show page numbers
        doc_pages = {}
        for chunk in result:
            doc_name = chunk.get('display_name') or chunk.get('file_name') or 'Unknown'
            page = chunk.get('page_number', 0)
            doc_id = chunk.get('document_id', 'Unknown')
            
            if doc_name not in doc_pages:
                doc_pages[doc_name] = {'id': doc_id, 'pages': set()}
            
            doc_pages[doc_name]['pages'].add(page)
        
        # Print document page distribution
        logger.info("Document page distribution:")
        for doc, info in doc_pages.items():
            pages = sorted(list(info['pages']))
            logger.info(f"Document '{doc}' (ID: {info['id']}) has chunks from pages: {pages}")
    except Exception as e:
        logger.error(f"Error analyzing document chunks: {str(e)}")
    
    # 3. Create a mock source list to demonstrate proper format
    demo_sources = [
        {
            "source_type": "document",
            "filename": "ACP 110V (5).pdf",
            "name": "ACP 110V (5).pdf",
            "pages": [1, 2, 3]
        },
        {
            "source_type": "document",
            "filename": "SampleDoc.pdf",
            "name": "SampleDoc.pdf",
            "pages": [5, 7, 12]
        }
    ]
    
    # 4. Display how these sources should appear in the frontend
    logger.info("\nExample sources as they should appear in the frontend:")
    for source in demo_sources:
        pages = sorted(source['pages'])
        page_text = f"Page {pages[0]}" if len(pages) == 1 else f"Pages {', '.join(map(str, pages))}"
        logger.info(f"{source['name']} – {page_text}")
    
    # 5. Generate a proper formatted source dict for the frontend
    frontend_sources = []
    for source in demo_sources:
        pages = sorted(source['pages'])
        page_text = f"Page {pages[0]}" if len(pages) == 1 else f"Pages {', '.join(map(str, pages))}"
        frontend_sources.append({
            "text": f"{source['name']} – {page_text}",
            "link": f"/viewer?file={source['filename']}&page={pages[0]}",
            "isDocument": True
        })
    
    logger.info("\nJSON representation for frontend:")
    logger.info(json.dumps(frontend_sources, indent=2))
    
    logger.info("\nTest completed. Use this script to verify source attribution is working correctly.")

if __name__ == "__main__":
    test_source_attribution()
