# Enhanced Category Management System for RailGPT
# Comprehensive four-level category system with full CRUD operations
# Supports both document and website categories with proper hierarchy management

from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any, Union
import logging
import uuid
from datetime import datetime
import json

# Initialize logger
logger = logging.getLogger(__name__)

# Import Supabase client
try:
    from supabase_client import SupabaseClient
    def get_supabase_client():
        """Get Supabase client with proper error handling."""
        try:
            client = SupabaseClient()
            if client.supabase is None:
                logger.error("Supabase client initialization failed")
                return None
            return client
        except Exception as e:
            logger.error(f"Error getting Supabase client: {str(e)}")
            return None
except ImportError as e:
    logger.error(f"Could not import SupabaseClient: {e}")
    def get_supabase_client():
        return None

# Create router for enhanced category management
router = APIRouter(prefix="/api/categories", tags=["Enhanced Categories"])

# Enhanced Pydantic models
class CategoryBase(BaseModel):
    """Base category model with validation"""
    name: str = Field(..., min_length=1, max_length=255, description="Category name")
    type: str = Field(..., pattern="^(main_category|category|sub_category|minor_category)$", description="Category type")
    parent_id: Optional[str] = Field(None, description="Parent category UUID")
    description: Optional[str] = Field(None, max_length=1000, description="Category description")
    sort_order: Optional[int] = Field(0, ge=0, description="Sort order for display")

    @validator('parent_id', pre=True)
    def validate_parent_id(cls, v):
        if v and str(v).lower() in ['none', 'null', '']:
            return None
        return v

class CategoryCreate(CategoryBase):
    """Model for creating new categories"""
    
    @validator('type', pre=True)
    def validate_hierarchy(cls, v, values):
        # Basic validation - more complex validation will be done in the API
        return v

class CategoryUpdate(BaseModel):
    """Model for updating existing categories"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    sort_order: Optional[int] = Field(None, ge=0)
    is_active: Optional[bool] = None
    parent_id: Optional[str] = None

class CategoryResponse(CategoryBase):
    """Complete category response model"""
    id: str
    is_active: bool
    created_at: str
    updated_at: str
    full_path: Optional[str] = None
    level: Optional[int] = None

class CategoryHierarchy(CategoryResponse):
    """Category with hierarchy information and children"""
    children: List['CategoryHierarchy'] = []

class CategoryTree(BaseModel):
    """Tree structure for category hierarchy"""
    categories: List[CategoryHierarchy]
    total_count: int
    max_depth: int
    statistics: Dict[str, int]

class BulkCategoryOperation(BaseModel):
    """Model for bulk category operations"""
    category_ids: List[str] = Field(..., min_items=1)
    operation: str = Field(..., pattern="^(activate|deactivate|delete|move)$")
    target_parent_id: Optional[str] = None  # For move operations

class CategoryAssignment(BaseModel):
    """Model for assigning categories to documents/websites"""
    main_category_id: Optional[str] = None
    category_id: Optional[str] = None
    sub_category_id: Optional[str] = None
    minor_category_id: Optional[str] = None

class DocumentCategoryUpdate(BaseModel):
    """Model for updating document categories"""
    document_ids: List[str] = Field(..., min_items=1)
    categories: CategoryAssignment

class WebsiteCategoryUpdate(BaseModel):
    """Model for updating website categories"""
    website_ids: List[str] = Field(..., min_items=1)
    categories: CategoryAssignment

# Database helper functions
async def get_categories_from_db(table_name: str = 'document_categories', include_inactive: bool = False) -> List[Dict]:
    """Fetch categories from database with enhanced filtering and hierarchy calculation"""
    try:
        supabase = get_supabase_client()
        if not supabase:
            logger.error("Supabase client not available")
            return []

        # Build query
        query = supabase.supabase.table(table_name).select('*')
        if not include_inactive:
            query = query.eq('is_active', True)
        
        response = query.order('sort_order').execute()

        if not response.data:
            return []

        categories = response.data
        return await calculate_hierarchy_info(categories)

    except Exception as e:
        logger.error(f"Error fetching categories from {table_name}: {str(e)}")
        return []

async def calculate_hierarchy_info(categories: List[Dict]) -> List[Dict]:
    """Calculate full_path and level for each category"""
    category_map = {str(cat['id']): cat for cat in categories}
    
    def calculate_path_and_level(cat_id: str, visited: set = None) -> tuple:
        if visited is None:
            visited = set()
        
        if cat_id in visited:
            return "", 0  # Circular reference protection
        
        visited.add(cat_id)
        cat = category_map.get(cat_id)
        if not cat:
            return "", 0
        
        if not cat['parent_id']:
            # Root category
            cat['full_path'] = cat['name']
            cat['level'] = 0
            return cat['name'], 0
        
        parent_path, parent_level = calculate_path_and_level(str(cat['parent_id']), visited.copy())
        cat['full_path'] = f"{parent_path} > {cat['name']}" if parent_path else cat['name']
        cat['level'] = parent_level + 1
        return cat['full_path'], cat['level']
    
    # Calculate paths and levels for all categories
    for cat in categories:
        calculate_path_and_level(str(cat['id']))
    
    return categories

async def create_category_in_db(category_data: Dict, table_name: str = 'document_categories') -> Dict:
    """Create a new category in the database with validation"""
    try:
        supabase = get_supabase_client()
        if not supabase:
            raise Exception("Database connection not available")

        # Validate parent exists if specified
        if category_data.get('parent_id'):
            parent_response = supabase.supabase.table(table_name).select('id,type').eq('id', category_data['parent_id']).execute()
            if not parent_response.data:
                raise HTTPException(status_code=400, detail="Parent category not found")

        # Clean up the category data
        clean_data = {k: v for k, v in category_data.items() if v is not None and v != ""}
        if 'parent_id' in clean_data and clean_data['parent_id'] in ['None', 'null']:
            clean_data['parent_id'] = None

        logger.info(f"Creating category in {table_name}: {clean_data}")

        response = supabase.supabase.table(table_name).insert(clean_data).execute()

        if response.data:
            logger.info(f"Successfully created category: {response.data[0]['id']}")
            return response.data[0]
        else:
            raise Exception("Failed to create category in database")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

async def update_category_in_db(category_id: str, update_data: Dict, table_name: str = 'document_categories') -> Dict:
    """Update a category in the database"""
    try:
        supabase = get_supabase_client()
        if not supabase:
            raise Exception("Database connection not available")

        # Validate category exists
        existing_response = supabase.supabase.table(table_name).select('*').eq('id', category_id).execute()
        if not existing_response.data:
            raise HTTPException(status_code=404, detail="Category not found")

        # Clean update data
        clean_data = {k: v for k, v in update_data.items() if v is not None}
        clean_data['updated_at'] = datetime.utcnow().isoformat()

        response = supabase.supabase.table(table_name).update(clean_data).eq('id', category_id).execute()

        if response.data:
            return response.data[0]
        else:
            raise Exception("Failed to update category")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating category {category_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

async def delete_category_from_db(category_id: str, table_name: str = 'document_categories', force: bool = False) -> Dict:
    """Delete a category from the database with safety checks"""
    try:
        supabase = get_supabase_client()
        if not supabase:
            raise Exception("Database connection not available")

        # Check if category has children
        children_response = supabase.supabase.table(table_name).select('id').eq('parent_id', category_id).execute()
        if children_response.data and not force:
            raise HTTPException(status_code=400, detail="Cannot delete category that has subcategories. Use force=true to delete recursively.")

        # If force delete, recursively delete children first
        if force and children_response.data:
            for child in children_response.data:
                await delete_category_from_db(child['id'], table_name, force=True)

        # Delete the category
        response = supabase.supabase.table(table_name).delete().eq('id', category_id).execute()

        if not response.data:
            raise HTTPException(status_code=404, detail="Category not found")

        return response.data[0]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting category {category_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

def build_category_tree(categories: List[Dict]) -> List[Dict]:
    """Build hierarchical tree structure from flat category list"""
    category_map = {str(cat["id"]): dict(cat) for cat in categories}

    # Initialize children arrays
    for cat in category_map.values():
        cat["children"] = []

    # Build the tree structure
    root_categories = []
    for cat in category_map.values():
        if cat["parent_id"]:
            parent = category_map.get(str(cat["parent_id"]))
            if parent:
                parent["children"].append(cat)
        else:
            root_categories.append(cat)

    # Sort categories by sort_order and name
    def sort_categories(cats):
        cats.sort(key=lambda x: (x["sort_order"], x["name"]))
        for cat in cats:
            if cat["children"]:
                sort_categories(cat["children"])

    sort_categories(root_categories)
    return root_categories

# ============================================================================
# API ENDPOINTS
# ============================================================================

# Health check endpoint
@router.get("/health")
async def health_check():
    """Health check for category management system"""
    try:
        supabase = get_supabase_client()
        if not supabase:
            return {"status": "error", "message": "Database connection not available"}

        # Test database connection
        response = supabase.supabase.table('document_categories').select('count').execute()

        return {
            "status": "healthy",
            "message": "Category management system is operational",
            "database_connected": True
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Health check failed: {str(e)}",
            "database_connected": False
        }

# Document Categories Endpoints
@router.get("/documents", response_model=CategoryTree)
async def get_document_categories(
    include_inactive: bool = Query(False, description="Include inactive categories"),
    tree_format: bool = Query(True, description="Return as tree structure")
):
    """Get all document categories with hierarchy"""
    try:
        logger.info("Fetching document categories")

        categories = await get_categories_from_db('document_categories', include_inactive)

        if tree_format:
            tree_categories = build_category_tree(categories)
        else:
            tree_categories = categories

        # Calculate statistics
        stats = {
            "main_categories": len([c for c in categories if c['type'] == 'main_category']),
            "categories": len([c for c in categories if c['type'] == 'category']),
            "sub_categories": len([c for c in categories if c['type'] == 'sub_category']),
            "minor_categories": len([c for c in categories if c['type'] == 'minor_category']),
            "active": len([c for c in categories if c['is_active']]),
            "inactive": len([c for c in categories if not c['is_active']])
        }

        max_depth = max([c.get('level', 0) for c in categories]) if categories else 0

        return CategoryTree(
            categories=tree_categories,
            total_count=len(categories),
            max_depth=max_depth,
            statistics=stats
        )

    except Exception as e:
        logger.error(f"Error fetching document categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/documents", response_model=CategoryResponse)
async def create_document_category(category: CategoryCreate):
    """Create a new document category"""
    try:
        logger.info(f"Creating document category: {category.name}")

        category_data = category.dict()
        result = await create_category_in_db(category_data, 'document_categories')

        return CategoryResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating document category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/documents/{category_id}", response_model=CategoryResponse)
async def update_document_category(category_id: str, updates: CategoryUpdate):
    """Update a document category"""
    try:
        logger.info(f"Updating document category: {category_id}")

        update_data = {k: v for k, v in updates.dict().items() if v is not None}
        result = await update_category_in_db(category_id, update_data, 'document_categories')

        return CategoryResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating document category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/documents/{category_id}")
async def delete_document_category(
    category_id: str,
    force: bool = Query(False, description="Force delete including children")
):
    """Delete a document category"""
    try:
        logger.info(f"Deleting document category: {category_id}")

        result = await delete_category_from_db(category_id, 'document_categories', force)

        return {"message": "Category deleted successfully", "deleted_category": result}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting document category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Website Categories Endpoints
@router.get("/websites", response_model=CategoryTree)
async def get_website_categories(
    include_inactive: bool = Query(False, description="Include inactive categories"),
    tree_format: bool = Query(True, description="Return as tree structure")
):
    """Get all website categories with hierarchy"""
    try:
        logger.info("Fetching website categories")

        categories = await get_categories_from_db('website_categories', include_inactive)

        if tree_format:
            tree_categories = build_category_tree(categories)
        else:
            tree_categories = categories

        # Calculate statistics
        stats = {
            "main_categories": len([c for c in categories if c['type'] == 'main_category']),
            "categories": len([c for c in categories if c['type'] == 'category']),
            "sub_categories": len([c for c in categories if c['type'] == 'sub_category']),
            "minor_categories": len([c for c in categories if c['type'] == 'minor_category']),
            "active": len([c for c in categories if c['is_active']]),
            "inactive": len([c for c in categories if not c['is_active']])
        }

        max_depth = max([c.get('level', 0) for c in categories]) if categories else 0

        return CategoryTree(
            categories=tree_categories,
            total_count=len(categories),
            max_depth=max_depth,
            statistics=stats
        )

    except Exception as e:
        logger.error(f"Error fetching website categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/websites", response_model=CategoryResponse)
async def create_website_category(category: CategoryCreate):
    """Create a new website category"""
    try:
        logger.info(f"Creating website category: {category.name}")

        category_data = category.dict()
        result = await create_category_in_db(category_data, 'website_categories')

        return CategoryResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating website category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/websites/{category_id}", response_model=CategoryResponse)
async def update_website_category(category_id: str, updates: CategoryUpdate):
    """Update a website category"""
    try:
        logger.info(f"Updating website category: {category_id}")

        update_data = {k: v for k, v in updates.dict().items() if v is not None}
        result = await update_category_in_db(category_id, update_data, 'website_categories')

        return CategoryResponse(**result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating website category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/websites/{category_id}")
async def delete_website_category(
    category_id: str,
    force: bool = Query(False, description="Force delete including children")
):
    """Delete a website category"""
    try:
        logger.info(f"Deleting website category: {category_id}")

        result = await delete_category_from_db(category_id, 'website_categories', force)

        return {"message": "Category deleted successfully", "deleted_category": result}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting website category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Bulk Operations
@router.post("/bulk-operation")
async def bulk_category_operation(operation: BulkCategoryOperation, table_type: str = Query("documents", pattern="^(documents|websites)$")):
    """Perform bulk operations on categories"""
    try:
        table_name = f"{table_type[:-1]}_categories"  # documents -> document_categories
        logger.info(f"Performing bulk {operation.operation} on {len(operation.category_ids)} categories in {table_name}")

        supabase = get_supabase_client()
        if not supabase:
            raise HTTPException(status_code=500, detail="Database connection not available")

        results = []

        for category_id in operation.category_ids:
            try:
                if operation.operation == "activate":
                    result = await update_category_in_db(category_id, {"is_active": True}, table_name)
                elif operation.operation == "deactivate":
                    result = await update_category_in_db(category_id, {"is_active": False}, table_name)
                elif operation.operation == "delete":
                    result = await delete_category_from_db(category_id, table_name, force=True)
                elif operation.operation == "move":
                    if operation.target_parent_id:
                        result = await update_category_in_db(category_id, {"parent_id": operation.target_parent_id}, table_name)
                    else:
                        raise HTTPException(status_code=400, detail="target_parent_id required for move operation")

                results.append({"category_id": category_id, "status": "success", "result": result})

            except Exception as e:
                results.append({"category_id": category_id, "status": "error", "error": str(e)})

        success_count = len([r for r in results if r["status"] == "success"])

        return {
            "message": f"Bulk operation completed: {success_count}/{len(operation.category_ids)} successful",
            "operation": operation.operation,
            "results": results
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in bulk operation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Category Assignment Endpoints
@router.post("/assign/documents")
async def assign_document_categories(assignment: DocumentCategoryUpdate):
    """Assign categories to documents"""
    try:
        logger.info(f"Assigning categories to {len(assignment.document_ids)} documents")

        supabase = get_supabase_client()
        if not supabase:
            raise HTTPException(status_code=500, detail="Database connection not available")

        # Validate that all category IDs exist
        category_ids = [
            assignment.categories.main_category_id,
            assignment.categories.category_id,
            assignment.categories.sub_category_id,
            assignment.categories.minor_category_id
        ]

        valid_category_ids = []
        for cat_id in category_ids:
            if cat_id:
                response = supabase.supabase.table('document_categories').select('id,name').eq('id', cat_id).execute()
                if not response.data:
                    raise HTTPException(status_code=400, detail=f"Category {cat_id} not found")
                valid_category_ids.append(cat_id)

        # Update documents with category assignments
        results = []
        for doc_id in assignment.document_ids:
            try:
                # Check if document exists
                doc_response = supabase.supabase.table('documents').select('id').eq('id', doc_id).execute()
                if not doc_response.data:
                    results.append({"document_id": doc_id, "status": "error", "error": "Document not found"})
                    continue

                # Update document with category assignments
                update_data = {}
                if assignment.categories.main_category_id:
                    update_data['main_category'] = assignment.categories.main_category_id
                if assignment.categories.category_id:
                    update_data['category'] = assignment.categories.category_id
                if assignment.categories.sub_category_id:
                    update_data['sub_category'] = assignment.categories.sub_category_id
                if assignment.categories.minor_category_id:
                    update_data['minor_category'] = assignment.categories.minor_category_id

                update_response = supabase.supabase.table('documents').update(update_data).eq('id', doc_id).execute()

                if update_response.data:
                    results.append({"document_id": doc_id, "status": "success", "updated_fields": update_data})
                else:
                    results.append({"document_id": doc_id, "status": "error", "error": "Update failed"})

            except Exception as e:
                results.append({"document_id": doc_id, "status": "error", "error": str(e)})

        success_count = len([r for r in results if r["status"] == "success"])

        return {
            "message": f"Category assignment completed: {success_count}/{len(assignment.document_ids)} successful",
            "results": results
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error assigning document categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/assign/websites")
async def assign_website_categories(assignment: WebsiteCategoryUpdate):
    """Assign categories to websites"""
    try:
        logger.info(f"Assigning categories to {len(assignment.website_ids)} websites")

        supabase = get_supabase_client()
        if not supabase:
            raise HTTPException(status_code=500, detail="Database connection not available")

        # Validate that all category IDs exist
        category_ids = [
            assignment.categories.main_category_id,
            assignment.categories.category_id,
            assignment.categories.sub_category_id,
            assignment.categories.minor_category_id
        ]

        valid_category_ids = []
        for cat_id in category_ids:
            if cat_id:
                response = supabase.supabase.table('website_categories').select('id,name').eq('id', cat_id).execute()
                if not response.data:
                    raise HTTPException(status_code=400, detail=f"Category {cat_id} not found")
                valid_category_ids.append(cat_id)

        # Update websites with category assignments
        results = []
        for website_id in assignment.website_ids:
            try:
                # Check if website exists
                website_response = supabase.supabase.table('websites').select('id').eq('id', website_id).execute()
                if not website_response.data:
                    results.append({"website_id": website_id, "status": "error", "error": "Website not found"})
                    continue

                # Update website with category assignments
                update_data = {}
                if assignment.categories.main_category_id:
                    update_data['main_category'] = assignment.categories.main_category_id
                if assignment.categories.category_id:
                    update_data['category_level2'] = assignment.categories.category_id
                if assignment.categories.sub_category_id:
                    update_data['sub_category'] = assignment.categories.sub_category_id
                if assignment.categories.minor_category_id:
                    update_data['minor_category'] = assignment.categories.minor_category_id

                update_response = supabase.supabase.table('websites').update(update_data).eq('id', website_id).execute()

                if update_response.data:
                    results.append({"website_id": website_id, "status": "success", "updated_fields": update_data})
                else:
                    results.append({"website_id": website_id, "status": "error", "error": "Update failed"})

            except Exception as e:
                results.append({"website_id": website_id, "status": "error", "error": str(e)})

        success_count = len([r for r in results if r["status"] == "success"])

        return {
            "message": f"Category assignment completed: {success_count}/{len(assignment.website_ids)} successful",
            "results": results
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error assigning website categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Utility Endpoints
@router.get("/hierarchy/{table_type}")
async def get_category_hierarchy(
    table_type: str,
    parent_id: Optional[str] = Query(None, description="Get children of specific parent")
):
    """Get category hierarchy for dropdown/selection purposes"""
    try:
        # Validate table_type
        if table_type not in ["documents", "websites"]:
            raise HTTPException(status_code=400, detail="table_type must be 'documents' or 'websites'")

        table_name = f"{table_type[:-1]}_categories"

        supabase = get_supabase_client()
        if not supabase:
            raise HTTPException(status_code=500, detail="Database connection not available")

        # Get categories filtered by parent if specified
        query = supabase.supabase.table(table_name).select('*').eq('is_active', True)
        if parent_id:
            query = query.eq('parent_id', parent_id)

        response = query.order('sort_order').execute()

        categories = response.data or []

        # Format for frontend dropdown usage
        formatted_categories = []
        for cat in categories:
            formatted_categories.append({
                "id": cat['id'],
                "name": cat['name'],
                "type": cat['type'],
                "parent_id": cat['parent_id'],
                "full_path": cat.get('full_path', cat['name']),
                "level": cat.get('level', 0)
            })

        return {
            "categories": formatted_categories,
            "total": len(formatted_categories),
            "parent_id": parent_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting category hierarchy: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
