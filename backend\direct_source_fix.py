#!/usr/bin/env python3
"""
Direct fix for the generate_clean_answer_with_sources function in RailGPT.

This script directly replaces the problematic function with a fixed version that:
1. Only tracks chunks that are ACTUALLY used in the LLM prompt
2. Implements stricter filtering to prevent phantom sources
3. Ensures proper fallback logic
"""

import os
import sys
import logging
import time
import shutil

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_fixed_function():
    """Return the fixed version of generate_clean_answer_with_sources"""
    
    return '''def generate_clean_answer_with_sources(query: str, chunks: List[Dict[str, Any]], source_type: str, model_id: str = "gemini-2.0-flash", extract_format: str = "paragraph"):
    """
    Generate a clean answer with properly formatted sources for a specific source type.
    
    *** FIXED VERSION *** 
    
    This version ONLY tracks chunks that are ACTUALLY USED in the LLM prompt as sources.
    Fixes the bug where sources were shown regardless of whether those pages 
    actually contributed to the answer.

    Args:
        query: The user's question
        chunks: List of chunks from a single source type (document OR website)
        source_type: Either "document" or "website"
        model_id: LLM model to use
        extract_format: Preferred format for the extraction

    Returns:
        Tuple of (answer_text, clean_sources_list, visual_content_found, visual_content_types)
    """
    try:
        logger.info(f"🔍 [FIXED] Generating clean {source_type} answer from {len(chunks)} total chunks")

        # STEP 1: Apply STRICTER filtering - increased thresholds
        min_doc_threshold = 0.40   # INCREASED from 0.3 to 0.40
        min_web_threshold = 0.25   # INCREASED from 0.15 to 0.25  
        min_threshold = min_doc_threshold if source_type == "document" else min_web_threshold
        min_text_length = 60       # INCREASED minimum text length

        # Filter chunks by relevance with stricter criteria
        relevant_chunks = []
        for chunk in chunks:
            similarity = chunk.get('similarity', 0.0)
            chunk_text = chunk.get('text', '').strip()

            # CRITICAL FIX: Apply stricter filtering
            if (similarity < min_threshold or 
                not chunk_text or 
                len(chunk_text) < min_text_length):
                logger.info(f"⏭️ [FIXED] Rejecting {source_type} chunk: similarity={similarity:.3f} (need >={min_threshold}), text_len={len(chunk_text)} (need >={min_text_length})")
                continue

            relevant_chunks.append(chunk)

        # CRITICAL FIX: Return empty immediately if no chunks meet criteria
        if not relevant_chunks:
            logger.info(f"❌ [FIXED] NO relevant {source_type} chunks found above threshold {min_threshold}")
            return f"No relevant information found in {source_type} sources.", [], False, []

        # STEP 2: Limit chunks that will ACTUALLY be used in LLM prompt
        max_chunks_to_use = 3 if source_type == "document" else 2  # REDUCED limits
        
        # Sort by similarity and take only the top chunks
        relevant_chunks_sorted = sorted(relevant_chunks, key=lambda x: x.get('similarity', 0), reverse=True)
        chunks_to_use = relevant_chunks_sorted[:max_chunks_to_use]
        
        logger.info(f"📊 [FIXED] Will use {len(chunks_to_use)} chunks out of {len(relevant_chunks)} relevant (from {len(chunks)} total)")
        
        # Log which chunks will be used
        for i, chunk in enumerate(chunks_to_use, 1):
            similarity = chunk.get('similarity', 0)
            if source_type == "document":
                filename = chunk.get('filename', 'Unknown')
                page = chunk.get('page', 'Unknown')
                logger.info(f"   ✅ [FIXED] Will use chunk {i}: {filename} page {page} (similarity: {similarity:.3f})")
            else:
                url = chunk.get('url', 'Unknown')
                logger.info(f"   ✅ [FIXED] Will use chunk {i}: {url} (similarity: {similarity:.3f})")

        # STEP 3: Build context and track sources ONLY from chunks being used
        context_texts = []
        sources_actually_used = {}  # Track ONLY sources from chunks used in LLM
        visual_content_found = False
        visual_content_types = []

        for chunk in chunks_to_use:  # CRITICAL: Only process chunks being used
            chunk_text = chunk.get("text", "")
            similarity = chunk.get("similarity", 0)

            if source_type == "document":
                # Get document info
                document_id = chunk.get("document_id")
                filename = None
                
                if document_id:
                    try:
                        from supabase_client import supabase
                        doc_query = f"SELECT COALESCE(display_name, file_name, name) as filename FROM documents WHERE id = '{document_id}'"
                        doc_result = supabase.execute_query(doc_query)
                        if doc_result and len(doc_result) > 0:
                            filename = doc_result[0]['filename']
                    except Exception as e:
                        logger.error(f"Error retrieving document name: {str(e)}")
                
                if not filename:
                    filename = chunk.get("filename") or chunk.get("title") or "Unknown document"
                
                page = chunk.get("page") or chunk.get("page_number") or 1

                # Check for visual content
                metadata = chunk.get("metadata", {})
                content_type = metadata.get("content_type", "text")
                
                if content_type != "text":
                    visual_content_found = True
                    if content_type not in visual_content_types:
                        visual_content_types.append(content_type)

                # Add to context
                context_texts.append(f"From '{filename}' (page {page}, relevance: {similarity:.2f}):\\n{chunk_text}\\n")

                # CRITICAL: Track this source since we're using it
                source_key = f"{filename}"
                
                if source_key not in sources_actually_used:
                    sources_actually_used[source_key] = {
                        "source_type": "document",
                        "filename": filename,
                        "name": os.path.basename(filename),
                        "pages": set(),
                        "content_type": content_type,
                        "max_similarity": similarity,
                        "visual_content": format_visual_content_for_frontend(metadata, content_type) if content_type != "text" else None
                    }
                else:
                    if similarity > sources_actually_used[source_key]["max_similarity"]:
                        sources_actually_used[source_key]["max_similarity"] = similarity

                sources_actually_used[source_key]["pages"].add(page)

            elif source_type == "website":
                # Website processing
                url = chunk.get("url", "")
                
                if not url or url == "Unknown":
                    chunk_text_for_url = chunk.get("text", "")
                    import re
                    url_match = re.search(r'URL:\\s*(https?://[^\\s\\n]+)', chunk_text_for_url)
                    if url_match:
                        url = url_match.group(1)
                    else:
                        url = "Unknown website"

                # Add to context
                context_texts.append(f"From '{url}' (relevance: {similarity:.2f}):\\n{chunk_text}\\n")

                # CRITICAL: Track this source since we're using it
                source_key = url
                if source_key not in sources_actually_used:
                    sources_actually_used[source_key] = {
                        "source_type": "website",
                        "url": url,
                        "title": chunk.get("title", ""),
                        "max_similarity": similarity
                    }
                else:
                    if similarity > sources_actually_used[source_key]["max_similarity"]:
                        sources_actually_used[source_key]["max_similarity"] = similarity

        # STEP 4: Generate answer using ONLY the selected chunks
        if not context_texts:
            logger.info(f"❌ [FIXED] No valid context generated from {source_type} chunks")
            return f"Could not extract meaningful information from {source_type} sources.", [], visual_content_found, visual_content_types

        combined_context = "\\n".join(context_texts)
        
        logger.info(f"🤖 [FIXED] Generating {source_type} answer with {len(chunks_to_use)} chunks, context length: {len(combined_context)}")

        # Generate the answer
        answer = generate_llm_answer(
            query=query,
            similar_chunks=chunks_to_use,  # Only chunks being used
            model_id=model_id,
            extract_format=extract_format
        )

        if not answer or answer.strip() == "":
            logger.warning(f"⚠️ [FIXED] Empty answer generated for {source_type}")
            return f"Could not generate a meaningful answer from {source_type} sources.", [], visual_content_found, visual_content_types

        # STEP 5: Build sources list from ONLY the chunks we actually used
        clean_sources = []
        
        for source_key, source_data in sources_actually_used.items():
            if source_type == "document":
                pages = sorted(list(source_data["pages"]))
                
                logger.info(f"📄 [FIXED] Source '{source_data['name']}' - Pages ACTUALLY USED: {pages}")
                
                if len(pages) == 1:
                    page_ref = f"Page {pages[0]}"
                else:
                    page_ref = f"Pages {', '.join(map(str, pages))}"

                clean_source = {
                    "source_type": "document",
                    "filename": source_data["filename"],
                    "name": source_data["name"],
                    "page": pages[0],
                    "pages": pages,
                    "link": f"/viewer?doc={source_data['filename']}&page={pages[0]}",
                    "display_text": f"{source_data['name']} – {page_ref}",
                    "relevance_score": source_data["max_similarity"],
                    "content_type": source_data.get("content_type", "text"),
                    "visual_content": source_data.get("visual_content")
                }
                clean_sources.append(clean_source)

            elif source_type == "website":
                clean_source = {
                    "source_type": "website",
                    "url": source_data["url"],
                    "title": source_data.get("title", ""),
                    "name": source_data.get("title", source_data["url"]),
                    "link": f"/website-preview?url={source_data['url']}",
                    "display_text": source_data.get("title", source_data["url"]),
                    "relevance_score": source_data["max_similarity"]
                }
                clean_sources.append(clean_source)

        # Sort by relevance
        clean_sources.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)

        logger.info(f"✅ [FIXED] Generated {source_type} answer with {len(clean_sources)} TRUE sources (only from chunks actually used in LLM)")
        
        return answer, clean_sources, visual_content_found, visual_content_types

    except Exception as e:
        logger.error(f"❌ [FIXED] Error in generate_clean_answer_with_sources: {str(e)}")
        return f"Error processing {source_type} content: {str(e)}", [], False, []'''

def apply_direct_fix():
    """Apply the direct fix by replacing the function"""
    
    server_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "server.py")
    
    if not os.path.exists(server_path):
        logger.error(f"server.py not found at {server_path}")
        return False
    
    # Create backup
    backup_path = server_path + f".backup_direct_fix_{int(time.time())}"
    shutil.copy2(server_path, backup_path)
    logger.info(f"Created backup at {backup_path}")
    
    # Read current content
    with open(server_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the function to replace
    func_start = content.find("def generate_clean_answer_with_sources(")
    if func_start == -1:
        logger.error("Could not find generate_clean_answer_with_sources function")
        return False
    
    # Find the end of the function
    lines = content[func_start:].split('\\n')
    func_lines = []
    in_function = True
    
    for i, line in enumerate(lines):
        if i == 0:  # Function definition line
            func_lines.append(line)
            continue
            
        # Check if we've reached the end of the function
        if line.strip() and not line.startswith(' ') and not line.startswith('\\t'):
            # We've hit a new top-level definition
            break
            
        func_lines.append(line)
    
    old_function = '\\n'.join(func_lines)
    func_end = func_start + len(old_function)
    
    # Replace with fixed function
    fixed_function = get_fixed_function()
    new_content = content[:func_start] + fixed_function + content[func_end:]
    
    # Write back
    with open(server_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    logger.info("✅ Applied direct fix to generate_clean_answer_with_sources function")
    return True

def main():
    """Main function"""
    
    logger.info("🎯 Applying DIRECT fix to generate_clean_answer_with_sources function")
    logger.info("This fixes the core bug where sources were shown regardless of actual contribution")
    
    if apply_direct_fix():
        logger.info("✅ Direct fix applied successfully!")
        logger.info("\\n🔧 Key changes made:")
        logger.info("1. ✅ Increased similarity thresholds (0.40 for docs, 0.25 for websites)")
        logger.info("2. ✅ Increased minimum text length (60 characters)")
        logger.info("3. ✅ Reduced max chunks used (3 for docs, 2 for websites)")
        logger.info("4. ✅ Enhanced logging to show which chunks are actually used")
        logger.info("5. ✅ Only track sources from chunks that go into the LLM prompt")
        logger.info("\\n🚀 RESTART THE SERVER to apply changes!")
    else:
        logger.error("❌ Failed to apply direct fix")

if __name__ == "__main__":
    main() 