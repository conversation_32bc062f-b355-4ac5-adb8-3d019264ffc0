import React, { useState, useRef, useEffect } from 'react';
import axios from 'axios';
import { Send } from 'lucide-react';
import ChatMessage, { ChatMessageProps } from './ChatMessage';
import { v4 as uuidv4 } from 'uuid';
import { formatMessageContent, extractAllSources } from '../../services/messageFormatter';

interface ChatInterfaceProps {
  sessionId?: string;
  onNewSession?: (sessionId: string) => void;
  initialMessages?: ChatMessageProps[];
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  sessionId,
  onNewSession,
  initialMessages = []
}) => {
  const [messages, setMessages] = useState<ChatMessageProps[]>(initialMessages);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSend = async () => {
    if (!input.trim() || isLoading) return;

    const userMessage: ChatMessageProps = {
      id: uuidv4(),
      content: input,
      sender: 'user'
    };

    const aiMessage: ChatMessageProps = {
      id: uuidv4(),
      content: '',
      sender: 'ai',
      loading: true
    };

    // Add user message and loading AI message
    setMessages([...messages, userMessage, aiMessage]);
    setInput('');
    setIsLoading(true);

    try {
      // Make API request to get response
      const response = await axios.post('/api/chat', {
        query: input,
        session_id: sessionId,
        history: messages.map(msg => ({
          role: msg.sender === 'user' ? 'user' : 'assistant',
          content: msg.content
        }))
      });

      // If this is a new chat, call the onNewSession callback
      if (!sessionId && response.data.session_id && onNewSession) {
        onNewSession(response.data.session_id);
      }

      // Process response data using formatters
      const content = formatMessageContent(response.data);
      const { documentSources, websiteSources, allSources } = extractAllSources(response.data);

      // Update AI message with processed response
      setMessages(prevMessages => {
        const newMessages = [...prevMessages];
        const lastIndex = newMessages.length - 1;

        // Ensure we're updating the correct message
        if (lastIndex >= 0 && newMessages[lastIndex].sender === 'ai') {
          newMessages[lastIndex] = {
            ...newMessages[lastIndex],
            id: response.data.id || newMessages[lastIndex].id,
            content: content,
            document_answer: response.data.document_answer || '',
            website_answer: response.data.website_answer || '',
            loading: false,
            sources: allSources,
            document_sources: documentSources,
            website_sources: websiteSources
          };
        }

        return newMessages;
      });
    } catch (error) {
      console.error('Error getting chat response:', error);

      // Update AI message with error
      setMessages(prevMessages => {
        const newMessages = [...prevMessages];
        const lastIndex = newMessages.length - 1;

        if (lastIndex >= 0 && newMessages[lastIndex].sender === 'ai') {
          newMessages[lastIndex] = {
            ...newMessages[lastIndex],
            content: 'Sorry, there was an error processing your request. Please try again.',
            loading: false
          };
        }

        return newMessages;
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 mt-10">
            <h2 className="text-xl font-semibold mb-2">Welcome to RailGPT</h2>
            <p>Ask me anything about railways!</p>
          </div>
        ) : (
          messages.map(message => (
            <ChatMessage 
              key={message.id} 
              {...message} 
            />
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      <div className="border-t p-4">
        <div className="flex items-center space-x-2">
          <textarea
            className="flex-1 border rounded-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Type your message..."
            rows={2}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={isLoading}
          />
          <button
            onClick={handleSend}
            disabled={!input.trim() || isLoading}
            className="p-2 bg-blue-600 text-white rounded-lg disabled:bg-gray-400"
          >
            <Send size={20} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
