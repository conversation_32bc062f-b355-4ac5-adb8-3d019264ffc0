import requests

response = requests.post("http://localhost:8000/api/query", json={
    "query": "Show me tables from FSDS",
    "use_hybrid_search": True
})

result = response.json()

print("=== DETAILED TABLE DEBUG ===")
for i, source in enumerate(result.get('sources', []), 1):
    if source.get('content_type') == 'table' or source.get('visual_content', {}).get('visual_content_type') == 'table':
        print(f"Source {i}: Table found")
        print(f"  Filename: {source.get('filename')}")
        print(f"  Page: {source.get('page')}")
        
        visual = source.get('visual_content', {})
        print(f"  Visual content keys: {list(visual.keys())}")
        
        if 'table_html' in visual:
            html = visual['table_html']
            print(f"  Table HTML length: {len(html)}")
            print(f"  Table HTML preview: {html[:200]}...")
        else:
            print(f"  ❌ Missing table_html field")
            
        if 'table_data' in visual:
            print(f"  Table data rows: {len(visual['table_data'])}")
        
        if 'markdown_table' in visual:
            print(f"  Markdown table length: {len(visual['markdown_table'])}")
        
        print() 