#!/usr/bin/env python3
"""
Check existing tables and populate categories correctly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from supabase_client import SupabaseClient
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_existing_tables():
    """Check what category tables exist"""
    
    print("🔍 Checking Existing Tables")
    print("=" * 50)
    
    client = SupabaseClient()
    
    tables_to_check = ['categories', 'document_categories', 'website_categories']
    existing_tables = []
    
    for table_name in tables_to_check:
        try:
            result = client.supabase.table(table_name).select("*").limit(1).execute()
            print(f"✅ Table '{table_name}' exists")
            existing_tables.append(table_name)
            
            # Check if table has data
            count_result = client.supabase.table(table_name).select("*", count="exact").execute()
            count = count_result.count if hasattr(count_result, 'count') else len(count_result.data)
            print(f"   📊 Contains {count} records")
            
        except Exception as e:
            print(f"❌ Table '{table_name}' does not exist or is not accessible")
            print(f"   Error: {str(e)}")
    
    return existing_tables

def get_table_schema(table_name):
    """Get the schema of a table"""
    
    print(f"\n🔍 Checking schema for '{table_name}'")
    print("-" * 30)
    
    client = SupabaseClient()
    
    try:
        # Try to get one record to see the structure
        result = client.supabase.table(table_name).select("*").limit(1).execute()
        
        if result.data and len(result.data) > 0:
            print("📋 Table columns found:")
            for key in result.data[0].keys():
                print(f"   - {key}")
        else:
            print("📋 Table exists but is empty")
            
        return True
        
    except Exception as e:
        print(f"❌ Error checking schema: {str(e)}")
        return False

def populate_document_categories():
    """Populate document_categories table with comprehensive data"""
    
    print("\n📝 Populating Document Categories")
    print("=" * 50)
    
    client = SupabaseClient()
    
    # Comprehensive document categories for railway system
    categories = [
        # Main Categories
        {"name": "Safety & Security", "type": "main_category", "description": "Safety protocols, security measures, and emergency procedures", "sort_order": 1},
        {"name": "Technical Documentation", "type": "main_category", "description": "Technical manuals, specifications, and engineering documents", "sort_order": 2},
        {"name": "Operations", "type": "main_category", "description": "Operational procedures, schedules, and workflows", "sort_order": 3},
        {"name": "Administrative", "type": "main_category", "description": "Administrative policies, reports, and documentation", "sort_order": 4},
        {"name": "Regulatory & Compliance", "type": "main_category", "description": "Regulatory documents, compliance guidelines, and legal requirements", "sort_order": 5},
        {"name": "Training & Development", "type": "main_category", "description": "Training materials, educational resources, and development programs", "sort_order": 6},
        {"name": "Maintenance", "type": "main_category", "description": "Maintenance procedures, schedules, and technical guides", "sort_order": 7},
        {"name": "Infrastructure", "type": "main_category", "description": "Infrastructure documentation, plans, and specifications", "sort_order": 8},
        
        # Safety Sub-categories
        {"name": "Emergency Procedures", "type": "category", "description": "Emergency response and crisis management procedures", "sort_order": 11},
        {"name": "Safety Protocols", "type": "category", "description": "Standard safety protocols and guidelines", "sort_order": 12},
        {"name": "Security Measures", "type": "category", "description": "Security protocols and access control measures", "sort_order": 13},
        {"name": "Accident Reports", "type": "category", "description": "Accident investigation reports and analysis", "sort_order": 14},
        
        # Technical Sub-categories
        {"name": "Rolling Stock", "type": "category", "description": "Documentation related to trains, coaches, and locomotives", "sort_order": 21},
        {"name": "Signaling Systems", "type": "category", "description": "Signal equipment and control systems documentation", "sort_order": 22},
        {"name": "Track & Infrastructure", "type": "category", "description": "Track, bridges, and infrastructure technical documents", "sort_order": 23},
        {"name": "Electrical Systems", "type": "category", "description": "Electrical equipment and power systems documentation", "sort_order": 24},
        
        # Operations Sub-categories
        {"name": "Timetables", "type": "category", "description": "Train schedules and timetable information", "sort_order": 31},
        {"name": "Operating Procedures", "type": "category", "description": "Standard operating procedures and protocols", "sort_order": 32},
        {"name": "Traffic Management", "type": "category", "description": "Traffic control and management procedures", "sort_order": 33},
        {"name": "Station Operations", "type": "category", "description": "Station management and operational procedures", "sort_order": 34},
    ]
    
    success_count = 0
    
    for category in categories:
        try:
            result = client.supabase.table("document_categories").insert(category).execute()
            if result.data:
                print(f"   ✅ Created: {category['name']} ({category['type']})")
                success_count += 1
            else:
                print(f"   ❌ Failed to create: {category['name']}")
        except Exception as e:
            if "duplicate key" in str(e).lower() or "unique constraint" in str(e).lower():
                print(f"   ℹ️  Already exists: {category['name']}")
                success_count += 1
            else:
                print(f"   ❌ Error creating {category['name']}: {str(e)}")
    
    print(f"\n📊 Document Categories: {success_count}/{len(categories)} processed successfully")
    return success_count > 0

def populate_website_categories():
    """Populate website_categories table"""
    
    print("\n📝 Populating Website Categories")
    print("=" * 50)
    
    client = SupabaseClient()
    
    # Website categories for railway-related sites
    categories = [
        {"name": "Railway Booking Platforms", "type": "main_category", "description": "Online railway booking and reservation platforms", "sort_order": 11},
        {"name": "Railway News & Media", "type": "main_category", "description": "News websites covering railway and transportation news", "sort_order": 12},
        {"name": "Tourism & Travel Guides", "type": "main_category", "description": "Travel booking and tourism websites with railway information", "sort_order": 13},
        {"name": "Government Railway Sites", "type": "main_category", "description": "Government websites with railway-related information", "sort_order": 14},
        {"name": "Railway Education", "type": "main_category", "description": "Educational and training websites for railway professionals", "sort_order": 15},
        {"name": "Technical Documentation Sites", "type": "main_category", "description": "Technical documentation and engineering resources", "sort_order": 16},
        {"name": "Industry Publications", "type": "category", "description": "Railway industry publications and journals", "sort_order": 21},
        {"name": "Research Organizations", "type": "category", "description": "Railway research and development organizations", "sort_order": 22},
        {"name": "Equipment Manufacturers", "type": "category", "description": "Railway equipment and technology manufacturers", "sort_order": 23},
        {"name": "Consulting Services", "type": "category", "description": "Railway consulting and professional services", "sort_order": 24},
    ]
    
    success_count = 0
    
    for category in categories:
        try:
            result = client.supabase.table("website_categories").insert(category).execute()
            if result.data:
                print(f"   ✅ Created: {category['name']}")
                success_count += 1
            else:
                print(f"   ❌ Failed to create: {category['name']}")
        except Exception as e:
            if "duplicate key" in str(e).lower() or "unique constraint" in str(e).lower():
                print(f"   ℹ️  Already exists: {category['name']}")
                success_count += 1
            else:
                print(f"   ❌ Error creating {category['name']}: {str(e)}")
    
    print(f"\n📊 Website Categories: {success_count}/{len(categories)} processed successfully")
    return success_count > 0

def main():
    """Main function to check and populate categories"""
    
    print("🚀 Category System Check and Population")
    print("=" * 70)
    
    # Step 1: Check existing tables
    existing_tables = check_existing_tables()
    
    if not existing_tables:
        print("\n❌ No category tables found!")
        print("   Please create the tables first using Supabase dashboard or SQL scripts.")
        return False
    
    # Step 2: Check schemas
    for table in existing_tables:
        get_table_schema(table)
    
    # Step 3: Populate tables
    success = True
    
    if "document_categories" in existing_tables:
        success &= populate_document_categories()
    else:
        print("\n⚠️  document_categories table not found, skipping population")
    
    if "website_categories" in existing_tables:
        success &= populate_website_categories()
    else:
        print("\n⚠️  website_categories table not found, skipping population")
    
    # Step 4: Final summary
    print("\n" + "=" * 70)
    if success:
        print("🎉 Category system populated successfully!")
        print("   You can now test the category management API endpoints.")
    else:
        print("⚠️  Some issues occurred during population.")
        print("   Check the errors above and try manual population if needed.")
    
    return success

if __name__ == "__main__":
    main()
