# Use Python 3.9 slim image
FROM python:3.9-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements for simple server
COPY requirements.simple.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.simple.txt

# Copy simple server file
COPY simple_server.py .
COPY .env .

# Expose port
EXPOSE 8000

# Run the simple server
CMD ["python", "simple_server.py"]
