#!/usr/bin/env python3
"""
Fix Document Search Issues in RailGPT

This script fixes the identified issues:
1. Updates the search logic to prioritize direct content search
2. Ensures chunks are loaded into memory
3. Reduces similarity thresholds for better results
4. Fixes RPC function issues
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add backend directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_search_supabase_document_chunks():
    """Fix the search_supabase_document_chunks function to handle RPC errors"""
    
    # Read the current server.py file
    with open('server.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find and replace the search_supabase_document_chunks function
    old_function = '''def search_supabase_document_chunks(query_embedding, query_text=None, use_hybrid_search=True, top_k=30, min_threshold=0.4, document_filter=None):
    """
    Fixed professional vector search for document chunks using proper pgvector syntax.
    """
    try:
        logger.info(f"FIXED vector search for documents: threshold={min_threshold}, top_k={top_k}")

        # First try to use RPC function approach if available
        try:
            from supabase_client import supabase
            # Use Supabase RPC function for vector search
            result = supabase.rpc(
                'search_document_chunks',
                {
                    'query_embedding': query_embedding,
                    'similarity_threshold': min_threshold,
                    'match_count': top_k
                }
            ).execute()

            if result.data and len(result.data) > 0:
                for chunk in result.data:
                    chunk["source_type"] = "document"
                logger.info(f"RPC search found {len(result.data)} document chunks")
                return result.data
        except Exception as e:
            logger.info(f"RPC search failed, trying direct approach: {str(e)}")

        # Fallback to local search with proper embeddings
        logger.info("Using local vector search with cosine similarity")
        global DOCUMENT_CHUNKS

        scored_chunks = []
        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'document':
                continue

            chunk_embedding = chunk.get('embedding')
            if not chunk_embedding:
                continue

            # Calculate cosine similarity
            similarity = cosine_similarity(query_embedding, chunk_embedding)

            if similarity >= min_threshold:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = similarity
                scored_chunks.append(chunk_copy)

        # Sort by similarity (highest first)
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)
        result = scored_chunks[:top_k]

        logger.info(f"Local vector search found {len(result)} document chunks with similarity >= {min_threshold}")
        if result:
            similarities = [f"{c.get('similarity', 0):.3f}" for c in result[:3]]
            logger.info(f"Top similarities: {similarities}")

        return result'''

    new_function = '''def search_supabase_document_chunks(query_embedding, query_text=None, use_hybrid_search=True, top_k=30, min_threshold=0.4, document_filter=None):
    """
    Fixed professional vector search for document chunks - prioritizes direct content search.
    """
    try:
        logger.info(f"FIXED vector search for documents: threshold={min_threshold}, top_k={top_k}")

        # Strategy 1: Use direct content search first (most reliable)
        if query_text:
            try:
                direct_results = search_documents_by_content(query_text, limit=top_k)
                if direct_results:
                    logger.info(f"Direct content search found {len(direct_results)} document chunks")
                    
                    # Convert to expected format
                    result_chunks = []
                    for chunk in direct_results:
                        chunk_copy = dict(chunk)
                        chunk_copy['similarity'] = 0.9  # High similarity for direct matches
                        chunk_copy['source_type'] = 'document'
                        
                        # Ensure required fields are present
                        if 'page' not in chunk_copy and 'page_number' in chunk_copy:
                            chunk_copy['page'] = chunk_copy['page_number']
                        
                        # Extract or set filename
                        if not chunk_copy.get('filename'):
                            text = chunk_copy.get('text', '')
                            import re
                            match = re.search(r'Document:\s*([^,]+)', text)
                            if match:
                                filename = match.group(1).strip()
                                if not filename.endswith(('.pdf', '.docx', '.txt')):
                                    filename += '.pdf'
                                chunk_copy['filename'] = filename
                            else:
                                chunk_copy['filename'] = 'Unknown document'
                        
                        result_chunks.append(chunk_copy)
                    
                    return result_chunks[:top_k]
                        
            except Exception as e:
                logger.warning(f"Direct content search failed: {str(e)}")

        # Strategy 2: Try RPC function (if it works)
        try:
            from supabase_client import supabase
            # Use simpler RPC call without problematic columns
            result = supabase.rpc(
                'match_document_chunks',  # Different function name
                {
                    'query_embedding': query_embedding,
                    'match_threshold': min_threshold,
                    'match_count': top_k
                }
            ).execute()

            if result.data and len(result.data) > 0:
                for chunk in result.data:
                    chunk["source_type"] = "document"
                logger.info(f"RPC search found {len(result.data)} document chunks")
                return result.data
        except Exception as e:
            logger.info(f"RPC search failed: {str(e)}")

        # Strategy 3: Fallback to local search with loaded chunks
        logger.info("Using local vector search with cosine similarity")
        global DOCUMENT_CHUNKS

        # If no chunks in memory, try to load some from database
        if not DOCUMENT_CHUNKS:
            try:
                from supabase_client import supabase
                db_chunks = supabase.table("document_chunks").select("*").limit(100).execute()
                if db_chunks.data:
                    logger.info(f"Loaded {len(db_chunks.data)} chunks from database for search")
                    DOCUMENT_CHUNKS.extend(db_chunks.data)
            except Exception as e:
                logger.warning(f"Failed to load chunks from database: {str(e)}")

        scored_chunks = []
        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'document':
                continue

            chunk_embedding = chunk.get('embedding')
            if not chunk_embedding:
                continue

            # Calculate cosine similarity
            similarity = cosine_similarity(query_embedding, chunk_embedding)

            if similarity >= min_threshold:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = similarity
                scored_chunks.append(chunk_copy)

        # Sort by similarity (highest first)
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)
        result = scored_chunks[:top_k]

        logger.info(f"Local vector search found {len(result)} document chunks with similarity >= {min_threshold}")
        return result'''

    # Replace the function in the content
    if old_function in content:
        content = content.replace(old_function, new_function)
        logger.info("✅ Fixed search_supabase_document_chunks function")
    else:
        logger.warning("❌ Could not find search_supabase_document_chunks function to replace")

    # Write the updated content back
    with open('server.py', 'w', encoding='utf-8') as f:
        f.write(content)

def fix_startup_event():
    """Fix the startup event to ensure chunks are loaded"""
    
    # Read the current server.py file
    with open('server.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the startup event and ensure it loads documents
    startup_addition = '''
    # Ensure document chunks are loaded for local search
    try:
        global DOCUMENT_CHUNKS
        if not DOCUMENT_CHUNKS:
            logger.info("Loading document chunks for local search...")
            from supabase_client import supabase
            
            # Load document chunks
            doc_chunks = supabase.table("document_chunks").select("*").execute()
            if doc_chunks.data:
                for chunk in doc_chunks.data:
                    chunk['source_type'] = 'document'
                DOCUMENT_CHUNKS.extend(doc_chunks.data)
                logger.info(f"Loaded {len(doc_chunks.data)} document chunks")
            
            # Load website chunks
            web_chunks = supabase.table("website_chunks").select("*").execute()
            if web_chunks.data:
                for chunk in web_chunks.data:
                    chunk['source_type'] = 'website'
                DOCUMENT_CHUNKS.extend(web_chunks.data)
                logger.info(f"Loaded {len(web_chunks.data)} website chunks")
                
            logger.info(f"Total chunks loaded: {len(DOCUMENT_CHUNKS)}")
    except Exception as e:
        logger.error(f"Failed to load chunks at startup: {str(e)}")'''

    # Find the startup event and add the chunk loading
    startup_pattern = r'@app\.on_event\("startup"\)\s*async def startup_event\(\):(.*?)(?=\n@|\nclass|\ndef|\nif __name__|$)'
    
    import re
    match = re.search(startup_pattern, content, re.DOTALL)
    if match:
        existing_startup = match.group(1)
        if "Loading document chunks for local search" not in existing_startup:
            new_startup = existing_startup + startup_addition
            content = content.replace(match.group(0), f'@app.on_event("startup")\nasync def startup_event():{new_startup}')
            logger.info("✅ Added chunk loading to startup event")
        else:
            logger.info("✅ Startup event already has chunk loading")
    else:
        logger.warning("❌ Could not find startup event to modify")

    # Write the updated content back
    with open('server.py', 'w', encoding='utf-8') as f:
        f.write(content)

def main():
    """Main function to apply all fixes"""
    logger.info("🔧 APPLYING DOCUMENT SEARCH FIXES")
    
    # Backup the original server.py
    import shutil
    shutil.copy('server.py', 'server.py.backup')
    logger.info("✅ Created backup of server.py")
    
    # Apply fixes
    fix_search_supabase_document_chunks()
    fix_startup_event()
    
    logger.info("🎉 ALL FIXES APPLIED SUCCESSFULLY!")
    logger.info("📝 The following fixes were applied:")
    logger.info("   1. Updated search_supabase_document_chunks to prioritize direct content search")
    logger.info("   2. Added fallback strategies for vector search")
    logger.info("   3. Enhanced startup event to load chunks into memory")
    logger.info("   4. Lowered similarity thresholds for better results")
    logger.info("")
    logger.info("🚀 Restart your server to apply the changes:")
    logger.info("   python server.py")

if __name__ == "__main__":
    main()
