import requests
from supabase_client import supabase

# Get table chunks directly from database
query = """
SELECT 
    dc.id, 
    dc.text, 
    dc.metadata, 
    dc.content_type,
    d.display_name as filename,
    dc.page_number
FROM document_chunks dc
JOIN documents d ON dc.document_id = d.id
WHERE dc.content_type = 'table'
ORDER BY dc.created_at DESC
LIMIT 3
"""

result = supabase.execute_query(query)

print("=== DATABASE TABLE CHUNKS ===")
for i, chunk in enumerate(result[:3], 1):
    print(f"\nChunk {i}:")
    print(f"  Filename: {chunk.get('filename')}")
    print(f"  Page: {chunk.get('page_number')}")
    print(f"  Content type: {chunk.get('content_type')}")
    
    metadata = chunk.get('metadata', {})
    print(f"  Metadata keys: {list(metadata.keys())}")
    
    if 'table_html' in metadata:
        html = metadata['table_html']
        print(f"  Table HTML length: {len(html)}")
        print(f"  Table HTML preview: {html[:100]}...")
    else:
        print(f"  ❌ Missing table_html in metadata")
        
    if 'table_data' in metadata:
        print(f"  Table data rows: {len(metadata['table_data'])}")
    else:
        print(f"  ❌ Missing table_data in metadata")

print("\n=== TESTING HTML GENERATION ===")
# Test the HTML generation function directly
from document_extractor import format_table_data_as_html

sample_table = [
    ["Column 1", "Column 2", "Column 3"],
    ["Value 1", "Value 2", "Value 3"],
    ["Value 4", "Value 5", "Value 6"]
]

html = format_table_data_as_html(sample_table)
print(f"Generated HTML length: {len(html)}")
print(f"Generated HTML: {html[:200]}...") 