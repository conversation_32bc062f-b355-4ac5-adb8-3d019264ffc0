import requests

response = requests.post("http://localhost:8000/api/query", json={
    "query": "Show me the quotation table for water level sensor",
    "use_hybrid_search": True
})

result = response.json()

print("=== NEW DOCUMENT TEST ===")
print(f"Sources count: {len(result.get('sources', []))}")
print(f"Visual content found: {result.get('visual_content_found')}")

table_sources = 0
for source in result.get('sources', []):
    if source.get('visual_content', {}).get('table_html'):
        table_sources += 1
        print(f"✅ Found table_html in {source.get('filename')} page {source.get('page')}")

print(f"Sources with table_html: {table_sources}") 