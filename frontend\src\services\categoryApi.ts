// Enhanced Category Management API Service
// Supports comprehensive four-level category system for both documents and websites
import { Category, CategoryHierarchy, CategoryCreate, CategoryUpdate, DocumentCategoryUpdate } from '../types/documents';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';
const ENHANCED_API_URL = `${API_URL}/api/enhanced/api/categories`;

// Enhanced API Response interfaces
interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
}

interface CategoryResponse extends ApiResponse<Category> {
  category?: Category;
}

interface CategoryTree {
  categories: CategoryHierarchy[];
  total_count: number;
  max_depth: number;
  statistics: {
    main_categories: number;
    categories: number;
    sub_categories: number;
    minor_categories: number;
    active: number;
    inactive: number;
  };
}

interface BulkOperationResult {
  message: string;
  operation: string;
  results: Array<{
    category_id: string;
    status: 'success' | 'error';
    result?: any;
    error?: string;
  }>;
}

interface CategoryAssignment {
  main_category_id?: string;
  category_id?: string;
  sub_category_id?: string;
  minor_category_id?: string;
}

// Enhanced API Functions

// Get all document categories with enhanced hierarchy and statistics
export const getCategories = async (includeInactive: boolean = false): Promise<CategoryHierarchy[]> => {
  try {
    const url = `${API_URL}/api/categories/`;
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch document categories: ${response.statusText}`);
    }

    const data: CategoryHierarchy[] = await response.json();
    return data;
  } catch (error: any) {
    if (error.message?.includes('fetch') || error.name === 'TypeError') {
      console.warn('Backend server not available. Document categories will be empty until server starts.');
      return [];
    }
    console.error('Error fetching document categories:', error);
    throw error;
  }
};

// Get document categories with full statistics
export const getDocumentCategoriesWithStats = async (includeInactive: boolean = false): Promise<CategoryTree> => {
  try {
    const url = `${ENHANCED_API_URL}/documents?include_inactive=${includeInactive}&tree_format=true`;
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch document categories: ${response.statusText}`);
    }

    const data: CategoryTree = await response.json();
    return data;
  } catch (error: any) {
    if (error.message?.includes('fetch') || error.name === 'TypeError') {
      console.warn('Backend server not available. Document categories will be empty until server starts.');
      return {
        categories: [],
        total_count: 0,
        max_depth: 0,
        statistics: {
          main_categories: 0,
          categories: 0,
          sub_categories: 0,
          minor_categories: 0,
          active: 0,
          inactive: 0
        }
      };
    }
    console.error('Error fetching document categories:', error);
    throw error;
  }
};

// Get all website categories with enhanced hierarchy and statistics
export const getWebsiteCategories = async (includeInactive: boolean = false): Promise<CategoryHierarchy[]> => {
  try {
    const url = `${ENHANCED_API_URL}/websites?include_inactive=${includeInactive}&tree_format=true`;
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch website categories: ${response.statusText}`);
    }

    const data: CategoryTree = await response.json();
    return data.categories;
  } catch (error: any) {
    if (error.message?.includes('fetch') || error.name === 'TypeError') {
      console.warn('Backend server not available. Website categories will be empty until server starts.');
      return [];
    }
    console.error('Error fetching website categories:', error);
    throw error;
  }
};

// Get website categories with full statistics
export const getWebsiteCategoriesWithStats = async (includeInactive: boolean = false): Promise<CategoryTree> => {
  try {
    const url = `${ENHANCED_API_URL}/websites?include_inactive=${includeInactive}&tree_format=true`;
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to fetch website categories: ${response.statusText}`);
    }

    const data: CategoryTree = await response.json();
    return data;
  } catch (error: any) {
    if (error.message?.includes('fetch') || error.name === 'TypeError') {
      console.warn('Backend server not available. Website categories will be empty until server starts.');
      return {
        categories: [],
        total_count: 0,
        max_depth: 0,
        statistics: {
          main_categories: 0,
          categories: 0,
          sub_categories: 0,
          minor_categories: 0,
          active: 0,
          inactive: 0
        }
      };
    }
    console.error('Error fetching website categories:', error);
    throw error;
  }
};

// Get categories by type
export const getCategoriesByType = async (type: string): Promise<Category[]> => {
  try {
    const response = await fetch(`${API_URL}/api/categories/by-type/${type}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch categories by type: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching categories by type:', error);
    throw error;
  }
};

// Get categories by parent
export const getCategoriesByParent = async (parentId: string): Promise<Category[]> => {
  try {
    const response = await fetch(`${API_URL}/api/categories/by-parent/${parentId}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch categories by parent: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching categories by parent:', error);
    throw error;
  }
};

// Enhanced Create Functions

// Create new document category
export const createCategory = async (category: CategoryCreate): Promise<CategoryResponse> => {
  try {
    const response = await fetch(`${API_URL}/api/categories/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(category),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: response.statusText }));
      throw new Error(errorData.detail || `Failed to create document category: ${response.statusText}`);
    }

    const data = await response.json();
    return { success: true, message: 'Category created successfully', category: data };
  } catch (error: any) {
    if (error.message?.includes('fetch') || error.name === 'TypeError') {
      throw new Error('Backend server not available. Please start the backend server to create categories.');
    }
    console.error('Error creating document category:', error);
    throw error;
  }
};

// Create new website category
export const createWebsiteCategory = async (category: CategoryCreate): Promise<CategoryResponse> => {
  try {
    const response = await fetch(`${ENHANCED_API_URL}/websites`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(category),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: response.statusText }));
      throw new Error(errorData.detail || `Failed to create website category: ${response.statusText}`);
    }

    const data = await response.json();
    return { success: true, message: 'Category created successfully', category: data };
  } catch (error: any) {
    if (error.message?.includes('fetch') || error.name === 'TypeError') {
      throw new Error('Backend server not available. Please start the backend server to create categories.');
    }
    console.error('Error creating website category:', error);
    throw error;
  }
};

// Update category
export const updateCategory = async (categoryId: string, categoryUpdate: CategoryUpdate): Promise<ApiResponse<void>> => {
  try {
    const response = await fetch(`${API_URL}/api/categories/${categoryId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(categoryUpdate),
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: response.statusText }));
      throw new Error(errorData.detail || `Failed to update category: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error updating category:', error);
    throw error;
  }
};

// Enhanced Delete Functions

// Delete document category
export const deleteCategory = async (categoryId: string, force: boolean = false): Promise<ApiResponse<void>> => {
  try {
    const url = `${API_URL}/api/categories/${categoryId}${force ? '?force=true' : ''}`;
    const response = await fetch(url, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: response.statusText }));
      throw new Error(errorData.detail || `Failed to delete document category: ${response.statusText}`);
    }

    const data = await response.json();
    return { success: true, message: data.message };
  } catch (error) {
    console.error('Error deleting document category:', error);
    throw error;
  }
};

// Delete website category
export const deleteWebsiteCategory = async (categoryId: string, force: boolean = false): Promise<ApiResponse<void>> => {
  try {
    const url = `${ENHANCED_API_URL}/websites/${categoryId}${force ? '?force=true' : ''}`;
    const response = await fetch(url, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: response.statusText }));
      throw new Error(errorData.detail || `Failed to delete website category: ${response.statusText}`);
    }

    const data = await response.json();
    return { success: true, message: data.message };
  } catch (error) {
    console.error('Error deleting website category:', error);
    throw error;
  }
};

// Enhanced Bulk Operations

// Perform bulk operations on categories
export const bulkCategoryOperation = async (
  categoryIds: string[],
  operation: 'activate' | 'deactivate' | 'delete' | 'move',
  tableType: 'documents' | 'websites' = 'documents',
  targetParentId?: string
): Promise<BulkOperationResult> => {
  try {
    const response = await fetch(`${ENHANCED_API_URL}/bulk-operation?table_type=${tableType}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        category_ids: categoryIds,
        operation,
        target_parent_id: targetParentId
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: response.statusText }));
      throw new Error(errorData.detail || `Failed to perform bulk operation: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error performing bulk operation:', error);
    throw error;
  }
};

// Enhanced Category Assignment Functions

// Assign categories to documents
export const assignDocumentCategories = async (
  documentIds: string[],
  categories: CategoryAssignment
): Promise<ApiResponse<void>> => {
  try {
    const response = await fetch(`${ENHANCED_API_URL}/assign/documents`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        document_ids: documentIds,
        categories
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: response.statusText }));
      throw new Error(errorData.detail || `Failed to assign document categories: ${response.statusText}`);
    }

    const data = await response.json();
    return { success: true, message: data.message };
  } catch (error) {
    console.error('Error assigning document categories:', error);
    throw error;
  }
};

// Assign categories to websites
export const assignWebsiteCategories = async (
  websiteIds: string[],
  categories: CategoryAssignment
): Promise<ApiResponse<void>> => {
  try {
    const response = await fetch(`${ENHANCED_API_URL}/assign/websites`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        website_ids: websiteIds,
        categories
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: response.statusText }));
      throw new Error(errorData.detail || `Failed to assign website categories: ${response.statusText}`);
    }

    const data = await response.json();
    return { success: true, message: data.message };
  } catch (error) {
    console.error('Error assigning website categories:', error);
    throw error;
  }
};

// Get category hierarchy for dropdown/selection purposes
export const getCategoryHierarchy = async (
  tableType: 'documents' | 'websites',
  parentId?: string
): Promise<{ categories: Array<{ id: string; name: string; type: string; parent_id?: string; full_path: string; level: number }>; total: number; parent_id?: string }> => {
  try {
    const url = tableType === 'documents' 
      ? `${API_URL}/api/categories/` 
      : `${API_URL}/api/categories/website`;
    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Failed to get category hierarchy: ${response.statusText}`);
    }

    const data = await response.json();
    return { categories: data, total: data.length, parent_id: parentId };
  } catch (error) {
    console.error('Error getting category hierarchy:', error);
    throw error;
  }
};

// Update document categories
export const updateDocumentCategories = async (
  documentId: string, 
  categoryUpdate: DocumentCategoryUpdate
): Promise<ApiResponse<void>> => {
  try {
    const response = await fetch(`${API_URL}/api/categories/documents/${documentId}/categories`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(categoryUpdate),
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: response.statusText }));
      throw new Error(errorData.detail || `Failed to update document categories: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error updating document categories:', error);
    throw error;
  }
};

// Get category path
export const getCategoryPath = async (categoryId: string): Promise<string> => {
  try {
    const response = await fetch(`${API_URL}/api/categories/${categoryId}/path`);
    
    if (!response.ok) {
      throw new Error(`Failed to get category path: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.path;
  } catch (error) {
    console.error('Error getting category path:', error);
    throw error;
  }
};

// Helper function to build category hierarchy tree from flat list
export const buildCategoryTree = (categories: CategoryHierarchy[]): CategoryHierarchy[] => {
  const categoryMap = new Map<string, CategoryHierarchy>();
  const rootCategories: CategoryHierarchy[] = [];

  // First pass: create map of all categories
  categories.forEach(category => {
    categoryMap.set(category.id, { ...category, children: [] });
  });

  // Second pass: build tree structure
  categories.forEach(category => {
    const categoryNode = categoryMap.get(category.id)!;
    
    if (category.parent_id) {
      const parent = categoryMap.get(category.parent_id);
      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(categoryNode);
      }
    } else {
      rootCategories.push(categoryNode);
    }
  });

  // Sort categories by sort_order and name
  const sortCategories = (cats: CategoryHierarchy[]) => {
    cats.sort((a, b) => {
      if (a.sort_order !== b.sort_order) {
        return a.sort_order - b.sort_order;
      }
      return a.name.localeCompare(b.name);
    });
    
    cats.forEach(cat => {
      if (cat.children && cat.children.length > 0) {
        sortCategories(cat.children);
      }
    });
  };

  sortCategories(rootCategories);
  return rootCategories;
};

// Helper function to get all categories of a specific type from hierarchy
export const getCategoriesOfType = (
  categories: CategoryHierarchy[], 
  type: string
): CategoryHierarchy[] => {
  const result: CategoryHierarchy[] = [];
  
  const traverse = (cats: CategoryHierarchy[]) => {
    cats.forEach(cat => {
      if (cat.type === type) {
        result.push(cat);
      }
      if (cat.children) {
        traverse(cat.children);
      }
    });
  };
  
  traverse(categories);
  return result;
};

// Helper function to find category by ID in hierarchy
export const findCategoryById = (
  categories: CategoryHierarchy[], 
  id: string
): CategoryHierarchy | null => {
  for (const category of categories) {
    if (category.id === id) {
      return category;
    }
    if (category.children) {
      const found = findCategoryById(category.children, id);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

// Helper function to get category options for dropdowns
export const getCategoryOptions = (
  categories: CategoryHierarchy[],
  type?: string,
  parentId?: string
): Array<{ value: string; label: string; fullPath: string }> => {
  const options: Array<{ value: string; label: string; fullPath: string }> = [];
  
  const traverse = (cats: CategoryHierarchy[], level = 0) => {
    cats.forEach(cat => {
      // Filter by type if specified
      if (type && cat.type !== type) {
        if (cat.children) {
          traverse(cat.children, level);
        }
        return;
      }
      
      // Filter by parent if specified
      if (parentId && cat.parent_id !== parentId) {
        if (cat.children) {
          traverse(cat.children, level);
        }
        return;
      }
      
      const indent = '  '.repeat(level);
      options.push({
        value: cat.id,
        label: `${indent}${cat.name}`,
        fullPath: cat.full_path
      });
      
      if (cat.children) {
        traverse(cat.children, level + 1);
      }
    });
  };
  
  traverse(categories);
  return options;
};
