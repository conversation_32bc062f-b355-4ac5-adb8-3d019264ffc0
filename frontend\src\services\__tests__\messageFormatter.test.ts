import {
  formatMessageContent,
  extractDocumentSources,
  extractWebsiteSources,
  extractAllSources
} from '../messageFormatter';

describe('messageFormatter', () => {
  describe('formatMessageContent', () => {
    it('should return document_answer if available', () => {
      const response = { document_answer: 'Document answer', content: 'Content' };
      expect(formatMessageContent(response)).toBe('Document answer');
    });

    it('should fall back to website_answer if document_answer is not available', () => {
      const response = { website_answer: 'Website answer', content: 'Content' };
      expect(formatMessageContent(response)).toBe('Website answer');
    });

    it('should fall back to answer if more specific answers are not available', () => {
      const response = { answer: 'General answer', content: 'Content' };
      expect(formatMessageContent(response)).toBe('General answer');
    });

    it('should fall back to content if no specific answers are available', () => {
      const response = { content: 'Content' };
      expect(formatMessageContent(response)).toBe('Content');
    });

    it('should return empty string if no content is available', () => {
      const response = {};
      expect(formatMessageContent(response)).toBe('');
    });
  });

  describe('extractDocumentSources', () => {
    it('should extract document_sources if available', () => {
      const response = {
        document_sources: [
          { id: '1', document_id: 'doc1', filename: 'test.pdf', page_numbers: [1, 2] }
        ]
      };
      const result = extractDocumentSources(response);
      expect(result).toHaveLength(1);
      expect(result[0].document_id).toBe('doc1');
      expect(result[0].filename).toBe('test.pdf');
      expect(result[0].page_numbers).toEqual([1, 2]);
    });

    it('should normalize document sources correctly', () => {
      const response = {
        document_sources: [
          { document_id: 'doc1', pages: 5 } // Not an array
        ]
      };
      const result = extractDocumentSources(response);
      expect(result[0].page_numbers).toEqual([5]); // Should be converted to array
      expect(result[0].filename).toBe('Document'); // Default value
    });

    it('should fall back to sources if document_sources is not available', () => {
      const response = {
        sources: [
          { document_id: 'doc1', filename: 'doc1.pdf' },
          { website_id: 'web1', url: 'http://example.com' } // Should be filtered out
        ]
      };
      const result = extractDocumentSources(response);
      expect(result).toHaveLength(1);
      expect(result[0].document_id).toBe('doc1');
    });
  });

  describe('extractWebsiteSources', () => {
    it('should extract website_sources if available', () => {
      const response = {
        website_sources: [
          { id: '1', website_id: 'web1', url: 'http://example.com', title: 'Example' }
        ]
      };
      const result = extractWebsiteSources(response);
      expect(result).toHaveLength(1);
      expect(result[0].website_id).toBe('web1');
      expect(result[0].url).toBe('http://example.com');
      expect(result[0].title).toBe('Example');
    });

    it('should fall back to sources if website_sources is not available', () => {
      const response = {
        sources: [
          { document_id: 'doc1', filename: 'doc1.pdf' }, // Should be filtered out
          { website_id: 'web1', url: 'http://example.com' }
        ]
      };
      const result = extractWebsiteSources(response);
      expect(result).toHaveLength(1);
      expect(result[0].website_id).toBe('web1');
    });
  });

  describe('extractAllSources', () => {
    it('should extract both document and website sources', () => {
      const response = {
        document_sources: [
          { id: 'd1', document_id: 'doc1', filename: 'test.pdf' }
        ],
        website_sources: [
          { id: 'w1', website_id: 'web1', url: 'http://example.com' }
        ]
      };

      const { documentSources, websiteSources, allSources } = extractAllSources(response);

      expect(documentSources).toHaveLength(1);
      expect(websiteSources).toHaveLength(1);
      expect(allSources).toHaveLength(2);

      // Verify document source in allSources
      const docSource = allSources.find(s => s.document_id === 'doc1');
      expect(docSource).toBeDefined();
      expect(docSource?.filename).toBe('test.pdf');

      // Verify website source in allSources
      const webSource = allSources.find(s => s.website_id === 'web1');
      expect(webSource).toBeDefined();
      expect(webSource?.url).toBe('http://example.com');
    });

    it('should handle when no sources are present', () => {
      const response = { content: 'Some content without sources' };

      const { documentSources, websiteSources, allSources } = extractAllSources(response);

      expect(documentSources).toHaveLength(0);
      expect(websiteSources).toHaveLength(0);
      expect(allSources).toHaveLength(0);
    });
  });
});
