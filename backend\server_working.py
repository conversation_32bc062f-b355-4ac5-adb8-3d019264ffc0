import os
import re
import logging
import numpy as np
import shutil
import time
import uuid
import json
import random
from typing import List, Dict, Any, Optional, Tuple
import fitz  # PyMuPDF
from fastapi import FastAPI, HTTPException, Depends, Request, BackgroundTasks, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse, RedirectResponse
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Import custom modules
from website_scraper import extract_website_text
from document_extractor import extract_document, extract_document_with_visual_content
from vector_db import vector_db  # Import the vector database
import llm_router  # Import the new LLM router module
from feedback import FeedbackData, send_feedback_email, get_feedback_emails, update_feedback_emails, FeedbackEmailConfig  # Import feedback module
from supabase_client import supabase  # Import Supabase client
from config import config  # Import secure configuration
import vector_search  # Import standardized vector search

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Configure LLM models through the router
available_models = llm_router.get_available_models()
logger.info(f"Available LLM models: {[model['id'] for model in available_models]}")

# For backward compatibility
api_key = os.getenv("GEMINI_API_KEY")
if not api_key:
    logger.warning("GEMINI_API_KEY not found in environment variables. Using mock embeddings.")

# Global variable to store document chunks with embeddings
DOCUMENT_CHUNKS = []
# Priority weights for different source types
DOCUMENT_PRIORITY_WEIGHT = 2.5  # Higher priority for documents
WEBSITE_PRIORITY_WEIGHT = 1.5   # Medium priority for websites
# Threshold for considering document chunks relevant
RELEVANCE_THRESHOLD = 0.05  # Very low threshold to prioritize documents

# Create FastAPI app
app = FastAPI(title="RailGPT Document Management System")

# Import and include category management router
try:
    from category_management import router as category_router
    app.include_router(category_router)
    logger.info("Category management router included successfully")
except ImportError as e:
    logger.warning(f"Could not import category management router: {e}")
except Exception as e:
    logger.warning(f"Error including category management router: {e}")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Custom middleware to increase file upload size limit
@app.middleware("http")
async def add_custom_upload_limit(request: Request, call_next):
    # Set a large upload limit (200MB)
    request.scope.setdefault("_body_size_limit", 200 * 1024 * 1024)  # 200MB
    response = await call_next(request)
    return response

# Define API request and response models
class QueryRequest(BaseModel):
    query: str
    model: Optional[str] = "gemini-2.0-flash"
    fallback_enabled: Optional[bool] = True
    extract_format: Optional[str] = "paragraph"
    use_hybrid_search: Optional[bool] = True
    retry_on_timeout: Optional[bool] = True
    context_mode: Optional[str] = "flexible"

class WebsiteScrapeRequest(BaseModel):
    url: str

class WebsiteAddRequest(BaseModel):
    url: str
    submitted_by: Optional[str] = None
    role: Optional[str] = None
    follow_links: Optional[bool] = False
    extraction_depth: Optional[int] = 1
    extract_images: Optional[bool] = False
    extract_tables: Optional[bool] = True
    max_pages: Optional[int] = 10
    extractor_type: Optional[str] = "trafilatura"
    domain_category: Optional[str] = "general"
    embedding_model: Optional[str] = "gemini-2.0-flash"
    # Hierarchical category fields
    main_category: Optional[str] = None
    category: Optional[str] = None
    sub_category: Optional[str] = None
    minor_category: Optional[str] = None

class Source(BaseModel):
    source_type: str  # "document" or "website"
    # For documents
    filename: Optional[str] = None
    name: Optional[str] = None  # Display name for the document
    page: Optional[int] = None
    # For websites
    url: Optional[str] = None
    link: Optional[str] = None  # For document viewer links
    # For visual content
    content_type: Optional[str] = None  # "text", "table", "image", "chart_diagram"
    visual_content: Optional[Dict[str, Any]] = None  # Visual content metadata
    storage_url: Optional[str] = None  # URL for stored visual content
    display_type: Optional[str] = None  # "text", "html_table", "image", "base64_image"

class QueryResponse(BaseModel):
    answer: str  # Combined answer from all sources
    document_answer: Optional[str] = None  # Answer only from document sources
    website_answer: Optional[str] = None  # Answer only from website sources
    sources: List[Source]  # All sources
    document_sources: Optional[List[Source]] = None  # Only document sources
    website_sources: Optional[List[Source]] = None  # Only website sources
    llm_model: Optional[str] = None  # The LLM model used for generating the answer
    llm_fallback: Optional[bool] = False  # Whether the answer was generated using LLM fallback
    visual_content_found: Optional[bool] = False  # Whether visual content was found
    visual_content_types: Optional[List[str]] = None  # Types of visual content found

class ChunkData(BaseModel):
    filename: str
    page: int
    chunk_id: str
    text: str
    embedding: Optional[List[float]] = None

# Document processing functions
def clean_text(text: str) -> str:
    """Clean text for processing."""
    return " ".join(text.split()) if text else ""

def generate_embedding(text: str, model_id: str = "gemini-2.0-flash") -> List[float]:
    """Generate embedding vector for text using the LLM router."""
    try:
        # Use the LLM router to generate embeddings
        return llm_router.generate_embedding(text, model_id)
    except Exception as e:
        logger.error(f"Error generating embedding with {model_id}: {str(e)}")
        # Try with a different model before falling back to random
        try:
            logger.info(f"Retrying embedding generation with default model")
            return llm_router.generate_embedding(text, llm_router.DEFAULT_MODEL)
        except Exception as fallback_error:
            logger.error(f"Fallback embedding generation also failed: {str(fallback_error)}")
            # Fallback to a consistent embedding rather than random
            np.random.seed(hash(text) % 2**32)
            return list(np.random.rand(768))

def cosine_similarity(embedding1, embedding2):
    """Calculate cosine similarity between two embeddings, handling string conversions."""
    try:
        # Handle string embeddings (from JSON)
        if isinstance(embedding1, str):
            try:
                embedding1 = json.loads(embedding1)
            except:
                logger.error("Failed to parse string embedding1")
                return 0.0

        if isinstance(embedding2, str):
            try:
                embedding2 = json.loads(embedding2)
            except:
                logger.error("Failed to parse string embedding2")
                return 0.0

        # Ensure embeddings are numpy arrays of float32
        embedding1 = np.array(embedding1, dtype=np.float32)
        embedding2 = np.array(embedding2, dtype=np.float32)

        # Compute cosine similarity
        dot_product = np.dot(embedding1, embedding2)
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)
    except Exception as e:
        logger.error(f"Error calculating cosine similarity: {str(e)}")
        return 0.0  # Return 0 similarity on error

# Initialize Supabase connection
try:
    if supabase:
        logger.info("Supabase client initialized successfully")
        # Test connection and setup storage if needed
        try:
            # Test basic connection
            test_result = supabase.execute_query("SELECT 1 as test")
            if test_result:
                logger.info("Supabase database connection verified")
                
                # Setup storage buckets if they don't exist
                try:
                    # Create additional buckets if needed
                    supabase.create_bucket_if_not_exists("websites", is_public=True)
                    supabase.create_bucket_if_not_exists("images", is_public=True)
                except Exception as e:
                    logger.warning(f"Error setting up Supabase storage: {str(e)}")
                    logger.info("Will use mock storage functionality when needed.")
    except Exception as e:
        logger.error(f"Error checking Supabase configuration: {str(e)}")

# Root endpoint
@app.get("/")
async def read_root():
    return {"message": "RailGPT Document Management System API is running"}

# Health check endpoint
@app.get("/api/health")
async def health_check():
    return {"status": "ok", "message": "RailGPT API is running"}

# Alternative health check endpoint (for compatibility)
@app.get("/health")
async def health_check_alt():
    return {"status": "ok", "message": "RailGPT API is running"}

# Main query endpoint - Core RailGPT functionality
@app.post("/api/query", response_model=QueryResponse)
async def query_documents(request: QueryRequest):
    """
    Main query endpoint that implements the RailGPT answer logic:
    1st priority: uploaded documents
    2nd priority: extracted websites
    3rd priority: LLM model if no answer found
    Display both cards if answer found in both sources
    """
    query = request.query
    model = request.model or "gemini-2.0-flash"
    fallback_enabled = request.fallback_enabled

    logger.info(f"Processing query: '{query}' with model: {model}")

    try:
        # Step 1: Search documents (1st priority)
        document_results = []
        document_answer = None

        try:
            # Generate embedding for vector search
            query_embedding = generate_embedding(query, model)

            # Search documents using multiple strategies
            document_results = search_documents_in_supabase(query, limit=10)

            if document_results:
                logger.info(f"Found {len(document_results)} document results")

                # Generate answer from document sources
                document_context = "\n\n".join([
                    f"From {result.get('filename', 'Unknown')}: {result.get('text', '')}"
                    for result in document_results[:5]
                ])

                if document_context.strip():
                    document_answer = await generate_answer_from_context(
                        query, document_context, model, "documents"
                    )

        except Exception as e:
            logger.error(f"Error searching documents: {str(e)}")

        # Step 2: Search websites (2nd priority)
        website_results = []
        website_answer = None

        try:
            # Search websites
            website_results = search_websites_in_supabase(query, limit=10)

            if website_results:
                logger.info(f"Found {len(website_results)} website results")

                # Generate answer from website sources
                website_context = "\n\n".join([
                    f"From {result.get('url', 'Unknown')}: {result.get('text', '')}"
                    for result in website_results[:5]
                ])

                if website_context.strip():
                    website_answer = await generate_answer_from_context(
                        query, website_context, model, "websites"
                    )

        except Exception as e:
            logger.error(f"Error searching websites: {str(e)}")

        # Step 3: Determine final answer based on priority logic
        final_answer = ""
        llm_fallback = False

        if document_answer and website_answer:
            # Both sources found - combine answers
            final_answer = f"**From Documents:**\n{document_answer}\n\n**From Websites:**\n{website_answer}"
        elif document_answer:
            # Documents found (1st priority)
            final_answer = document_answer
        elif website_answer:
            # Websites found (2nd priority)
            final_answer = website_answer
        elif fallback_enabled:
            # No sources found - use LLM fallback (3rd priority)
            logger.info("No relevant sources found, using LLM fallback")
            final_answer = await generate_llm_fallback_answer(query, model)
            llm_fallback = True
        else:
            final_answer = "I couldn't find relevant information in the uploaded documents or indexed websites for your query."

        # Format sources for response
        document_sources = format_document_sources(document_results)
        website_sources = format_website_sources(website_results)
        all_sources = document_sources + website_sources

        return QueryResponse(
            answer=final_answer,
            document_answer=document_answer,
            website_answer=website_answer,
            sources=all_sources,
            document_sources=document_sources,
            website_sources=website_sources,
            llm_model=model,
            llm_fallback=llm_fallback
        )

    except Exception as e:
        logger.error(f"Error processing query: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing query: {str(e)}")

async def generate_answer_from_context(query: str, context: str, model: str, source_type: str) -> str:
    """Generate answer from provided context using LLM."""
    try:
        prompt = f"""Based on the following {source_type} content, please answer the user's question.

Question: {query}

Content:
{context}

Please provide a clear, accurate answer based only on the information provided above. If the information is not sufficient to answer the question, say so."""

        response = await llm_router.generate_response(prompt, model)
        return response.strip()

    except Exception as e:
        logger.error(f"Error generating answer from context: {str(e)}")
        return f"Error generating answer from {source_type}: {str(e)}"

async def generate_llm_fallback_answer(query: str, model: str) -> str:
    """Generate fallback answer using LLM when no sources are found."""
    try:
        prompt = f"""You are RailGPT, an AI assistant for Indian Railways. Please answer the following question about railways, trains, or transportation:

Question: {query}

Please provide a helpful answer based on your knowledge of Indian Railways and transportation systems."""

        response = await llm_router.generate_response(prompt, model)
        return response.strip()

    except Exception as e:
        logger.error(f"Error generating LLM fallback answer: {str(e)}")
        return "I apologize, but I'm unable to process your query at the moment. Please try again later."

# Search functions
def search_documents_in_supabase(query: str, limit: int = 10) -> List[Dict[str, Any]]:
    """Search documents using multiple strategies."""
    try:
        logger.info(f"Searching documents in Supabase for: '{query}' (limit: {limit})")

        # Try content-based search first
        content_results = search_documents_by_content(query, limit)
        if content_results:
            logger.info(f"Content search found {len(content_results)} results")
            return content_results

        # Try vector search if available
        try:
            query_embedding = generate_embedding(query)
            if query_embedding:
                vector_results = search_supabase_document_chunks(
                    query_embedding=query_embedding,
                    query_text=query,
                    use_hybrid_search=True,
                    top_k=limit,
                    min_threshold=0.1
                )
                if vector_results:
                    logger.info(f"Vector search found {len(vector_results)} results")
                    return vector_results
        except Exception as e:
            logger.warning(f"Vector search failed: {str(e)}")

        # Try text-based search as fallback
        text_results = text_based_document_search(query, limit)
        if text_results:
            logger.info(f"Text-based search found {len(text_results)} results")
            return text_results

        logger.info("No document results found with any search method")
        return []

    except Exception as e:
        logger.error(f"Error in search_documents_in_supabase: {str(e)}")
        return []

def search_websites_in_supabase(query: str, limit: int = 10) -> List[Dict[str, Any]]:
    """Search websites using multiple strategies."""
    try:
        logger.info(f"Searching websites in Supabase for: '{query}' (limit: {limit})")

        # Try vector search first
        try:
            query_embedding = generate_embedding(query)
            if query_embedding:
                vector_results = search_supabase_website_chunks(
                    query_embedding=query_embedding,
                    query_text=query,
                    use_hybrid_search=True,
                    top_k=limit,
                    min_threshold=0.1
                )
                if vector_results:
                    logger.info(f"Website vector search found {len(vector_results)} results")
                    return vector_results
        except Exception as e:
            logger.warning(f"Website vector search failed: {str(e)}")

        # Try text-based search as fallback
        text_results = text_based_website_search(query, limit)
        if text_results:
            logger.info(f"Website text-based search found {len(text_results)} results")
            return text_results

        logger.info("No website results found with any search method")
        return []

    except Exception as e:
        logger.error(f"Error in search_websites_in_supabase: {str(e)}")
        return []

def search_documents_by_content(query: str, limit: int = 10):
    """Search for documents by content using text search."""
    logger.info(f"Searching for documents with content containing '{query}'...")

    # Clean the query and extract key terms
    import re
    from string import punctuation

    clean_query = re.sub(r'[' + punctuation + ']', ' ', query.lower())
    words = clean_query.split()
    stop_words = {'what', 'is', 'the', 'of', 'in', 'on', 'at', 'and', 'or', 'for', 'to', 'a', 'an', 'by', 'with', 'about'}
    key_terms = [word for word in words if word not in stop_words and len(word) > 2]

    if not key_terms:
        key_terms = [query]

    logger.info(f"Extracted key terms for content search: {key_terms}")

    # Create a tsquery string with OR operators between terms
    ts_query_terms = " | ".join([term.replace("'", "''") for term in key_terms])
    sanitized_full_query = query.replace("'", "''")

    # Prepare the query with both exact phrase matching and key term matching
    search_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        d.display_name as filename,
        d.file_path as url,
        'document' as source_type,
        GREATEST(
            ts_rank(to_tsvector('english', dc.text), to_tsquery('english', '{ts_query_terms}')),
            ts_rank(to_tsvector('english', dc.text), plainto_tsquery('english', '{sanitized_full_query}'))
        ) AS similarity
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        to_tsvector('english', dc.text) @@ to_tsquery('english', '{ts_query_terms}')
        OR to_tsvector('english', dc.text) @@ plainto_tsquery('english', '{sanitized_full_query}')
        OR dc.text ILIKE '%{sanitized_full_query}%'
    ORDER BY
        similarity DESC
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(search_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching documents by content: {result['error']}")
            return []

        logger.info(f"Found {len(result)} document chunks matching '{query}'")
        return result
    except Exception as e:
        logger.error(f"Error searching documents by content: {str(e)}")
        return []
