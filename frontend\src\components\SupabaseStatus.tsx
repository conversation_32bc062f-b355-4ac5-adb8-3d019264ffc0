import React, { useEffect, useState } from 'react';
import { testSupabaseConnection } from '../services/supabase';

const SupabaseStatus: React.FC = () => {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState<boolean>(true);
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    const checkConnection = async () => {
      setIsChecking(true);
      try {
        const connected = await testSupabaseConnection();
        setIsConnected(connected);
        if (!connected) {
          setErrorMessage('Could not connect to database. Some features may not work.');
        }
      } catch (error) {
        setIsConnected(false);
        setErrorMessage(
          error instanceof Error 
            ? `Connection error: ${error.message}` 
            : 'Unknown connection error'
        );
      } finally {
        setIsChecking(false);
      }
    };

    checkConnection();
  }, []);

  if (isChecking) {
    return (
      <div className="flex items-center p-2 text-sm text-gray-600">
        <div className="animate-pulse w-3 h-3 bg-yellow-400 rounded-full mr-2"></div>
        Checking database connection...
      </div>
    );
  }

  if (isConnected === false) {
    return (
      <div className="flex items-center p-2 text-sm text-red-600 bg-red-50 rounded-md">
        <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
        <div>
          <span className="font-medium">Database disconnected:</span> {errorMessage}
        </div>
      </div>
    );
  }

  if (isConnected === true) {
    return (
      <div className="flex items-center p-1 text-xs text-green-600">
        <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
        Database connected
      </div>
    );
  }

  return null;
};

export default SupabaseStatus;
