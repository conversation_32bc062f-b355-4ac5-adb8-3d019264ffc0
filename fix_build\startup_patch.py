#!/usr/bin/env python3
"""
Direct startup patch for RailGPT Supabase client issue
"""
import os
import sys
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def patch_supabase_client():
    """
    Apply patch to the Supabase client directly in the installed package
    """
    try:
        # Find the Supabase client module
        import supabase
        module_path = os.path.dirname(supabase.__file__)
        logger.info(f"Supabase module path: {module_path}")

        # Path to the sync client file
        sync_client_path = os.path.join(module_path, "_sync", "client.py")
        if not os.path.exists(sync_client_path):
            logger.error(f"Could not find Supabase sync client at: {sync_client_path}")
            return False
            
        # Read the current content
        with open(sync_client_path, 'r') as f:
            content = f.read()
            
        # Create backup
        with open(f"{sync_client_path}.bak", 'w') as f:
            f.write(content)
            
        # Modify the create method to remove the proxy parameter
        if "def create(cls, supabase_url, supabase_key, options" in content:
            modified_content = content.replace(
                "def create(cls, supabase_url, supabase_key, options",
                "def create(cls, supabase_url, supabase_key, options=None"
            )
            
            # Check if 'proxy' is passed to the class constructor
            if "client = cls(supabase_url, supabase_key, options)" in modified_content:
                # Create a filtered options dictionary without 'proxy'
                modified_content = modified_content.replace(
                    "client = cls(supabase_url, supabase_key, options)",
                    "filtered_options = {} if options is None else {k: v for k, v in options.items() if k != 'proxy'}\n        client = cls(supabase_url, supabase_key, filtered_options)"
                )
                
            # Write the modified content
            with open(sync_client_path, 'w') as f:
                f.write(modified_content)
                
            logger.info(f"Successfully patched Supabase client at: {sync_client_path}")
            return True
        else:
            logger.error("Could not find the create method in the Supabase client code")
            return False
    except Exception as e:
        logger.error(f"Error patching Supabase client: {e}")
        return False

def main():
    print("Starting RailGPT Supabase client patch...")
    success = patch_supabase_client()
    print(f"Patch {'succeeded' if success else 'failed'}")
    
if __name__ == "__main__":
    main()
