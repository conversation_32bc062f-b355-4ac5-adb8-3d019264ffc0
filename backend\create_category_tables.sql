-- Create Category Tables for RailGPT
-- Run this SQL in your Supabase SQL Editor

-- Create document_categories table
CREATE TABLE IF NOT EXISTS document_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('main_category', 'category', 'sub_category', 'minor_category')),
    parent_id UUID REFERENCES document_categories(id) ON DELETE CASCADE,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique names at the same level
    UNIQUE(name, parent_id)
);

-- Create website_categories table
CREATE TABLE IF NOT EXISTS website_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_document_categories_parent_id ON document_categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_document_categories_type ON document_categories(type);
CREATE INDEX IF NOT EXISTS idx_document_categories_is_active ON document_categories(is_active);
CREATE INDEX IF NOT EXISTS idx_document_categories_sort_order ON document_categories(sort_order);

CREATE INDEX IF NOT EXISTS idx_website_categories_is_active ON website_categories(is_active);
CREATE INDEX IF NOT EXISTS idx_website_categories_sort_order ON website_categories(sort_order);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
DROP TRIGGER IF EXISTS update_document_categories_updated_at ON document_categories;
CREATE TRIGGER update_document_categories_updated_at
    BEFORE UPDATE ON document_categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_website_categories_updated_at ON website_categories;
CREATE TRIGGER update_website_categories_updated_at
    BEFORE UPDATE ON website_categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Insert some default document categories
INSERT INTO document_categories (id, name, type, parent_id, description, sort_order) VALUES
    ('550e8400-e29b-41d4-a716-************', 'Railway Operations', 'main_category', NULL, 'Main category for railway operations', 1),
    ('************************************', 'Safety & Security', 'main_category', NULL, 'Main category for safety and security', 2),
    ('550e8400-e29b-41d4-a716-************', 'Technical Documentation', 'main_category', NULL, 'Main category for technical documents', 3),
    ('550e8400-e29b-41d4-a716-************', 'Administrative', 'main_category', NULL, 'Main category for administrative documents', 4)
ON CONFLICT (name, parent_id) DO NOTHING;

-- Insert subcategories for Railway Operations
INSERT INTO document_categories (id, name, type, parent_id, description, sort_order) VALUES
    ('550e8400-e29b-41d4-a716-446655440011', 'Train Operations', 'category', '550e8400-e29b-41d4-a716-************', 'Documents related to train operations', 1),
    ('550e8400-e29b-41d4-a716-446655440012', 'Station Management', 'category', '550e8400-e29b-41d4-a716-************', 'Documents related to station management', 2),
    ('550e8400-e29b-41d4-a716-446655440013', 'Track Maintenance', 'category', '550e8400-e29b-41d4-a716-************', 'Documents related to track maintenance', 3)
ON CONFLICT (name, parent_id) DO NOTHING;

-- Insert subcategories for Safety & Security
INSERT INTO document_categories (id, name, type, parent_id, description, sort_order) VALUES
    ('550e8400-e29b-41d4-a716-************', 'Safety Protocols', 'category', '************************************', 'Safety protocol documents', 1),
    ('550e8400-e29b-41d4-a716-************', 'Emergency Procedures', 'category', '************************************', 'Emergency procedure documents', 2),
    ('550e8400-e29b-41d4-a716-************', 'Security Guidelines', 'category', '************************************', 'Security guideline documents', 3)
ON CONFLICT (name, parent_id) DO NOTHING;

-- Insert subcategories for Technical Documentation
INSERT INTO document_categories (id, name, type, parent_id, description, sort_order) VALUES
    ('550e8400-e29b-41d4-a716-************', 'Equipment Manuals', 'category', '550e8400-e29b-41d4-a716-************', 'Equipment and machinery manuals', 1),
    ('550e8400-e29b-41d4-a716-************', 'System Specifications', 'category', '550e8400-e29b-41d4-a716-************', 'System specification documents', 2),
    ('550e8400-e29b-41d4-a716-************', 'Maintenance Guides', 'category', '550e8400-e29b-41d4-a716-************', 'Maintenance guide documents', 3)
ON CONFLICT (name, parent_id) DO NOTHING;

-- Insert some default website categories
INSERT INTO website_categories (id, name, description, sort_order) VALUES
    ('660e8400-e29b-41d4-a716-************', 'Official Railway Websites', 'Official Indian Railways websites', 1),
    ('660e8400-e29b-41d4-a716-************', 'News & Updates', 'Railway news and update websites', 2),
    ('660e8400-e29b-41d4-a716-************', 'Technical Resources', 'Technical and educational railway resources', 3),
    ('************************************', 'Travel & Booking', 'Travel and booking related websites', 4),
    ('660e8400-e29b-41d4-a716-************', 'General Information', 'General railway information websites', 5)
ON CONFLICT (name) DO NOTHING;

-- Add category columns to existing documents table if they don't exist
DO $$ 
BEGIN
    -- Add main_category column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'documents' AND column_name = 'main_category') THEN
        ALTER TABLE documents ADD COLUMN main_category UUID REFERENCES document_categories(id);
    END IF;
    
    -- Add category column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'documents' AND column_name = 'category') THEN
        ALTER TABLE documents ADD COLUMN category UUID REFERENCES document_categories(id);
    END IF;
    
    -- Add sub_category column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'documents' AND column_name = 'sub_category') THEN
        ALTER TABLE documents ADD COLUMN sub_category UUID REFERENCES document_categories(id);
    END IF;
    
    -- Add minor_category column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'documents' AND column_name = 'minor_category') THEN
        ALTER TABLE documents ADD COLUMN minor_category UUID REFERENCES document_categories(id);
    END IF;
END $$;

-- Add category columns to existing websites table if they don't exist
DO $$ 
BEGIN
    -- Add main_category column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'websites' AND column_name = 'main_category') THEN
        ALTER TABLE websites ADD COLUMN main_category UUID REFERENCES website_categories(id);
    END IF;
    
    -- Add category column (for backward compatibility)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'websites' AND column_name = 'category') THEN
        ALTER TABLE websites ADD COLUMN category UUID REFERENCES website_categories(id);
    END IF;
    
    -- Add sub_category column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'websites' AND column_name = 'sub_category') THEN
        ALTER TABLE websites ADD COLUMN sub_category UUID REFERENCES website_categories(id);
    END IF;
END $$;

-- Create indexes for category foreign keys
CREATE INDEX IF NOT EXISTS idx_documents_main_category ON documents(main_category);
CREATE INDEX IF NOT EXISTS idx_documents_category ON documents(category);
CREATE INDEX IF NOT EXISTS idx_documents_sub_category ON documents(sub_category);
CREATE INDEX IF NOT EXISTS idx_documents_minor_category ON documents(minor_category);

CREATE INDEX IF NOT EXISTS idx_websites_main_category ON websites(main_category);
CREATE INDEX IF NOT EXISTS idx_websites_category ON websites(category);
CREATE INDEX IF NOT EXISTS idx_websites_sub_category ON websites(sub_category);

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT ALL ON document_categories TO authenticated;
-- GRANT ALL ON website_categories TO authenticated;

-- Create RLS policies if needed (uncomment and adjust as needed)
-- ALTER TABLE document_categories ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE website_categories ENABLE ROW LEVEL SECURITY;

-- CREATE POLICY "Allow read access to document categories" ON document_categories FOR SELECT USING (true);
-- CREATE POLICY "Allow insert access to document categories" ON document_categories FOR INSERT WITH CHECK (true);
-- CREATE POLICY "Allow update access to document categories" ON document_categories FOR UPDATE USING (true);

-- CREATE POLICY "Allow read access to website categories" ON website_categories FOR SELECT USING (true);
-- CREATE POLICY "Allow insert access to website categories" ON website_categories FOR INSERT WITH CHECK (true);
-- CREATE POLICY "Allow update access to website categories" ON website_categories FOR UPDATE USING (true);

-- Verify the tables were created successfully
SELECT 'document_categories table created' as status, count(*) as record_count FROM document_categories;
SELECT 'website_categories table created' as status, count(*) as record_count FROM website_categories;
