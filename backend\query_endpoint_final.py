from typing import Dict, List, Any, Optional
from fastapi import HTTPException
from pydantic import BaseModel
import logging

# Import from server module
from .server import (
    generate_embedding, search_supabase_document_chunks_enhanced, 
    search_supabase_website_chunks, detect_visual_query, llm_router,
    generate_llm_answer, logger
)
from .answer_logic_final import generate_clean_answer_with_sources

class QueryRequest(BaseModel):
    query: str
    model: Optional[str] = "gemini-2.0-flash"
    fallback_enabled: Optional[bool] = True
    extract_format: Optional[str] = "paragraph"
    use_hybrid_search: Optional[bool] = True
    retry_on_timeout: Optional[bool] = True
    context_mode: Optional[str] = "flexible"

class Source(BaseModel):
    source_type: str  # "document" or "website"
    filename: Optional[str] = None
    name: Optional[str] = None
    page: Optional[int] = None
    pages: Optional[List[int]] = None  # NEW: aggregated page numbers
    url: Optional[str] = None
    link: Optional[str] = None
    content_type: Optional[str] = None
    visual_content: Optional[Dict[str, Any]] = None
    storage_url: Optional[str] = None
    display_type: Optional[str] = None

class QueryResponse(BaseModel):
    answer: str
    document_answer: Optional[str] = None
    website_answer: Optional[str] = None
    sources: List[Source]
    document_sources: Optional[List[Source]] = None
    website_sources: Optional[List[Source]] = None
    llm_model: Optional[str] = None
    llm_fallback: Optional[bool] = False
    visual_content_found: Optional[bool] = False
    visual_content_types: Optional[List[str]] = None

async def process_query_final(request: QueryRequest) -> QueryResponse:
    """
    FINAL CANONICAL QUERY PROCESSOR
    
    Implements strict priority logic:
    1. Document chunks (highest priority)
    2. Website chunks (medium priority) 
    3. LLM fallback (lowest priority)
    
    Only uses LLM fallback when NO relevant chunks found in either source.
    """
    logger.info(f"🔍 [FINAL] Processing query: '{request.query}' using model: {request.model}")
    
    # Initialize response variables
    document_answer = None
    website_answer = None
    document_sources = []
    website_sources = []
    combined_sources = []
    llm_fallback_used = False
    model_id = request.model
    
    try:
        # Validate model availability
        if not llm_router.is_model_available(request.model):
            logger.warning(f"Model {request.model} not available, using default")
            model_id = llm_router.DEFAULT_MODEL
        
        # Generate query embedding
        logger.info("🔍 Generating query embedding...")
        query_embedding = generate_embedding(request.query, model_id)
        if not query_embedding:
            raise Exception("Failed to generate query embedding")
        
        logger.info(f"✅ Query embedding generated (size: {len(query_embedding)})")
        
        # STEP 1: Search Document Chunks (HIGHEST PRIORITY)
        logger.info("=== STEP 1: DOCUMENT SEARCH (HIGHEST PRIORITY) ===")
        document_chunks = []
        
        try:
            document_chunks = search_supabase_document_chunks_enhanced(
                query_embedding=query_embedding,
                query_text=request.query,
                use_hybrid_search=request.use_hybrid_search,
                visual_query_info=detect_visual_query(request.query),
                top_k=20,  # Get more candidates
                min_threshold=0.1  # Lower threshold - let answer logic filter
            )
            
            if document_chunks:
                logger.info(f"✅ Found {len(document_chunks)} document chunks")
                for i, chunk in enumerate(document_chunks[:3], 1):
                    sim = chunk.get('similarity', 0)
                    filename = chunk.get('filename', 'Unknown')
                    logger.info(f"   {i}. Similarity: {sim:.3f} | File: {filename}")
                
                # Generate document answer using canonical logic
                document_answer, document_sources, doc_visual_found, doc_visual_types = generate_clean_answer_with_sources(
                    query=request.query,
                    chunks=document_chunks,
                    source_type="document",
                    model_id=model_id,
                    extract_format=request.extract_format
                )
                
                if document_sources:
                    logger.info(f"✅ Document answer generated with {len(document_sources)} sources")
                    combined_sources.extend(document_sources)
                else:
                    logger.info("⚠️ Document chunks found but no relevant sources after filtering")
                    document_answer = None
            else:
                logger.info("⚠️ No document chunks found")
                
        except Exception as e:
            logger.error(f"❌ Document search error: {str(e)}")
            document_chunks = []
        
        # STEP 2: Search Website Chunks (MEDIUM PRIORITY)
        logger.info("=== STEP 2: WEBSITE SEARCH (MEDIUM PRIORITY) ===")
        website_chunks = []
        
        try:
            website_chunks = search_supabase_website_chunks(
                query_embedding=query_embedding,
                query_text=request.query,
                use_hybrid_search=request.use_hybrid_search,
                top_k=15,  # Get candidates
                min_threshold=0.1  # Lower threshold - let answer logic filter
            )
            
            if website_chunks:
                logger.info(f"✅ Found {len(website_chunks)} website chunks")
                for i, chunk in enumerate(website_chunks[:3], 1):
                    sim = chunk.get('similarity', 0)
                    url = chunk.get('url', 'Unknown URL')
                    logger.info(f"   {i}. Similarity: {sim:.3f} | URL: {url}")
                
                # Generate website answer using canonical logic
                website_answer, website_sources, web_visual_found, web_visual_types = generate_clean_answer_with_sources(
                    query=request.query,
                    chunks=website_chunks,
                    source_type="website",
                    model_id=model_id,
                    extract_format=request.extract_format
                )
                
                if website_sources:
                    logger.info(f"✅ Website answer generated with {len(website_sources)} sources")
                    combined_sources.extend(website_sources)
                else:
                    logger.info("⚠️ Website chunks found but no relevant sources after filtering")
                    website_answer = None
            else:
                logger.info("⚠️ No website chunks found")
                
        except Exception as e:
            logger.error(f"❌ Website search error: {str(e)}")
            website_chunks = []
        
        # STEP 3: DETERMINE FINAL ANSWER STRATEGY
        logger.info("=== STEP 3: FINAL ANSWER STRATEGY ===")
        
        final_answer = ""
        
        # Priority 1: Document answer exists
        if document_answer and document_sources:
            logger.info("📄 Using DOCUMENT answer (highest priority)")
            final_answer = document_answer
            llm_fallback_used = False
            
        # Priority 2: Website answer exists (if no document answer)
        elif website_answer and website_sources:
            logger.info("🌐 Using WEBSITE answer (medium priority)")
            final_answer = website_answer
            llm_fallback_used = False
            
        # Priority 3: LLM fallback (if no relevant content found)
        else:
            logger.info("🤖 Using LLM FALLBACK (lowest priority - no relevant chunks found)")
            if request.fallback_enabled:
                try:
                    final_answer = generate_llm_answer(
                        query=request.query,
                        similar_chunks=[],  # No context
                        model_id=model_id,
                        extract_format=request.extract_format
                    )
                    llm_fallback_used = True
                    logger.info("✅ LLM fallback answer generated")
                except Exception as e:
                    logger.error(f"❌ LLM fallback failed: {str(e)}")
                    final_answer = "I apologize, but I couldn't find relevant information in the uploaded documents or extracted websites, and the fallback system is currently unavailable."
                    llm_fallback_used = True
            else:
                final_answer = "No relevant information found in uploaded documents or extracted websites."
                llm_fallback_used = True
        
        # Build response
        response = QueryResponse(
            answer=final_answer,
            document_answer=document_answer,
            website_answer=website_answer,
            sources=combined_sources,
            document_sources=document_sources if document_sources else None,
            website_sources=website_sources if website_sources else None,
            llm_model=model_id,
            llm_fallback=llm_fallback_used,
            visual_content_found=False,  # TODO: Implement visual content detection
            visual_content_types=None
        )
        
        logger.info(f"✅ [FINAL] Query processed successfully:")
        logger.info(f"   - Document sources: {len(document_sources)}")
        logger.info(f"   - Website sources: {len(website_sources)}")
        logger.info(f"   - LLM fallback used: {llm_fallback_used}")
        
        return response
        
    except Exception as e:
        logger.error(f"❌ [FINAL] Query processing failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Query processing failed: {str(e)}") 