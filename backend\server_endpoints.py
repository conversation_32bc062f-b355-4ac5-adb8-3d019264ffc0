# Additional search functions and endpoints for RailGPT server

def text_based_document_search(query: str, top_k: int = 10):
    """Text-based document search using keyword matching with relevance scoring."""
    global DOCUMENT_CHUNKS
    
    try:
        logger.info(f"Using text-based document search for: '{query}'")
        
        # Extract key terms from query
        query_lower = query.lower()
        query_words = set(query_lower.split())
        
        # Railway domain keywords for relevance boost
        railway_keywords = {
            'railway', 'train', 'rail', 'station', 'track', 'locomotive', 'coach',
            'signal', 'platform', 'passenger', 'freight', 'engine', 'diesel',
            'fsds', 'monitoring', 'system', 'safety', 'maintenance', 'inspection'
        }
        
        scored_chunks = []
        
        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'document':
                continue
            
            chunk_text = chunk.get('text', '').lower()
            if not chunk_text:
                continue
            
            # Calculate relevance score
            score = 0
            
            # 1. Exact phrase match (highest priority)
            if query_lower in chunk_text:
                score += 2.0
            
            # 2. Keyword matching
            chunk_words = set(chunk_text.split())
            common_words = query_words & chunk_words
            if common_words:
                min_matches = max(1, len(query_words) // 2)
                if len(common_words) >= min_matches:
                    score += len(common_words) * 0.5
            
            # 3. Railway domain relevance
            railway_matches = railway_keywords & chunk_words
            if railway_matches:
                score += len(railway_matches) * 0.8
            
            # 4. Special boost for technical terms
            if 'fsds' in chunk_text:
                score += 1.5
            if 'monitoring' in chunk_text and 'system' in chunk_text:
                score += 1.0
            
            # Only include chunks with meaningful relevance
            if score >= 0.8:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = score
                scored_chunks.append(chunk_copy)
        
        # Sort by relevance score
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)
        
        result = scored_chunks[:top_k]
        logger.info(f"Text-based document search found {len(result)} relevant chunks")
        
        return result
        
    except Exception as e:
        logger.error(f"Error in text-based document search: {str(e)}")
        return []

def text_based_website_search(query: str, top_k: int = 10):
    """Text-based website search using keyword matching with relevance scoring."""
    global DOCUMENT_CHUNKS
    
    try:
        logger.info(f"Using text-based website search for: '{query}'")
        
        # Extract key terms from query
        query_lower = query.lower()
        query_words = set(query_lower.split())
        
        # Transport/railway domain keywords for relevance boost
        transport_keywords = {
            'railway', 'train', 'rail', 'station', 'transport', 'transportation',
            'public', 'safety', 'travel', 'passenger', 'metro', 'services'
        }
        
        scored_chunks = []
        
        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'website':
                continue
            
            chunk_text = chunk.get('text', '').lower()
            if not chunk_text:
                continue
            
            # Calculate relevance score
            score = 0
            
            # 1. Exact phrase match (highest priority)
            if query_lower in chunk_text:
                score += 2.0
            
            # 2. Keyword matching
            chunk_words = set(chunk_text.split())
            common_words = query_words & chunk_words
            if common_words:
                min_matches = max(1, len(query_words) // 2)
                if len(common_words) >= min_matches:
                    score += len(common_words) * 0.5
            
            # 3. Transport domain relevance
            transport_matches = transport_keywords & chunk_words
            if transport_matches:
                score += len(transport_matches) * 0.8
            
            # 4. Special boost for transport safety
            if 'transportation' in chunk_text and 'safety' in chunk_text:
                score += 1.0
            if 'public' in chunk_text and ('transport' in chunk_text or 'railway' in chunk_text):
                score += 1.0
            
            # Only include chunks with meaningful relevance
            if score >= 0.8:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = score
                scored_chunks.append(chunk_copy)
        
        # Sort by relevance score
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)
        
        result = scored_chunks[:top_k]
        logger.info(f"Text-based website search found {len(result)} relevant chunks")
        
        return result
        
    except Exception as e:
        logger.error(f"Error in text-based website search: {str(e)}")
        return []

def search_supabase_document_chunks(query_embedding, query_text=None, use_hybrid_search=True, top_k=30, min_threshold=0.4):
    """Professional vector search for document chunks using Supabase pgvector."""
    try:
        logger.info(f"Vector search for documents: threshold={min_threshold}, top_k={top_k}")
        
        # Try to use Supabase RPC function first
        try:
            from supabase_client import supabase
            result = supabase.rpc(
                'search_document_chunks',
                {
                    'query_embedding': query_embedding,
                    'similarity_threshold': min_threshold,
                    'match_count': top_k
                }
            ).execute()
            
            if result.data and len(result.data) > 0:
                for chunk in result.data:
                    chunk["source_type"] = "document"
                logger.info(f"RPC search found {len(result.data)} document chunks")
                return result.data
        except Exception as e:
            logger.info(f"RPC search failed, trying local approach: {str(e)}")
        
        # Fallback to local search with proper embeddings
        logger.info("Using local vector search with cosine similarity")
        global DOCUMENT_CHUNKS
        
        scored_chunks = []
        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'document':
                continue
            
            chunk_embedding = chunk.get('embedding')
            if not chunk_embedding:
                continue
            
            # Calculate cosine similarity
            similarity = cosine_similarity(query_embedding, chunk_embedding)
            
            if similarity >= min_threshold:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = similarity
                scored_chunks.append(chunk_copy)
        
        # Sort by similarity (highest first)
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)
        result = scored_chunks[:top_k]
        
        logger.info(f"Local vector search found {len(result)} document chunks")
        return result
        
    except Exception as e:
        logger.error(f"Error in vector document search: {str(e)}")
        return []

def search_supabase_website_chunks(query_embedding, query_text=None, use_hybrid_search=True, top_k=30, min_threshold=0.2):
    """Professional vector search for website chunks using Supabase pgvector."""
    try:
        logger.info(f"Vector search for websites: threshold={min_threshold}, top_k={top_k}")
        
        # Try to use Supabase RPC function first
        try:
            from supabase_client import supabase
            result = supabase.rpc(
                'search_website_chunks',
                {
                    'query_embedding': query_embedding,
                    'similarity_threshold': min_threshold,
                    'match_count': top_k
                }
            ).execute()
            
            if result.data and len(result.data) > 0:
                for chunk in result.data:
                    chunk["source_type"] = "website"
                logger.info(f"RPC website search found {len(result.data)} chunks")
                return result.data
        except Exception as rpc_error:
            logger.info(f"RPC website search failed: {str(rpc_error)}")
        
        # Fallback to local search
        logger.info("Using local vector search for websites")
        global DOCUMENT_CHUNKS
        
        scored_chunks = []
        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'website':
                continue
            
            chunk_embedding = chunk.get('embedding')
            if not chunk_embedding:
                continue
            
            # Calculate cosine similarity
            similarity = cosine_similarity(query_embedding, chunk_embedding)
            
            if similarity >= min_threshold:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = similarity
                scored_chunks.append(chunk_copy)
        
        # Sort by similarity (highest first)
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)
        result = scored_chunks[:top_k]
        
        logger.info(f"Local website vector search found {len(result)} chunks")
        return result
        
    except Exception as e:
        logger.error(f"Error in vector website search: {str(e)}")
        return []

def format_document_sources(results: List[Dict[str, Any]]) -> List[Source]:
    """Format document search results into Source objects."""
    sources = []
    for result in results:
        source = Source(
            source_type="document",
            filename=result.get('filename', 'Unknown Document'),
            name=result.get('filename', 'Unknown Document'),
            page=result.get('page_number', 1),
            link=f"/viewer?file={result.get('filename', '')}&page={result.get('page_number', 1)}"
        )
        sources.append(source)
    return sources

def format_website_sources(results: List[Dict[str, Any]]) -> List[Source]:
    """Format website search results into Source objects."""
    sources = []
    seen_urls = set()
    for result in results:
        url = result.get('url', 'Unknown Website')
        if url not in seen_urls:
            seen_urls.add(url)
            source = Source(
                source_type="website",
                url=url,
                link=url
            )
            sources.append(source)
    return sources
