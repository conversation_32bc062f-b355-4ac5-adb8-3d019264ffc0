import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Category, CategoryReassignmentRequest, getCategoriesByLevel } from '../../services/api';

interface CategorySelectorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (categorySelection: CategoryReassignmentRequest) => Promise<void>;
  entityType: 'document' | 'website';
  entityName: string;
  currentCategories?: {
    mainCategory?: string;
    category?: string;
    subCategory?: string;
    minorCategory?: string;
  };
  isBulkOperation?: boolean;
  selectedCount?: number;
}

interface CategorySelection {
  mainCategoryId?: string;
  categoryId?: string;
  subCategoryId?: string;
  minorCategoryId?: string;
}

const CategorySelectorModal: React.FC<CategorySelectorModalProps> = ({
  isOpen,
  onClose,
  onSave,
  entityType,
  entityName,
  currentCategories,
  isBulkOperation = false,
  selectedCount = 1
}) => {
  // Track if component is mounted to prevent state updates on unmounted components
  const isMountedRef = useRef(true);

  const [categories, setCategories] = useState<{
    mainCategories: Category[];
    categories: Category[];
    subCategories: Category[];
    minorCategories: Category[];
  }>({
    mainCategories: [],
    categories: [],
    subCategories: [],
    minorCategories: []
  });

  const [selection, setSelection] = useState<CategorySelection>({});
  const [changeReason, setChangeReason] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingLevel, setLoadingLevel] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Define loadMainCategories before using it in useEffect
  const loadMainCategories = useCallback(async () => {
    try {
      if (isMountedRef.current) setLoadingLevel(1);
      const response = await getCategoriesByLevel(entityType, 1);
      if (isMountedRef.current) {
        setCategories(prev => ({
          ...prev,
          mainCategories: response.categories || []
        }));
      }
    } catch (error) {
      console.error('Error loading main categories:', error);
      if (isMountedRef.current) {
        setError('Failed to load main categories');
        setCategories(prev => ({
          ...prev,
          mainCategories: []
        }));
      }
    } finally {
      if (isMountedRef.current) setLoadingLevel(null);
    }
  }, [entityType]);

  // Load main categories on mount
  useEffect(() => {
    let mounted = true;
    
    const loadData = async () => {
      if (isOpen && mounted && isMountedRef.current) {
        await loadMainCategories();
      }
    };
    
    if (isOpen) {
      loadData();
    }
    
    return () => {
      mounted = false;
    };
  }, [isOpen, loadMainCategories]);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen && isMountedRef.current) {
      setSelection({});
      setChangeReason('');
      setError(null);
      setCategories({
        mainCategories: [],
        categories: [],
        subCategories: [],
        minorCategories: []
      });
    }
  }, [isOpen]);

  const loadCategoriesByParent = async (level: number, parentId: string) => {
    try {
      if (isMountedRef.current) setLoadingLevel(level);
      const response = await getCategoriesByLevel(entityType, level, parentId);
      
      if (isMountedRef.current) {
        const levelKey = level === 2 ? 'categories' : level === 3 ? 'subCategories' : 'minorCategories';
        setCategories(prev => ({
          ...prev,
          [levelKey]: response.categories
        }));
      }
    } catch (error) {
      console.error(`Error loading level ${level} categories:`, error);
      if (isMountedRef.current) {
        setError(`Failed to load level ${level} categories`);
      }
    } finally {
      if (isMountedRef.current) setLoadingLevel(null);
    }
  };

  const handleMainCategoryChange = async (value: string) => {
    const newSelection = {
      mainCategoryId: value,
      categoryId: undefined,
      subCategoryId: undefined,
      minorCategoryId: undefined
    };
    if (isMountedRef.current) setSelection(newSelection);

    // Clear dependent categories
    if (isMountedRef.current) {
      setCategories(prev => ({
        ...prev,
        categories: [],
        subCategories: [],
        minorCategories: []
      }));
    }

    if (value) {
      await loadCategoriesByParent(2, value);
    }
  };

  const handleCategoryChange = async (value: string) => {
    const newSelection = {
      ...selection,
      categoryId: value,
      subCategoryId: undefined,
      minorCategoryId: undefined
    };
    if (isMountedRef.current) setSelection(newSelection);

    // Clear dependent categories
    if (isMountedRef.current) {
      setCategories(prev => ({
        ...prev,
        subCategories: [],
        minorCategories: []
      }));
    }

    if (value) {
      await loadCategoriesByParent(3, value);
    }
  };

  const handleSubCategoryChange = async (value: string) => {
    const newSelection = {
      ...selection,
      subCategoryId: value,
      minorCategoryId: undefined
    };
    if (isMountedRef.current) setSelection(newSelection);

    // Clear dependent categories
    if (isMountedRef.current) {
      setCategories(prev => ({
        ...prev,
        minorCategories: []
      }));
    }

    if (value) {
      await loadCategoriesByParent(4, value);
    }
  };

  const handleMinorCategoryChange = (value: string) => {
    if (isMountedRef.current) {
      setSelection({
        ...selection,
        minorCategoryId: value
      });
    }
  };

  const handleSave = async () => {
    try {
      if (isMountedRef.current) {
        setIsLoading(true);
        setError(null);
      }

      const reassignmentRequest: CategoryReassignmentRequest = {
        main_category_id: selection.mainCategoryId,
        category_id: selection.categoryId,
        sub_category_id: selection.subCategoryId,
        minor_category_id: selection.minorCategoryId,
        changed_by: 'user', // In a real app, this would be the current user
        change_reason: changeReason || undefined
      };

      await onSave(reassignmentRequest);
      if (isMountedRef.current) {
        onClose();
      }
    } catch (error) {
      console.error('Error saving category assignment:', error);
      if (isMountedRef.current) {
        setError(error instanceof Error ? error.message : 'Failed to save category assignment');
      }
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false);
      }
    }
  };

  const renderDropdown = (
    label: string,
    value: string | undefined,
    onChange: (value: string) => void,
    options: Category[],
    placeholder: string,
    isLoading: boolean = false
  ) => (
    <div className="mb-4">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label}
      </label>
      <select
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100"
        disabled={isLoading || options.length === 0}
      >
        <option value="">{isLoading ? 'Loading...' : placeholder}</option>
        {options.map((option) => (
          <option key={option.id} value={option.id}>
            {option.name}
          </option>
        ))}
      </select>
    </div>
  );

  const buildCurrentPath = () => {
    if (!currentCategories) return 'Uncategorized';
    
    const parts = [];
    if (currentCategories.mainCategory) parts.push(currentCategories.mainCategory);
    if (currentCategories.category) parts.push(currentCategories.category);
    if (currentCategories.subCategory) parts.push(currentCategories.subCategory);
    if (currentCategories.minorCategory) parts.push(currentCategories.minorCategory);
    
    return parts.length > 0 ? parts.join(' → ') : 'Uncategorized';
  };

  const buildNewPath = () => {
    const selectedMainCategory = categories.mainCategories?.find(c => c.id === selection.mainCategoryId);
    const selectedCategory = categories.categories?.find(c => c.id === selection.categoryId);
    const selectedSubCategory = categories.subCategories?.find(c => c.id === selection.subCategoryId);
    const selectedMinorCategory = categories.minorCategories?.find(c => c.id === selection.minorCategoryId);

    const parts = [];
    if (selectedMainCategory) parts.push(selectedMainCategory.name);
    if (selectedCategory) parts.push(selectedCategory.name);
    if (selectedSubCategory) parts.push(selectedSubCategory.name);
    if (selectedMinorCategory) parts.push(selectedMinorCategory.name);
    
    return parts.length > 0 ? parts.join(' → ') : 'None selected';
  };

  // const hasChanges = () => {
  //   if (!currentCategories) return Object.values(selection).some(v => v);
  //   
  //   // Check if selection is different from current categories
  //   // This is a simplified check - in a real app you'd compare IDs properly
  //   return true; // For now, always enable save if any selection is made
  // };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-900">
            {isBulkOperation 
              ? `Change Category for ${selectedCount} ${entityType}s`
              : `Change Category for ${entityType}`
            }
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        <div className="mb-6">
          <p className="text-sm text-gray-600 mb-2">
            {isBulkOperation ? `${selectedCount} items selected` : entityName}
          </p>
          
          {!isBulkOperation && currentCategories && (
            <div className="mb-4">
              <p className="text-sm font-medium text-gray-700 mb-1">Current Category:</p>
              <p className="text-sm text-gray-600 bg-gray-100 p-2 rounded">
                {buildCurrentPath()}
              </p>
            </div>
          )}

          <div className="mb-4">
            <p className="text-sm font-medium text-gray-700 mb-1">New Category:</p>
            <p className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
              {buildNewPath()}
            </p>
          </div>
        </div>

        <div className="space-y-4">
          {renderDropdown(
            'Main Category',
            selection.mainCategoryId,
            handleMainCategoryChange,
            categories.mainCategories || [],
            'Select main category',
            loadingLevel === 1
          )}

          {renderDropdown(
            'Category',
            selection.categoryId,
            handleCategoryChange,
            categories.categories || [],
            selection.mainCategoryId ? 'Select category' : 'Select main category first',
            loadingLevel === 2
          )}

          {renderDropdown(
            'Subcategory',
            selection.subCategoryId,
            handleSubCategoryChange,
            categories.subCategories || [],
            selection.categoryId ? 'Select subcategory (optional)' : 'Select category first',
            loadingLevel === 3
          )}

          {renderDropdown(
            'Minor Category',
            selection.minorCategoryId,
            handleMinorCategoryChange,
            categories.minorCategories || [],
            selection.subCategoryId ? 'Select minor category (optional)' : 'Select subcategory first',
            loadingLevel === 4
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Reason for Change (Optional)
            </label>
            <textarea
              value={changeReason}
              onChange={(e) => setChangeReason(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              rows={3}
              placeholder="Enter reason for category change..."
            />
          </div>
        </div>

        <div className="flex space-x-3 mt-6">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-400"
            disabled={isLoading || !selection.mainCategoryId}
          >
            {isLoading ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CategorySelectorModal; 