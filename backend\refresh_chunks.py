#!/usr/bin/env python3
"""
Script to refresh embeddings and validate chunk data integrity.
This ensures all chunks have proper embeddings and metadata.
"""
import logging
from typing import List, Dict, Any
import asyncio
from supabase_client import supabase
from llm_router import generate_embedding

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def refresh_document_chunk_embeddings() -> Dict[str, int]:
    """Refresh embeddings for document chunks that need them."""
    stats = {"processed": 0, "updated": 0, "failed": 0}
    
    try:
        # Get chunks missing embeddings
        missing_embeddings = await supabase.from_("document_chunks") \
            .select("id, text") \
            .is_("embedding", "null") \
            .execute()
        
        chunks = missing_embeddings.data if hasattr(missing_embeddings, "data") else missing_embeddings
        
        for chunk in chunks:
            stats["processed"] += 1
            try:
                # Generate new embedding
                embedding = generate_embedding(chunk["text"])
                if embedding:
                    # Update chunk with new embedding
                    await supabase.from_("document_chunks") \
                        .update({"embedding": embedding}) \
                        .eq("id", chunk["id"]) \
                        .execute()
                    stats["updated"] += 1
                else:
                    stats["failed"] += 1
            except Exception as e:
                logger.error(f"Error updating chunk {chunk['id']}: {str(e)}")
                stats["failed"] += 1
                
        return stats
    except Exception as e:
        logger.error(f"Error in refresh_document_chunk_embeddings: {str(e)}")
        return stats

async def refresh_website_chunk_embeddings() -> Dict[str, int]:
    """Refresh embeddings for website chunks that need them."""
    stats = {"processed": 0, "updated": 0, "failed": 0}
    
    try:
        # Get chunks missing embeddings
        missing_embeddings = await supabase.from_("website_chunks") \
            .select("id, text") \
            .is_("embedding", "null") \
            .execute()
        
        chunks = missing_embeddings.data if hasattr(missing_embeddings, "data") else missing_embeddings
        
        for chunk in chunks:
            stats["processed"] += 1
            try:
                # Generate new embedding
                embedding = generate_embedding(chunk["text"])
                if embedding:
                    # Update chunk with new embedding
                    await supabase.from_("website_chunks") \
                        .update({"embedding": embedding}) \
                        .eq("id", chunk["id"]) \
                        .execute()
                    stats["updated"] += 1
                else:
                    stats["failed"] += 1
            except Exception as e:
                logger.error(f"Error updating chunk {chunk['id']}: {str(e)}")
                stats["failed"] += 1
                
        return stats
    except Exception as e:
        logger.error(f"Error in refresh_website_chunk_embeddings: {str(e)}")
        return stats

async def validate_chunk_metadata() -> Dict[str, int]:
    """Validate and fix chunk metadata if needed."""
    stats = {"processed": 0, "updated": 0, "failed": 0}
    
    try:
        # Check document chunks
        doc_chunks = await supabase.from_("document_chunks") \
            .select("id, metadata") \
            .execute()
            
        chunks = doc_chunks.data if hasattr(doc_chunks, "data") else doc_chunks
        
        for chunk in chunks:
            stats["processed"] += 1
            try:
                metadata = chunk.get("metadata", {})
                if isinstance(metadata, str):
                    try:
                        metadata = json.loads(metadata)
                    except:
                        metadata = {}
                
                # Ensure required metadata fields exist
                if not metadata.get("has_images"):
                    metadata["has_images"] = False
                if not metadata.get("has_tables"):
                    metadata["has_tables"] = False
                if not metadata.get("has_charts"):
                    metadata["has_charts"] = False
                
                # Update chunk with fixed metadata
                await supabase.from_("document_chunks") \
                    .update({"metadata": metadata}) \
                    .eq("id", chunk["id"]) \
                    .execute()
                stats["updated"] += 1
            except Exception as e:
                logger.error(f"Error validating chunk {chunk['id']}: {str(e)}")
                stats["failed"] += 1
                
        return stats
    except Exception as e:
        logger.error(f"Error in validate_chunk_metadata: {str(e)}")
        return stats

async def main():
    """Main function to run all refresh and validation tasks."""
    try:
        logger.info("Starting chunk refresh and validation...")
        
        # Refresh document chunks
        doc_stats = await refresh_document_chunk_embeddings()
        logger.info(f"Document chunks processed: {doc_stats['processed']}")
        logger.info(f"Document chunks updated: {doc_stats['updated']}")
        logger.info(f"Document chunks failed: {doc_stats['failed']}")
        
        # Refresh website chunks
        web_stats = await refresh_website_chunk_embeddings()
        logger.info(f"Website chunks processed: {web_stats['processed']}")
        logger.info(f"Website chunks updated: {web_stats['updated']}")
        logger.info(f"Website chunks failed: {web_stats['failed']}")
        
        # Validate metadata
        meta_stats = await validate_chunk_metadata()
        logger.info(f"Metadata validations processed: {meta_stats['processed']}")
        logger.info(f"Metadata records updated: {meta_stats['updated']}")
        logger.info(f"Metadata validations failed: {meta_stats['failed']}")
        
        logger.info("Chunk refresh and validation completed!")
        
    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
