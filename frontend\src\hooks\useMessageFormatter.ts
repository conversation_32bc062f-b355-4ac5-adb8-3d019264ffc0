import { useMemo } from 'react';
import { 
  formatMessageContent,
  extractDocumentSources,
  extractWebsiteSources,
  extractAllSources,
  DocumentSource,
  WebsiteSource,
  GenericSource
} from '../services/messageFormatter';

/**
 * Hook to provide message formatting utilities for chat responses
 * @param responseData - The raw response data from the API
 */
export const useMessageFormatter = (responseData: any) => {
  const formattedContent = useMemo(() => {
    return formatMessageContent(responseData);
  }, [responseData]);

  const sources = useMemo(() => {
    return extractAllSources(responseData);
  }, [responseData]);

  // Helper to check if there are any sources available
  const hasSources = useMemo(() => {
    return sources.documentSources.length > 0 || sources.websiteSources.length > 0;
  }, [sources]);

  return {
    content: formattedContent,
    documentSources: sources.documentSources,
    websiteSources: sources.websiteSources,
    allSources: sources.allSources,
    hasSources
  };
};

export default useMessageFormatter;
