# Document and Website Management Endpoints for RailGPT

# Get all documents endpoint
@app.get("/api/documents")
async def get_documents():
    """Get all documents from Supabase database"""
    try:
        logger.info("Fetching documents from Supabase database")
        
        # Query to get all documents with their metadata
        documents_query = """
        SELECT 
            id,
            display_name,
            file_path,
            file_type,
            file_size,
            uploaded_by,
            role,
            created_at,
            updated_at,
            supabase_file_path,
            supabase_file_url,
            processing_status,
            chunk_count,
            visual_content_extracted
        FROM documents 
        ORDER BY created_at DESC
        """
        
        result = supabase.execute_query(documents_query)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error fetching documents: {result['error']}")
            return {"documents": [], "error": result["error"]}
        
        logger.info(f"Successfully fetched {len(result)} documents")
        return {"documents": result, "total": len(result)}
        
    except Exception as e:
        logger.error(f"Error fetching documents: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching documents: {str(e)}")

# Get all websites endpoint
@app.get("/api/websites")
async def get_websites():
    """Get all websites from Supabase database"""
    try:
        logger.info("Fetching websites from Supabase database")
        
        # Query to get all websites with their metadata
        websites_query = """
        SELECT 
            id,
            url,
            domain,
            title,
            description,
            submitted_by,
            role,
            created_at,
            updated_at,
            extraction_status,
            chunk_count,
            last_crawled,
            content_quality_score,
            main_category,
            category,
            sub_category
        FROM websites 
        ORDER BY created_at DESC
        """
        
        result = supabase.execute_query(websites_query)
        
        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error fetching websites: {result['error']}")
            return {"websites": [], "error": result["error"]}
        
        logger.info(f"Successfully fetched {len(result)} websites")
        return {"websites": result, "total": len(result)}
        
    except Exception as e:
        logger.error(f"Error fetching websites: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching websites: {str(e)}")

# Document upload endpoint
@app.post("/api/upload-document")
async def upload_document(
    file: UploadFile = File(...),
    uploaded_by: Optional[str] = Form(None),
    role: Optional[str] = Form(None),
    supabase_file_path: Optional[str] = Form(None),
    supabase_file_url: Optional[str] = Form(None),
    extract_tables: Optional[bool] = Form(True),
    extract_images: Optional[bool] = Form(True),
    extract_charts: Optional[bool] = Form(True)
):
    """Upload and process a document with visual content extraction."""
    logger.info(f"===== DOCUMENT UPLOAD STARTED =====")
    logger.info(f"Received document: {file.filename}, size: {file.size if hasattr(file, 'size') else 'unknown'} bytes")
    logger.info(f"Uploaded by: {uploaded_by}, role: {role}")
    
    try:
        # Validate file type
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")
        
        allowed_extensions = {'.pdf', '.docx', '.doc', '.txt'}
        file_extension = os.path.splitext(file.filename)[1].lower()
        
        if file_extension not in allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"File type {file_extension} not supported. Allowed types: {', '.join(allowed_extensions)}"
            )
        
        # Create uploads directory if it doesn't exist
        upload_dir = os.path.join(os.path.dirname(__file__), "data", "uploads")
        os.makedirs(upload_dir, exist_ok=True)
        
        # Save file temporarily
        file_path = os.path.join(upload_dir, file.filename)
        
        # Read and save file content
        content = await file.read()
        with open(file_path, "wb") as f:
            f.write(content)
        
        logger.info(f"File saved to: {file_path}")
        
        # Extract document content with visual elements
        try:
            if extract_images or extract_tables or extract_charts:
                logger.info("Extracting document with visual content...")
                extracted_data = extract_document_with_visual_content(
                    file_path,
                    extract_tables=extract_tables,
                    extract_images=extract_images,
                    extract_charts=extract_charts
                )
            else:
                logger.info("Extracting document text only...")
                extracted_data = extract_document(file_path)
            
            if not extracted_data or not extracted_data.get('chunks'):
                raise HTTPException(status_code=400, detail="Failed to extract content from document")
            
            logger.info(f"Successfully extracted {len(extracted_data['chunks'])} chunks")
            
        except Exception as e:
            logger.error(f"Error extracting document: {str(e)}")
            # Clean up temporary file
            if os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(status_code=500, detail=f"Error extracting document: {str(e)}")
        
        # Store document in Supabase
        try:
            # Insert document record
            document_data = {
                'display_name': file.filename,
                'file_path': file_path,
                'file_type': file_extension,
                'file_size': len(content),
                'uploaded_by': uploaded_by or 'Anonymous',
                'role': role or 'User',
                'supabase_file_path': supabase_file_path,
                'supabase_file_url': supabase_file_url,
                'processing_status': 'completed',
                'chunk_count': len(extracted_data['chunks']),
                'visual_content_extracted': bool(extract_images or extract_tables or extract_charts)
            }
            
            # Insert document
            document_result = supabase.table('documents').insert(document_data).execute()
            
            if not document_result.data:
                raise Exception("Failed to insert document record")
            
            document_id = document_result.data[0]['id']
            logger.info(f"Document inserted with ID: {document_id}")
            
            # Insert document chunks
            chunks_inserted = 0
            for i, chunk in enumerate(extracted_data['chunks']):
                try:
                    # Generate embedding for the chunk
                    embedding = generate_embedding(chunk['text'])
                    
                    chunk_data = {
                        'document_id': document_id,
                        'chunk_index': i,
                        'page_number': chunk.get('page', 1),
                        'text': chunk['text'],
                        'embedding': embedding,
                        'metadata': chunk.get('metadata', {}),
                        'visual_content': chunk.get('visual_content'),
                        'content_type': chunk.get('content_type', 'text')
                    }
                    
                    chunk_result = supabase.table('document_chunks').insert(chunk_data).execute()
                    
                    if chunk_result.data:
                        chunks_inserted += 1
                        
                        # Add to global DOCUMENT_CHUNKS for immediate availability
                        global DOCUMENT_CHUNKS
                        DOCUMENT_CHUNKS.append({
                            'id': chunk_result.data[0]['id'],
                            'document_id': document_id,
                            'chunk_index': i,
                            'page_number': chunk.get('page', 1),
                            'text': chunk['text'],
                            'embedding': embedding,
                            'filename': file.filename,
                            'url': f"/api/documents/view/{file.filename}",
                            'source_type': 'document',
                            'similarity': 1.0,
                            'metadata': chunk.get('metadata', {}),
                            'visual_content': chunk.get('visual_content'),
                            'content_type': chunk.get('content_type', 'text')
                        })
                    
                except Exception as chunk_error:
                    logger.error(f"Error inserting chunk {i}: {str(chunk_error)}")
                    continue
            
            logger.info(f"Successfully inserted {chunks_inserted} out of {len(extracted_data['chunks'])} chunks")
            
            # Clean up temporary file
            if os.path.exists(file_path):
                os.remove(file_path)
            
            return {
                "message": "Document uploaded and processed successfully",
                "document_id": document_id,
                "filename": file.filename,
                "chunks_processed": chunks_inserted,
                "visual_content_extracted": bool(extract_images or extract_tables or extract_charts),
                "file_size": len(content)
            }
            
        except Exception as e:
            logger.error(f"Error storing document in Supabase: {str(e)}")
            # Clean up temporary file
            if os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(status_code=500, detail=f"Error storing document: {str(e)}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in document upload: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")

# Website add endpoint
@app.post("/api/add-website")
async def add_website(request: WebsiteAddRequest):
    """Add and process a website for content extraction."""
    url = request.url
    submitted_by = request.submitted_by
    role = request.role
    
    logger.info(f"Received request to add website: {url}")
    
    if not url or not url.strip():
        raise HTTPException(status_code=400, detail="URL cannot be empty")
    
    # Validate URL format
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url
    
    try:
        # Extract website content
        logger.info(f"Extracting content from website: {url}")
        
        extracted_chunks = extract_website_text(
            url,
            follow_links=request.follow_links,
            extraction_depth=request.extraction_depth,
            extract_images=request.extract_images,
            extract_tables=request.extract_tables,
            max_pages=request.max_pages,
            extractor_type=request.extractor_type
        )
        
        if not extracted_chunks:
            raise HTTPException(status_code=400, detail="Failed to extract content from website")
        
        logger.info(f"Successfully extracted {len(extracted_chunks)} chunks from website")
        
        # Store website in Supabase
        try:
            from urllib.parse import urlparse
            parsed_url = urlparse(url)
            domain = parsed_url.netloc
            
            # Insert website record
            website_data = {
                'url': url,
                'domain': domain,
                'title': extracted_chunks[0].get('title', domain) if extracted_chunks else domain,
                'description': f"Website content extracted from {domain}",
                'submitted_by': submitted_by or 'Anonymous',
                'role': role or 'User',
                'extraction_status': 'completed',
                'chunk_count': len(extracted_chunks),
                'content_quality_score': 85,  # Default score
                'main_category': request.main_category,
                'category': request.category,
                'sub_category': request.sub_category
            }
            
            # Insert website
            website_result = supabase.table('websites').insert(website_data).execute()
            
            if not website_result.data:
                raise Exception("Failed to insert website record")
            
            website_id = website_result.data[0]['id']
            logger.info(f"Website inserted with ID: {website_id}")
            
            # Insert website chunks
            chunks_inserted = 0
            for i, chunk in enumerate(extracted_chunks):
                try:
                    # Generate embedding for the chunk
                    embedding = generate_embedding(chunk['text'], request.embedding_model)
                    
                    chunk_data = {
                        'website_id': website_id,
                        'chunk_index': i,
                        'text': chunk['text'],
                        'embedding': embedding,
                        'metadata': chunk.get('metadata', {}),
                        'url': chunk.get('url', url),
                        'title': chunk.get('title', ''),
                        'content_type': chunk.get('content_type', 'text')
                    }
                    
                    chunk_result = supabase.table('website_chunks').insert(chunk_data).execute()
                    
                    if chunk_result.data:
                        chunks_inserted += 1
                        
                        # Add to global DOCUMENT_CHUNKS for immediate availability
                        global DOCUMENT_CHUNKS
                        DOCUMENT_CHUNKS.append({
                            'id': chunk_result.data[0]['id'],
                            'website_id': website_id,
                            'chunk_index': i,
                            'text': chunk['text'],
                            'embedding': embedding,
                            'url': url,
                            'source_type': 'website',
                            'similarity': 1.0,
                            'metadata': chunk.get('metadata', {}),
                            'title': chunk.get('title', ''),
                            'content_type': chunk.get('content_type', 'text')
                        })
                    
                except Exception as chunk_error:
                    logger.error(f"Error inserting website chunk {i}: {str(chunk_error)}")
                    continue
            
            logger.info(f"Successfully inserted {chunks_inserted} out of {len(extracted_chunks)} website chunks")
            
            return {
                "message": "Website added and processed successfully",
                "website_id": website_id,
                "url": url,
                "domain": domain,
                "chunks_processed": chunks_inserted,
                "extraction_method": request.extractor_type
            }
            
        except Exception as e:
            logger.error(f"Error storing website in Supabase: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error storing website: {str(e)}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing website: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing website: {str(e)}")

# Document viewer endpoint to serve files
@app.get("/api/documents/view/{filename}")
async def view_document(filename: str):
    """Serve a document file for viewing in the browser."""
    try:
        import os
        from fastapi.responses import FileResponse
        
        # Decode the filename
        decoded_filename = filename
        logger.info(f"Attempting to serve document: {decoded_filename}")
        
        # Try to find the document in local storage first
        upload_dir = os.path.join(os.path.dirname(__file__), "data", "uploads")
        local_file_path = os.path.join(upload_dir, decoded_filename)
        
        if os.path.exists(local_file_path):
            logger.info(f"Serving document from local storage: {local_file_path}")
            return FileResponse(
                path=local_file_path,
                filename=decoded_filename,
                media_type='application/pdf' if decoded_filename.endswith('.pdf') else 'application/octet-stream'
            )
        
        # If not found locally, try to get from Supabase
        try:
            escaped_filename = decoded_filename.replace("'", "''")
            documents_query = f"""
            SELECT file_path, display_name, file_type, supabase_file_path, supabase_file_url
            FROM documents
            WHERE display_name = '{escaped_filename}' OR file_path LIKE '%{escaped_filename}%'
            """
            result = supabase.execute_query(documents_query)
            
            if result and len(result) > 0:
                document = result[0]
                
                # If we have a Supabase file URL, redirect to it
                if document.get('supabase_file_url'):
                    logger.info(f"Redirecting to Supabase file URL: {document['supabase_file_url']}")
                    return RedirectResponse(url=document['supabase_file_url'])
                
                # If we have a local file path, try to serve it
                if document.get('file_path') and os.path.exists(document['file_path']):
                    logger.info(f"Serving document from database path: {document['file_path']}")
                    return FileResponse(
                        path=document['file_path'],
                        filename=decoded_filename,
                        media_type='application/pdf' if decoded_filename.endswith('.pdf') else 'application/octet-stream'
                    )
            
        except Exception as db_error:
            logger.error(f"Error querying database for document: {str(db_error)}")
        
        # Document not found
        logger.error(f"Document not found: {decoded_filename}")
        raise HTTPException(status_code=404, detail=f"Document '{decoded_filename}' not found")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error serving document: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error serving document: {str(e)}")

# Feedback endpoints
@app.post("/api/feedback")
async def submit_feedback(feedback: FeedbackData):
    """Submit user feedback."""
    logger.info(f"Received feedback with issue type: {feedback.issue_type}")
    result = send_feedback_email(feedback)
    return result

@app.get("/api/feedback/emails")
async def get_feedback_notification_emails():
    """Get feedback notification email addresses."""
    emails = get_feedback_emails()
    logger.info(f"Retrieved feedback emails: {emails}")
    return {"emails": emails}

@app.put("/api/feedback/emails")
async def update_feedback_notification_emails(config: FeedbackEmailConfig):
    """Update feedback notification email addresses."""
    result = update_feedback_emails(config.emails)
    logger.info(f"Updated feedback emails: {config.emails}")
    return result
