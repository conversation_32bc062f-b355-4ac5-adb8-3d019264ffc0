{"name": "railgpt-frontend", "version": "1.0.0", "description": "RailGPT - AI-powered Railway Information System Frontend", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/jest": "^27.5.2", "@types/node": "^16.18.68", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "react-router-dom": "^6.20.1", "@types/react-router-dom": "^5.3.3", "axios": "^1.6.2", "@supabase/supabase-js": "^2.38.4", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "react-pdf": "^7.5.1", "pdfjs-dist": "^3.4.120", "lucide-react": "^0.294.0", "@heroicons/react": "^2.0.18", "tailwindcss": "^3.3.6", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "clsx": "^2.0.0", "classnames": "^2.3.2", "react-hot-toast": "^2.4.1", "react-loading-skeleton": "^3.3.1", "date-fns": "^2.30.0", "react-dropzone": "^14.2.3", "react-syntax-highlighter": "^15.5.0", "@types/react-syntax-highlighter": "^15.5.11", "react-intersection-observer": "^9.5.3", "framer-motion": "^10.16.16", "react-helmet-async": "^2.0.4", "react-error-boundary": "^4.0.11", "lodash": "^4.17.21", "@types/lodash": "^4.14.202", "uuid": "^9.0.1", "@types/uuid": "^9.0.7", "react-virtualized": "^9.22.5", "@types/react-virtualized": "^9.21.29", "react-window": "^1.8.8", "@types/react-window": "^1.8.8", "react-select": "^5.8.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "react-query": "^3.39.3", "zustand": "^4.4.7", "react-beautiful-dnd": "^13.1.1", "@types/react-beautiful-dnd": "^13.1.8", "react-table": "^7.8.0", "@types/react-table": "^7.7.18", "react-modal": "^3.16.1", "@types/react-modal": "^3.16.3", "react-tooltip": "^5.25.0", "react-copy-to-clipboard": "^5.1.0", "@types/react-copy-to-clipboard": "^5.0.7", "react-lazyload": "^3.2.0", "@types/react-lazyload": "^3.2.3", "react-infinite-scroll-component": "^6.1.0", "react-use": "^17.4.2", "react-hotkeys-hook": "^4.4.1", "react-confetti": "^6.1.0", "react-spring": "^9.7.3", "react-transition-group": "^4.4.5", "@types/react-transition-group": "^4.4.10"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}", "type-check": "tsc --noEmit", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js", "clean": "rm -rf build node_modules", "reinstall": "npm run clean && npm install", "dev": "npm start", "preview": "npm run build && npx serve -s build", "deploy": "npm run build && echo 'Build completed for deployment'"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"@typescript-eslint/no-unused-vars": "warn", "react-hooks/exhaustive-deps": "warn", "no-console": "warn"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-pdf": "^7.0.0", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.8.0", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "cross-env": "^7.0.3", "serve": "^14.2.1", "bundle-analyzer": "^0.1.0"}, "proxy": "http://localhost:8000", "homepage": ".", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "keywords": ["railgpt", "railway", "ai", "chatbot", "document-management", "react", "typescript", "supabase"], "author": "RailGPT Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/railgpt.git"}, "bugs": {"url": "https://github.com/your-username/railgpt/issues"}}