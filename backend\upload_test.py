import requests
import os

# Upload a document to test table extraction
url = "http://localhost:8000/api/upload-document"

# Check if the file exists
file_path = "../data/uploads/3. SBP IOT Based Water Level Quotation V2.docx"
if not os.path.exists(file_path):
    print(f"File not found: {file_path}")
    # Try alternative path
    file_path = "../data/uploads/3. SBP IOT Based Water Level Quotation V2.pdf"
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        # List available files
        upload_dir = "../data/uploads"
        if os.path.exists(upload_dir):
            files = os.listdir(upload_dir)
            print(f"Available files: {files}")
            # Use the first PDF file found
            pdf_files = [f for f in files if f.endswith('.pdf')]
            if pdf_files:
                file_path = os.path.join(upload_dir, pdf_files[0])
                print(f"Using: {file_path}")
            else:
                print("No PDF files found")
                exit(1)
        else:
            print("Upload directory not found")
            exit(1)

# Upload the file
with open(file_path, 'rb') as f:
    files = {'file': f}
    data = {
        'extract_tables': 'true',
        'extract_images': 'true',
        'extract_charts': 'true'
    }
    
    response = requests.post(url, files=files, data=data)
    
    if response.status_code == 200:
        result = response.json()
        print("✅ Upload successful!")
        print(f"Document ID: {result.get('document_id')}")
        print(f"Chunks created: {result.get('chunks_created')}")
        print(f"Visual content: {result.get('visual_content_found')}")
    else:
        print(f"❌ Upload failed: {response.status_code}")
        print(response.text) 