"""
Demo script to test and fix source attribution in chat answers.
This script sets up a mock query and response to demonstrate proper source attribution
with correct document names and page numbers.
"""

import os
import json
import logging
from typing import Dict, List, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def demo_source_attribution_fix():
    """
    Demonstrate proper source attribution with correct document names and page numbers.
    """
    logger.info("Starting source attribution demo fix")
    
    # Create mock document chunks representing a real query result
    mock_chunks = [
        {
            "document_id": "07c74ede-f0dc-47f4-9ba3-999a4ec9b823",
            "text": "Faster Response Time: Enables staff to take immediate action and minimize train stoppages.",
            "page_number": 1,
            "similarity": 0.85,
            "filename": "ACP 110V (5).pdf"
        },
        {
            "document_id": "07c74ede-f0dc-47f4-9ba3-999a4ec9b823",
            "text": "Improved Accountability: Records every ACP incident.",
            "page_number": 1,
            "similarity": 0.82,
            "filename": "ACP 110V (5).pdf"
        },
        {
            "document_id": "07c74ede-f0dc-47f4-9ba3-999a4ec9b823",
            "text": "Enhanced Passenger Experience: Ensures passenger safety with an efficient emergency response system.",
            "page_number": 2,
            "similarity": 0.79,
            "filename": "ACP 110V (5).pdf"
        },
        {
            "document_id": "07c74ede-f0dc-47f4-9ba3-999a4ec9b823",
            "text": "The document also contains images related to VASP Enterprises, their logos/emblems/symbols.",
            "page_number": 2,
            "similarity": 0.75,
            "filename": "ACP 110V (5).pdf"
        }
    ]
    
    # Process chunks and create sources dict - similar to generate_clean_answer_with_sources
    sources_dict = {}
    
    for chunk in mock_chunks:
        filename = chunk.get("filename", "Unknown document")
        page = chunk.get("page_number", 1)
        
        # Create source key for deduplication
        source_key = f"{filename}"
        
        # Initialize source entry if not exists
        if source_key not in sources_dict:
            sources_dict[source_key] = {
                "source_type": "document",
                "filename": filename,
                "name": os.path.basename(filename),
                "pages": []
            }
        
        # Add page if not already present - THIS IS THE KEY PART
        if page not in sources_dict[source_key]["pages"]:
            sources_dict[source_key]["pages"].append(page)
            logger.info(f"Added page {page} to source {filename}")
    
    # Convert sources_dict to list for response
    clean_sources = list(sources_dict.values())
    logger.info(f"Created {len(clean_sources)} source entries with pages: {[s.get('pages') for s in clean_sources]}")
    
    # Frontend processing - similar to processDocumentSources in frontend
    processed_sources = []
    for source in clean_sources:
        filename = source.get('filename', 'Unknown Document')
        pages = sorted(source.get('pages', []))
        
        if not pages:
            continue
            
        page_text = f"Page {pages[0]}" if len(pages) == 1 else f"Pages {', '.join(map(str, pages))}"
        
        processed_sources.append({
            "text": f"{filename} – {page_text}",
            "link": f"/viewer?file={filename}&page={pages[0]}",
            "isDocument": True
        })
    
    # Display results as they should appear in frontend
    logger.info("Source attribution as it should appear in chat:")
    logger.info("==============================================")
    logger.info("Sources:")
    for source in processed_sources:
        logger.info(f"- {source['text']}")
    
    # Return result object as would be shown in app
    return {
        "answer": "Benefits of the ACP 110V system include faster response time, improved accountability, reduced misuse through identifying sections with frequent ACP usage, data-driven decision making, and enhanced passenger safety.",
        "sources": processed_sources
    }

# Run the demo
if __name__ == "__main__":
    result = demo_source_attribution_fix()
    logger.info("\nFull response JSON that would be sent to frontend:")
    logger.info(json.dumps(result, indent=2))
