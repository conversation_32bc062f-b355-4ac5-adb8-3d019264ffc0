import React from 'react';
import { Link } from 'react-router-dom';
import { formatPageRange, truncateText } from '../../services/formatUtils';

interface Source {
  id: string;
  document_id?: string;
  website_id?: string;
  filename?: string;
  url?: string;
  title?: string;
  page_numbers?: number[];
  chunk_indices?: number[];
  relevance_score?: number;
}

interface SourceCitationProps {
  sources: Source[];
  documentSources?: Source[];
  websiteSources?: Source[];
}

const SourceCitation: React.FC<SourceCitationProps> = ({ 
  sources, 
  documentSources = [], 
  websiteSources = [] 
}) => {
  // Use specific source types if available, otherwise use generic sources
  const docSources = documentSources.length > 0 ? documentSources : 
    sources?.filter(s => s.document_id || s.filename) || [];

  const webSources = websiteSources.length > 0 ? websiteSources : 
    sources?.filter(s => s.website_id || s.url) || [];

  if (!docSources.length && !webSources.length) {
    return null;
  }

  return (
    <div className="mt-2 text-sm text-gray-600">
      <p className="font-medium">Sources:</p>
      <ul className="list-disc pl-5 mt-1 space-y-1">
        {docSources.map((source, index) => {
          // Extract and normalize page numbers array
          const pages = source.page_numbers ? 
            (Array.isArray(source.page_numbers) ? source.page_numbers : [source.page_numbers]) : 
            [];

          // Format page numbers for display using utility function
          const pageStr = pages.length > 0 ? 
            ` – ${formatPageRange(pages)}` : 
            '';

          return (
            <li key={`doc-${index}-${source.document_id || source.id}`}>
              <Link 
                to={`/documents/${source.document_id || source.id}`}
                className="text-blue-600 hover:underline"
                title={`View document: ${source.filename || 'Document'}`}
              >
                {truncateText(source.filename || 'Document', 60)}
              </Link>
              {pageStr}
              {source.relevance_score ? 
                <span className="text-gray-500 text-xs ml-1">(Relevance: {(source.relevance_score * 100).toFixed(0)}%)</span> : 
                null
              }
            </li>
          );
        })}

        {webSources.map((source, index) => (
          <li key={`web-${index}-${source.website_id || source.id}`}>
            <a 
              href={source.url} 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline"
              title={source.url}
            >
              {source.title ? truncateText(source.title, 60) : truncateText(source.url || 'Website', 60)}
            </a>
            {source.relevance_score ? 
              <span className="text-gray-500 text-xs ml-1">(Relevance: {(source.relevance_score * 100).toFixed(0)}%)</span> : 
              null
            }
          </li>
        ))}
      </ul>
    </div>
  );
};

export default SourceCitation;
