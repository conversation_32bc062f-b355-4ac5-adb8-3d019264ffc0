# Fixed Category Management API Endpoints for RailGPT
# Integrated with Supabase database for persistent storage

from fastapi import APIRouter, HTTPException, Body
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import logging
import uuid
from datetime import datetime

# Initialize logger
logger = logging.getLogger(__name__)

# Create router for category management endpoints
router = APIRouter(prefix="/api/categories", tags=["categories"])

# Pydantic models for request/response
class CategoryBase(BaseModel):
    name: str
    type: str  # main_category, category, sub_category, minor_category
    parent_id: Optional[str] = None
    description: Optional[str] = None
    sort_order: Optional[int] = 0

class CategoryCreate(CategoryBase):
    pass

class CategoryUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    sort_order: Optional[int] = None
    is_active: Optional[bool] = None

class Category(CategoryBase):
    id: str
    is_active: bool
    created_at: str
    updated_at: str
    full_path: Optional[str] = None
    level: Optional[int] = None

class CategoryHierarchy(BaseModel):
    id: str
    name: str
    type: str
    parent_id: Optional[str] = None
    description: Optional[str] = None
    sort_order: int
    is_active: bool
    created_at: str
    updated_at: str
    full_path: str
    level: int
    children: List['CategoryHierarchy'] = []

class WebsiteCategory(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    is_active: bool
    sort_order: int
    created_at: str
    updated_at: str

class WebsiteCategoryCreate(BaseModel):
    name: str
    description: Optional[str] = None
    sort_order: Optional[int] = 0

# Initialize Supabase client with proper error handling
def get_supabase_client():
    """Get Supabase client with proper error handling."""
    try:
        from supabase_client import supabase
        
        # Check if supabase client is properly initialized
        if not supabase:
            logger.error("Supabase client is None")
            return None
            
        if not hasattr(supabase, 'supabase'):
            logger.error("Supabase client does not have 'supabase' attribute")
            return None
            
        if supabase.supabase is None:
            logger.error("Supabase client.supabase is None")
            return None
            
        return supabase
        
    except ImportError as e:
        logger.error(f"Failed to import supabase_client: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Error getting Supabase client: {str(e)}")
        return None

# Database helper functions
async def get_categories_from_db(table_name: str = 'document_categories'):
    """Fetch all categories from Supabase database."""
    try:
        supabase = get_supabase_client()
        if not supabase:
            logger.error("Supabase client not available")
            return []

        # Get all active categories from database
        response = supabase.supabase.table(table_name).select('*').eq('is_active', True).order('sort_order').execute()

        if not response.data:
            return []

        # Build full_path and level for each category
        categories = response.data
        category_map = {str(cat['id']): cat for cat in categories}

        # Calculate full_path and level for each category
        def calculate_path_and_level(cat_id, visited=None):
            if visited is None:
                visited = set()
            
            if cat_id in visited:
                return "", 0  # Circular reference protection
            
            visited.add(cat_id)
            
            if cat_id not in category_map:
                return "", 0
            
            cat = category_map[cat_id]
            if not cat.get('parent_id'):
                return cat['name'], 1
            
            parent_path, parent_level = calculate_path_and_level(cat['parent_id'], visited.copy())
            if parent_path:
                return f"{parent_path} > {cat['name']}", parent_level + 1
            else:
                return cat['name'], 1

        # Add full_path and level to each category
        for cat in categories:
            cat['full_path'], cat['level'] = calculate_path_and_level(str(cat['id']))

        return categories

    except Exception as e:
        logger.error(f"Error fetching categories from database: {str(e)}")
        return []

async def create_category_in_db(category_data: dict, table_name: str = 'document_categories'):
    """Create a new category in the database."""
    try:
        supabase = get_supabase_client()
        if not supabase:
            raise Exception("Database connection not available")

        # Insert the category
        response = supabase.supabase.table(table_name).insert(category_data).execute()

        if response.data:
            return response.data[0]
        else:
            raise Exception("Failed to create category in database")

    except Exception as e:
        logger.error(f"Error creating category in database: {str(e)}")
        raise e

async def delete_category_from_db(category_id: str, table_name: str = 'document_categories'):
    """Delete a category from the database."""
    try:
        supabase = get_supabase_client()
        if not supabase:
            raise Exception("Database connection not available")

        # Soft delete by setting is_active to False
        response = supabase.supabase.table(table_name).update({'is_active': False}).eq('id', category_id).execute()

        if response.data:
            return response.data[0]
        else:
            raise Exception("Failed to delete category from database")

    except Exception as e:
        logger.error(f"Error deleting category from database: {str(e)}")
        raise e

async def update_category_in_db(category_id: str, update_data: dict, table_name: str = 'document_categories'):
    """Update a category in the database."""
    try:
        supabase = get_supabase_client()
        if not supabase:
            raise Exception("Database connection not available")

        # Add updated_at timestamp
        update_data['updated_at'] = datetime.utcnow().isoformat()

        # Update the category
        response = supabase.supabase.table(table_name).update(update_data).eq('id', category_id).execute()

        if response.data:
            return response.data[0]
        else:
            raise Exception("Failed to update category in database")

    except Exception as e:
        logger.error(f"Error updating category in database: {str(e)}")
        raise e

# Helper function to build category hierarchy
def build_hierarchy(categories: List[Dict[str, Any]]) -> List[CategoryHierarchy]:
    """Build hierarchical structure from flat category list."""
    if not categories:
        return []

    # Create a map for quick lookup
    category_map = {str(cat['id']): cat for cat in categories}
    
    # Add children list to each category
    for cat in categories:
        cat['children'] = []
    
    # Build parent-child relationships
    root_categories = []
    
    for cat in categories:
        parent_id = cat.get('parent_id')
        if parent_id and str(parent_id) in category_map:
            # Add to parent's children
            category_map[str(parent_id)]['children'].append(cat)
        else:
            # Root category
            root_categories.append(cat)
    
    # Sort function for categories
    def sort_categories(cats):
        cats.sort(key=lambda x: (x.get('sort_order', 0), x.get('name', '')))
        for cat in cats:
            if cat.get('children'):
                sort_categories(cat['children'])
    
    sort_categories(root_categories)
    return root_categories

# Test endpoint to check Supabase connection
@router.get("/test")
async def test_categories():
    """Test endpoint to check Supabase connection."""
    try:
        logger.info("Testing Supabase connection for categories")

        supabase = get_supabase_client()
        if not supabase:
            return {
                "success": False,
                "error": "Supabase client not initialized"
            }

        # Simple query to test connection
        response = supabase.supabase.table('document_categories').select('id,name').limit(3).execute()

        logger.info(f"Test query returned: {response.data}")
        return {
            "success": True,
            "message": "Supabase connection working",
            "sample_data": response.data
        }

    except Exception as e:
        logger.error(f"Error in test_categories: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

# Get all document categories with hierarchy
@router.get("/", response_model=List[CategoryHierarchy])
async def get_categories():
    """Get all document categories with their hierarchy structure."""
    try:
        logger.info("Fetching document categories from Supabase database")

        # Get categories from database
        db_categories = await get_categories_from_db('document_categories')

        logger.info(f"Retrieved {len(db_categories) if db_categories else 0} document categories from database")

        if not db_categories:
            logger.warning("No categories found in database, returning empty list")
            return []

        # Build hierarchy
        hierarchy = build_hierarchy(db_categories)
        
        logger.info(f"Built hierarchy with {len(hierarchy)} root categories")
        return hierarchy

    except Exception as e:
        logger.error(f"Error fetching document categories: {str(e)}")
        # Return empty list instead of raising exception to prevent frontend errors
        return []

# Get all website categories with hierarchy
@router.get("/website", response_model=List[CategoryHierarchy])
async def get_website_categories():
    """Get all website categories with their hierarchy structure."""
    try:
        logger.info("Fetching website categories from Supabase database")

        # Get categories from database
        db_categories = await get_categories_from_db('website_categories')

        logger.info(f"Retrieved {len(db_categories) if db_categories else 0} website categories from database")

        if not db_categories:
            logger.warning("No website categories found in database, returning empty list")
            return []

        # Build hierarchy
        hierarchy = build_hierarchy(db_categories)
        
        logger.info(f"Built website hierarchy with {len(hierarchy)} root categories")
        return hierarchy

    except Exception as e:
        logger.error(f"Error fetching website categories: {str(e)}")
        # Return empty list instead of raising exception to prevent frontend errors
        return []

# Create new document category
@router.post("/", response_model=Dict[str, Any])
async def create_category(category: CategoryCreate):
    """Create a new document category."""
    try:
        logger.info(f"Creating document category: {category.name}")

        # Validate category type
        valid_types = ['main_category', 'category', 'sub_category', 'minor_category']
        if category.type not in valid_types:
            raise HTTPException(status_code=400, detail=f"Invalid category type. Must be one of: {valid_types}")

        supabase = get_supabase_client()
        if not supabase:
            raise HTTPException(status_code=500, detail="Database connection not available")

        # Check if parent exists (if parent_id is provided)
        if category.parent_id:
            parent_response = supabase.supabase.table('document_categories').select('id,name,type').eq('id', category.parent_id).execute()
            if not parent_response.data:
                raise HTTPException(status_code=400, detail="Parent category not found")

        # Check if category name already exists at the same level
        existing_response = supabase.supabase.table('document_categories').select('id').eq('name', category.name).eq('parent_id', category.parent_id).execute()
        if existing_response.data:
            raise HTTPException(status_code=400, detail="Category with this name already exists at this level")

        # Prepare category data
        category_data = {
            'id': str(uuid.uuid4()),
            'name': category.name,
            'type': category.type,
            'parent_id': category.parent_id,
            'description': category.description,
            'sort_order': category.sort_order or 0,
            'is_active': True,
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }

        # Create category in database
        created_category = await create_category_in_db(category_data, 'document_categories')

        logger.info(f"Successfully created document category: {created_category['id']}")

        return {
            "success": True,
            "message": "Document category created successfully",
            "category": created_category
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating document category: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating category: {str(e)}")

# Create new website category
@router.post("/website", response_model=Dict[str, Any])
async def create_website_category(category: WebsiteCategoryCreate):
    """Create a new website category."""
    try:
        logger.info(f"Creating website category: {category.name}")

        supabase = get_supabase_client()
        if not supabase:
            raise HTTPException(status_code=500, detail="Database connection not available")

        # Check if category name already exists
        existing_response = supabase.supabase.table('website_categories').select('id').eq('name', category.name).execute()
        if existing_response.data:
            raise HTTPException(status_code=400, detail="Website category with this name already exists")

        # Prepare category data
        category_data = {
            'id': str(uuid.uuid4()),
            'name': category.name,
            'description': category.description,
            'sort_order': category.sort_order or 0,
            'is_active': True,
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }

        # Create category in database
        created_category = await create_category_in_db(category_data, 'website_categories')

        logger.info(f"Successfully created website category: {created_category['id']}")

        return {
            "success": True,
            "message": "Website category created successfully",
            "category": created_category
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating website category: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating website category: {str(e)}")

# Update category
@router.put("/{category_id}", response_model=Dict[str, Any])
async def update_category(category_id: str, category_update: CategoryUpdate):
    """Update an existing category."""
    try:
        logger.info(f"Updating category: {category_id}")

        supabase = get_supabase_client()
        if not supabase:
            raise HTTPException(status_code=500, detail="Database connection not available")

        # Check if category exists
        existing_response = supabase.supabase.table('document_categories').select('*').eq('id', category_id).execute()
        if not existing_response.data:
            raise HTTPException(status_code=404, detail="Category not found")

        # Prepare update data (only include non-None values)
        update_data = {}
        if category_update.name is not None:
            update_data['name'] = category_update.name
        if category_update.description is not None:
            update_data['description'] = category_update.description
        if category_update.sort_order is not None:
            update_data['sort_order'] = category_update.sort_order
        if category_update.is_active is not None:
            update_data['is_active'] = category_update.is_active

        if not update_data:
            raise HTTPException(status_code=400, detail="No update data provided")

        # Update category in database
        updated_category = await update_category_in_db(category_id, update_data, 'document_categories')

        logger.info(f"Successfully updated category: {category_id}")

        return {
            "success": True,
            "message": "Category updated successfully",
            "category": updated_category
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating category: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error updating category: {str(e)}")

# Delete category
@router.delete("/{category_id}", response_model=Dict[str, Any])
async def delete_category(category_id: str):
    """Delete a category (soft delete)."""
    try:
        logger.info(f"Deleting category: {category_id}")

        supabase = get_supabase_client()
        if not supabase:
            raise HTTPException(status_code=500, detail="Database connection not available")

        # Check if category exists
        existing_response = supabase.supabase.table('document_categories').select('*').eq('id', category_id).execute()
        if not existing_response.data:
            raise HTTPException(status_code=404, detail="Category not found")

        # Check if category has children
        children_response = supabase.supabase.table('document_categories').select('id').eq('parent_id', category_id).eq('is_active', True).execute()
        if children_response.data:
            raise HTTPException(status_code=400, detail="Cannot delete category with active children")

        # Soft delete category
        deleted_category = await delete_category_from_db(category_id, 'document_categories')

        logger.info(f"Successfully deleted category: {category_id}")

        return {
            "success": True,
            "message": "Category deleted successfully",
            "category": deleted_category
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting category: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting category: {str(e)}")
