"""
Optimized Supabase Client for RailGPT
Handles database operations with connection pooling and error handling
"""

import logging
import os
import asyncio
from typing import Dict, List, Any, Optional
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()
logger = logging.getLogger(__name__)

class OptimizedSupabaseClient:
    """
    Optimized Supabase client with connection pooling and error handling
    """
    
    def __init__(self):
        self.url = os.getenv("SUPABASE_URL")
        self.key = os.getenv("SUPABASE_ANON_KEY")
        self.client = None
        self.connection_pool_size = 10
        self.max_retries = 3
        
        if self.url and self.key:
            try:
                self.client = create_client(self.url, self.key)
                logger.info("✅ Supabase client initialized")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Supabase client: {str(e)}")
        else:
            logger.error("❌ Missing Supabase credentials")
    
    async def fetch(
        self, 
        query: str, 
        params: Optional[List[Any]] = None,
        retry_count: int = 0
    ) -> List[Dict[str, Any]]:
        """
        Execute a parameterized query with retry logic
        """
        if not self.client:
            logger.error("Supabase client not initialized")
            return []
        
        try:
            # Execute the query using RPC or direct SQL
            if query.strip().startswith('SELECT'):
                # Use rpc for complex queries
                result = self.client.rpc('execute_sql', {
                    'sql_query': query,
                    'sql_params': params or []
                }).execute()
            else:
                # Direct table operations
                result = self.client.from_("document_chunks").select("*").execute()
            
            if result.data:
                return result.data
            else:
                logger.warning("No data returned from query")
                return []
                
        except Exception as e:
            logger.error(f"Database query error: {str(e)}")
            
            # Retry logic
            if retry_count < self.max_retries:
                logger.info(f"Retrying query (attempt {retry_count + 1})")
                await asyncio.sleep(1)  # Brief delay before retry
                return await self.fetch(query, params, retry_count + 1)
            
            return []
    
    async def search_document_chunks(
        self,
        query_embedding: List[float],
        query_text: str = "",
        top_k: int = 30,
        min_threshold: float = 0.3
    ) -> List[Dict[str, Any]]:
        """
        Optimized document search with vector similarity
        """
        try:
            # Use the match_documents RPC function
            result = self.client.rpc('match_documents', {
                'query_embedding': query_embedding,
                'match_threshold': min_threshold,
                'match_count': top_k
            }).execute()
            
            if result.data:
                return result.data
            return []
            
        except Exception as e:
            logger.error(f"Document search error: {str(e)}")
            return []
    
    async def search_website_chunks(
        self,
        query_embedding: List[float],
        query_text: str = "",
        top_k: int = 20,
        min_threshold: float = 0.2
    ) -> List[Dict[str, Any]]:
        """
        Optimized website search with vector similarity
        """
        try:
            # Use the match_websites RPC function
            result = self.client.rpc('match_websites', {
                'query_embedding': query_embedding,
                'match_threshold': min_threshold,
                'match_count': top_k
            }).execute()
            
            if result.data:
                return result.data
            return []
            
        except Exception as e:
            logger.error(f"Website search error: {str(e)}")
            return []
    
    def get_documents(self) -> List[Dict[str, Any]]:
        """Get all documents"""
        try:
            result = self.client.from_("documents").select("*").execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Error fetching documents: {str(e)}")
            return []
    
    def get_websites(self) -> List[Dict[str, Any]]:
        """Get all websites"""
        try:
            result = self.client.from_("websites").select("*").execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Error fetching websites: {str(e)}")
            return []
    
    def execute_query(self, query: str, params: Dict[str, Any] = None) -> Any:
        """
        Execute a raw SQL query (for backward compatibility)
        """
        try:
            if not self.client:
                return {"error": "Client not initialized"}
            
            # For complex queries, use RPC
            result = self.client.rpc('execute_raw_sql', {
                'sql_query': query,
                'query_params': params or {}
            }).execute()
            
            return result.data
            
        except Exception as e:
            logger.error(f"Raw query execution error: {str(e)}")
            return {"error": str(e)}
    
    def health_check(self) -> Dict[str, Any]:
        """Check database connection health"""
        try:
            if not self.client:
                return {"status": "error", "message": "Client not initialized"}
            
            # Simple query to test connection
            result = self.client.from_("documents").select("id").limit(1).execute()
            
            return {
                "status": "healthy",
                "connection": "active",
                "url": self.url[:50] + "..." if self.url else "None"
            }
        except Exception as e:
            return {
                "status": "error",
                "message": str(e)
            }

# Global Supabase client instance
supabase = OptimizedSupabaseClient() 