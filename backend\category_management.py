# Enhanced Category Management API Endpoints for RailGPT
# Comprehensive four-level category system with full CRUD operations
# Integrated with Supabase database for persistent storage

from fastapi import APIRouter, HTTPException, Body, Query
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
import logging
import uuid
from datetime import datetime
import json

# Initialize logger first
logger = logging.getLogger(__name__)

# Initialize Supabase client with proper error handling
def get_supabase_client():
    """Get Supabase client with proper error handling."""
    try:
        from supabase_client import supabase

        # Check if supabase client is properly initialized
        if not supabase:
            logger.error("Supabase client is None")
            return None

        # The supabase object is a SupabaseClient instance
        # We need to check if it has the actual supabase client
        if not hasattr(supabase, 'supabase'):
            logger.error("Supabase client does not have 'supabase' attribute")
            return None

        if supabase.supabase is None:
            logger.error("Supabase client.supabase is None")
            return None

        # Test the connection by trying a simple operation
        try:
            # Try to access a table to verify the connection works
            test_result = supabase.supabase.table('document_categories').select('id').limit(1).execute()
            logger.info("Supabase client connection verified")
        except Exception as test_error:
            logger.warning(f"Supabase client connection test failed: {str(test_error)}")
            # Still return the client as it might work for other operations

        return supabase

    except ImportError as e:
        logger.error(f"Failed to import supabase_client: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Error getting Supabase client: {str(e)}")
        return None

# Create router for category management endpoints
router = APIRouter(prefix="/api/categories", tags=["categories"])

# Enhanced Pydantic models for comprehensive category management
class CategoryBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255, description="Category name")
    type: str = Field(..., pattern="^(main_category|category|sub_category|minor_category)$", description="Category type")
    parent_id: Optional[str] = Field(None, description="Parent category UUID")
    description: Optional[str] = Field(None, max_length=1000, description="Category description")
    sort_order: Optional[int] = Field(0, ge=0, description="Sort order for display")

class CategoryCreate(CategoryBase):
    """Model for creating new categories"""
    pass

class CategoryUpdate(BaseModel):
    """Model for updating existing categories"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    sort_order: Optional[int] = Field(None, ge=0)
    is_active: Optional[bool] = None
    parent_id: Optional[str] = None

class Category(CategoryBase):
    """Complete category model with all fields"""
    id: str
    is_active: bool
    created_at: str
    updated_at: str

class CategoryHierarchy(Category):
    """Category with hierarchy information"""
    full_path: str
    level: int
    children: Optional[List['CategoryHierarchy']] = None

class CategoryTree(BaseModel):
    """Tree structure for category hierarchy"""
    categories: List[CategoryHierarchy]
    total_count: int
    max_depth: int

class BulkCategoryUpdate(BaseModel):
    """Model for bulk category updates"""
    category_ids: List[str] = Field(..., min_items=1)
    updates: CategoryUpdate

class CategoryAssignment(BaseModel):
    """Model for assigning categories to documents/websites"""
    main_category_id: Optional[str] = None
    category_id: Optional[str] = None
    sub_category_id: Optional[str] = None
    minor_category_id: Optional[str] = None

class DocumentCategoryUpdate(BaseModel):
    """Model for updating document categories"""
    document_ids: List[str] = Field(..., min_items=1)
    categories: CategoryAssignment

class WebsiteCategoryUpdate(BaseModel):
    """Model for updating website categories"""
    website_ids: List[str] = Field(..., min_items=1)
    categories: CategoryAssignment

# Additional models for website categories
class WebsiteCategory(BaseModel):
    """Website category model"""
    id: str
    name: str
    type: str
    parent_id: Optional[str] = None
    description: Optional[str] = None
    is_active: bool
    sort_order: int
    created_at: str
    updated_at: str

class WebsiteCategoryCreate(CategoryBase):
    """Model for creating website categories"""
    pass

# Database helper functions
async def get_categories_from_db(table_name: str = 'document_categories'):
    """Fetch all categories from Supabase database."""
    try:
        supabase = get_supabase_client()
        if not supabase:
            logger.error("Supabase client not available")
            return []

        # Get all active categories from database
        response = supabase.supabase.table(table_name).select('*').eq('is_active', True).order('sort_order').execute()

        if not response.data:
            return []

        # Build full_path and level for each category
        categories = response.data
        category_map = {str(cat['id']): cat for cat in categories}

        # Calculate full_path and level for each category
        def calculate_path_and_level(cat_id, visited=None):
            if visited is None:
                visited = set()

            if cat_id in visited:
                return "", 0  # Circular reference protection

            visited.add(cat_id)
            cat = category_map.get(cat_id)
            if not cat:
                return "", 0

            if not cat['parent_id']:
                # Root category
                cat['full_path'] = cat['name']
                cat['level'] = 0
                return cat['name'], 0

            parent_path, parent_level = calculate_path_and_level(str(cat['parent_id']), visited.copy())
            cat['full_path'] = f"{parent_path} > {cat['name']}" if parent_path else cat['name']
            cat['level'] = parent_level + 1
            return cat['full_path'], cat['level']

        # Calculate paths and levels for all categories
        for cat in categories:
            calculate_path_and_level(str(cat['id']))

        return categories

    except Exception as e:
        logger.error(f"Error fetching categories from database: {str(e)}")
        # Return empty list as fallback
        return []

async def create_category_in_db(category_data: dict, table_name: str = 'document_categories'):
    """Create a new category in the database."""
    try:
        supabase = get_supabase_client()
        if not supabase:
            logger.error("Supabase client not available")
            raise Exception("Database connection not available")

        # Clean up the category data to ensure proper UUID handling
        clean_data = {}
        for key, value in category_data.items():
            if value is None and key == 'parent_id':
                # Skip None parent_id to let database handle it
                continue
            elif value == "None" or value == "null":
                # Convert string "None" to actual None
                clean_data[key] = None
            else:
                clean_data[key] = value

        logger.info(f"Creating category in {table_name} with data: {clean_data}")

        # Insert the category
        response = supabase.supabase.table(table_name).insert(clean_data).execute()

        if response.data:
            logger.info(f"Successfully created category: {response.data[0]}")
            return response.data[0]
        else:
            logger.error(f"No data returned from insert: {response}")
            raise Exception("Failed to create category in database")

    except Exception as e:
        logger.error(f"Error creating category in database: {str(e)}")
        raise e

async def delete_category_from_db(category_id: str, table_name: str = 'document_categories'):
    """Delete a category from the database."""
    try:
        supabase = get_supabase_client()
        if not supabase:
            raise Exception("Database connection not available")

        # Check if category has children
        children_response = supabase.supabase.table(table_name).select('id').eq('parent_id', category_id).execute()
        if children_response.data:
            raise HTTPException(status_code=400, detail="Cannot delete category that has subcategories")

        # Delete the category
        response = supabase.supabase.table(table_name).delete().eq('id', category_id).execute()

        if not response.data:
            raise HTTPException(status_code=404, detail="Category not found")

        return response.data[0]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting category from database: {str(e)}")
        raise e

# Fallback mock data (kept for reference, but not used when DB is available)
MOCK_CATEGORIES_FALLBACK = [
    # Main Categories
    {
        "id": "main-1",
        "name": "Safety",
        "type": "main_category",
        "parent_id": None,
        "description": "Safety-related documents and procedures",
        "is_active": True,
        "sort_order": 1,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "full_path": "Safety",
        "level": 0,
        "children": []
    },
    {
        "id": "main-2",
        "name": "Technical",
        "type": "main_category",
        "parent_id": None,
        "description": "Technical documentation and specifications",
        "is_active": True,
        "sort_order": 2,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "full_path": "Technical",
        "level": 0,
        "children": []
    },
    {
        "id": "main-3",
        "name": "Operations",
        "type": "main_category",
        "parent_id": None,
        "description": "Operational procedures and guidelines",
        "is_active": True,
        "sort_order": 3,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "full_path": "Operations",
        "level": 0,
        "children": []
    },
    {
        "id": "main-4",
        "name": "Administrative",
        "type": "main_category",
        "parent_id": None,
        "description": "Administrative documents and policies",
        "is_active": True,
        "sort_order": 4,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "full_path": "Administrative",
        "level": 0,
        "children": []
    },

    # Categories under Safety
    {
        "id": "cat-1",
        "name": "Emergency Procedures",
        "type": "category",
        "parent_id": "main-1",
        "description": "Emergency response and safety procedures",
        "is_active": True,
        "sort_order": 1,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "full_path": "Safety > Emergency Procedures",
        "level": 1,
        "children": []
    },
    {
        "id": "cat-2",
        "name": "Risk Assessment",
        "type": "category",
        "parent_id": "main-1",
        "description": "Risk assessment and management documents",
        "is_active": True,
        "sort_order": 2,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "full_path": "Safety > Risk Assessment",
        "level": 1,
        "children": []
    },

    # Categories under Technical
    {
        "id": "cat-3",
        "name": "System Specifications",
        "type": "category",
        "parent_id": "main-2",
        "description": "Technical system specifications and requirements",
        "is_active": True,
        "sort_order": 1,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "full_path": "Technical > System Specifications",
        "level": 1,
        "children": []
    },
    {
        "id": "cat-4",
        "name": "Maintenance",
        "type": "category",
        "parent_id": "main-2",
        "description": "Maintenance procedures and schedules",
        "is_active": True,
        "sort_order": 2,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "full_path": "Technical > Maintenance",
        "level": 1,
        "children": []
    },

    # Sub Categories under Emergency Procedures
    {
        "id": "sub-1",
        "name": "Fire Safety",
        "type": "sub_category",
        "parent_id": "cat-1",
        "description": "Fire safety procedures and protocols",
        "is_active": True,
        "sort_order": 1,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "full_path": "Safety > Emergency Procedures > Fire Safety",
        "level": 2,
        "children": []
    },
    {
        "id": "sub-2",
        "name": "Medical Emergency",
        "type": "sub_category",
        "parent_id": "cat-1",
        "description": "Medical emergency response procedures",
        "is_active": True,
        "sort_order": 2,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "full_path": "Safety > Emergency Procedures > Medical Emergency",
        "level": 2,
        "children": []
    },

    # Sub Categories under System Specifications
    {
        "id": "sub-3",
        "name": "Hardware",
        "type": "sub_category",
        "parent_id": "cat-3",
        "description": "Hardware specifications and requirements",
        "is_active": True,
        "sort_order": 1,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "full_path": "Technical > System Specifications > Hardware",
        "level": 2,
        "children": []
    },
    {
        "id": "sub-4",
        "name": "Software",
        "type": "sub_category",
        "parent_id": "cat-3",
        "description": "Software specifications and requirements",
        "is_active": True,
        "sort_order": 2,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "full_path": "Technical > System Specifications > Software",
        "level": 2,
        "children": []
    },

    # Minor Categories under Fire Safety
    {
        "id": "minor-1",
        "name": "Detection Systems",
        "type": "minor_category",
        "parent_id": "sub-1",
        "description": "Fire detection system procedures",
        "is_active": True,
        "sort_order": 1,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "full_path": "Safety > Emergency Procedures > Fire Safety > Detection Systems",
        "level": 3,
        "children": []
    },
    {
        "id": "minor-2",
        "name": "Suppression Systems",
        "type": "minor_category",
        "parent_id": "sub-1",
        "description": "Fire suppression system procedures",
        "is_active": True,
        "sort_order": 2,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "full_path": "Safety > Emergency Procedures > Fire Safety > Suppression Systems",
        "level": 3,
        "children": []
    },

    # Minor Categories under Hardware
    {
        "id": "minor-3",
        "name": "Servers",
        "type": "minor_category",
        "parent_id": "sub-3",
        "description": "Server hardware specifications",
        "is_active": True,
        "sort_order": 1,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "full_path": "Technical > System Specifications > Hardware > Servers",
        "level": 3,
        "children": []
    },
    {
        "id": "minor-4",
        "name": "Network Equipment",
        "type": "minor_category",
        "parent_id": "sub-3",
        "description": "Network equipment specifications",
        "is_active": True,
        "sort_order": 2,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z",
        "full_path": "Technical > System Specifications > Hardware > Network Equipment",
        "level": 3,
        "children": []
    }
]

MOCK_WEBSITE_CATEGORIES = [
    {
        "id": "1",
        "name": "Government",
        "description": "Government websites",
        "is_active": True,
        "sort_order": 1,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    },
    {
        "id": "2",
        "name": "Railway Authorities",
        "description": "Railway authority websites",
        "is_active": True,
        "sort_order": 2,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }
]

# Helper function to build category tree structure
def build_category_tree(categories):
    """Build hierarchical tree structure from flat category list."""
    # Create a map of categories by ID
    category_map = {cat["id"]: dict(cat) for cat in categories}

    # Initialize children arrays
    for cat in category_map.values():
        cat["children"] = []

    # Build the tree structure
    root_categories = []
    for cat in category_map.values():
        if cat["parent_id"]:
            parent = category_map.get(cat["parent_id"])
            if parent:
                parent["children"].append(cat)
        else:
            root_categories.append(cat)

    # Sort categories by sort_order and name
    def sort_categories(cats):
        cats.sort(key=lambda x: (x["sort_order"], x["name"]))
        for cat in cats:
            if cat["children"]:
                sort_categories(cat["children"])

    sort_categories(root_categories)
    return root_categories

# Test endpoint to check Supabase connection
@router.get("/test")
async def test_categories():
    """Test endpoint to check Supabase connection."""
    try:
        logger.info("Testing Supabase connection for categories")

        supabase = get_supabase_client()
        if not supabase:
            return {
                "success": False,
                "error": "Supabase client not initialized"
            }

        # Simple query to test connection
        response = supabase.supabase.table('document_categories').select('id,name').limit(3).execute()

        logger.info(f"Test query returned: {response.data}")
        return {
            "success": True,
            "message": "Supabase connection working",
            "sample_data": response.data
        }

    except Exception as e:
        logger.error(f"Error in test_categories: {str(e)}")
        return {
            "success": False,
            "error": str(e)
        }

# Get all document categories with hierarchy
@router.get("/", response_model=List[CategoryHierarchy])
async def get_categories():
    """Get all document categories with their hierarchy structure."""
    try:
        logger.info("Fetching document categories from Supabase database")

        # Get categories from database
        db_categories = await get_categories_from_db('document_categories')

        logger.info(f"Retrieved {len(db_categories) if db_categories else 0} document categories from database")

        if not db_categories:
            logger.warning("No categories found in database, returning empty list")
            return []

        # Convert database format to expected format
        formatted_categories = []
        for cat in db_categories:
            formatted_cat = {
                "id": str(cat["id"]),
                "name": cat["name"],
                "type": cat["type"],
                "parent_id": str(cat["parent_id"]) if cat["parent_id"] else None,
                "description": cat.get("description", ""),
                "is_active": cat["is_active"],
                "sort_order": cat["sort_order"],
                "created_at": cat["created_at"],
                "updated_at": cat["updated_at"],
                "full_path": cat.get("full_path", cat["name"]),
                "level": cat.get("level", 0),
                "children": []
            }
            formatted_categories.append(formatted_cat)

        # Build the hierarchical tree structure
        tree_categories = build_category_tree(formatted_categories)

        logger.info(f"Successfully returned {len(tree_categories)} root categories from database")
        return tree_categories

    except Exception as e:
        logger.error(f"Error in get_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Get all document categories as flat list (for testing and frontend compatibility)
@router.get("/flat", response_model=List[CategoryHierarchy])
async def get_categories_flat():
    """Get all document categories as a flat list (not hierarchical tree)."""
    try:
        logger.info("Fetching all document categories as flat list")

        # Get categories from database
        db_categories = await get_categories_from_db('document_categories')

        logger.info(f"Retrieved {len(db_categories) if db_categories else 0} document categories from database")

        if not db_categories:
            logger.warning("No categories found in database, returning empty list")
            return []

        # Convert database format to expected format (flat list)
        formatted_categories = []
        for cat in db_categories:
            formatted_cat = {
                "id": str(cat["id"]),
                "name": cat["name"],
                "type": cat["type"],
                "parent_id": str(cat["parent_id"]) if cat["parent_id"] else None,
                "description": cat.get("description", ""),
                "is_active": cat["is_active"],
                "sort_order": cat["sort_order"],
                "created_at": cat["created_at"],
                "updated_at": cat["updated_at"],
                "full_path": cat.get("full_path", cat["name"]),
                "level": cat.get("level", 0),
                "children": []
            }
            formatted_categories.append(formatted_cat)

        # Sort by level and sort_order for logical display
        formatted_categories.sort(key=lambda x: (x["level"], x["sort_order"], x["name"]))

        logger.info(f"Successfully returned {len(formatted_categories)} categories as flat list")
        return formatted_categories

    except Exception as e:
        logger.error(f"Error in get_categories_flat: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Get all website categories with hierarchy
@router.get("/website", response_model=List[CategoryHierarchy])
async def get_website_categories():
    """Get all website categories with their hierarchy structure."""
    try:
        logger.info("Fetching website categories from Supabase database")

        # Get categories from database
        db_categories = await get_categories_from_db('website_categories')

        logger.info(f"Retrieved {len(db_categories) if db_categories else 0} website categories from database")

        if not db_categories:
            logger.warning("No website categories found in database, returning empty list")
            return []

        # Convert database format to expected format
        formatted_categories = []
        for cat in db_categories:
            formatted_cat = {
                "id": str(cat["id"]),
                "name": cat["name"],
                "type": cat["type"],
                "parent_id": str(cat["parent_id"]) if cat["parent_id"] else None,
                "description": cat.get("description", ""),
                "is_active": cat["is_active"],
                "sort_order": cat["sort_order"],
                "created_at": cat["created_at"],
                "updated_at": cat["updated_at"],
                "full_path": cat.get("full_path", cat["name"]),
                "level": cat.get("level", 0),
                "children": []
            }
            formatted_categories.append(formatted_cat)

        # Build the hierarchical tree structure
        tree_categories = build_category_tree(formatted_categories)

        logger.info(f"Successfully returned {len(tree_categories)} root website categories from database")
        return tree_categories

    except Exception as e:
        logger.error(f"Error in get_website_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Get all website categories as flat list (for testing and frontend compatibility)
@router.get("/website/flat", response_model=List[CategoryHierarchy])
async def get_website_categories_flat():
    """Get all website categories as a flat list (not hierarchical tree)."""
    try:
        logger.info("Fetching all website categories as flat list")

        # Get categories from database
        db_categories = await get_categories_from_db('website_categories')

        logger.info(f"Retrieved {len(db_categories) if db_categories else 0} website categories from database")

        if not db_categories:
            logger.warning("No website categories found in database, returning empty list")
            return []

        # Convert database format to expected format (flat list)
        formatted_categories = []
        for cat in db_categories:
            formatted_cat = {
                "id": str(cat["id"]),
                "name": cat["name"],
                "type": cat["type"],
                "parent_id": str(cat["parent_id"]) if cat["parent_id"] else None,
                "description": cat.get("description", ""),
                "is_active": cat["is_active"],
                "sort_order": cat["sort_order"],
                "created_at": cat["created_at"],
                "updated_at": cat["updated_at"],
                "full_path": cat.get("full_path", cat["name"]),
                "level": cat.get("level", 0),
                "children": []
            }
            formatted_categories.append(formatted_cat)

        # Sort by level and sort_order for logical display
        formatted_categories.sort(key=lambda x: (x["level"], x["sort_order"], x["name"]))

        logger.info(f"Successfully returned {len(formatted_categories)} website categories as flat list")
        return formatted_categories

    except Exception as e:
        logger.error(f"Error in get_website_categories_flat: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Create new category
@router.post("/", response_model=Dict[str, Any])
async def create_category(category: CategoryCreate):
    """Create a new category."""
    try:
        logger.info(f"Creating category: {category.name}")

        # Validate category type
        valid_types = ['main_category', 'category', 'sub_category', 'minor_category']
        if category.type not in valid_types:
            raise HTTPException(status_code=400, detail=f"Invalid category type. Must be one of: {valid_types}")

        supabase = get_supabase_client()
        if not supabase:
            logger.error("Supabase client not available")
            raise HTTPException(status_code=500, detail="Database connection not available")

        # Check if parent exists (if parent_id is provided)
        if category.parent_id:
            parent_response = supabase.supabase.table('document_categories').select('id,name,type').eq('id', category.parent_id).execute()
            if not parent_response.data:
                raise HTTPException(status_code=400, detail="Parent category not found")

        # Check if category name already exists at the same level
        if category.parent_id:
            existing_response = supabase.supabase.table('document_categories').select('id').eq('name', category.name).eq('parent_id', category.parent_id).execute()
        else:
            existing_response = supabase.supabase.table('document_categories').select('id').eq('name', category.name).is_('parent_id', 'null').execute()

        if existing_response.data:
            raise HTTPException(status_code=400, detail="Category with this name already exists at this level")

        # Prepare category data for database
        category_data = {
            "id": str(uuid.uuid4()),  # Generate UUID for the category
            "name": category.name,
            "type": category.type,
            "parent_id": category.parent_id if category.parent_id else None,
            "description": category.description or "",
            "sort_order": category.sort_order or 0,
            "is_active": True,
            "created_at": datetime.utcnow().isoformat() + "Z",
            "updated_at": datetime.utcnow().isoformat() + "Z"
        }

        # Create category in database
        new_category = await create_category_in_db(category_data, 'document_categories')

        logger.info(f"Successfully created category: {category.name} with ID: {new_category['id']}")
        return {
            "success": True,
            "message": "Category created successfully",
            "id": str(new_category['id']),
            "category": new_category
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in create_category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Delete category
@router.delete("/{category_id}")
async def delete_category(category_id: str):
    """Delete a category."""
    try:
        logger.info(f"Deleting category: {category_id}")

        # Delete category from database
        deleted_category = await delete_category_from_db(category_id, 'document_categories')

        logger.info(f"Successfully deleted category: {deleted_category['name']}")
        return {
            "success": True,
            "message": f"Category '{deleted_category['name']}' deleted successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in delete_category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Update document categories
@router.put("/documents/{document_id}/categories")
async def update_document_categories(document_id: str, category_update: DocumentCategoryUpdate):
    """Update the category assignment for a document."""
    try:
        logger.info(f"Updating categories for document: {document_id}")
        return {"success": True, "message": "Document categories updated successfully"}
    except Exception as e:
        logger.error(f"Error in update_document_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Bulk update document categories
@router.put("/documents/bulk-update-categories")
async def bulk_update_document_categories(
    document_ids: List[str] = Body(...),
    category_update: DocumentCategoryUpdate = Body(...)
):
    """Update categories for multiple documents at once."""
    try:
        logger.info(f"Bulk updating categories for {len(document_ids)} documents")
        return {
            "success": True,
            "message": f"Successfully updated categories for {len(document_ids)} documents"
        }
    except Exception as e:
        logger.error(f"Error in bulk_update_document_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Create new website category
@router.post("/website", response_model=Dict[str, Any])
async def create_website_category(category: CategoryCreate):
    """Create a new website category in the database."""
    try:
        logger.info(f"Creating new website category: {category.name} of type {category.type}")

        # Validate category type
        valid_types = ['main_category', 'category', 'sub_category', 'minor_category']
        if category.type not in valid_types:
            raise HTTPException(status_code=400, detail=f"Invalid category type. Must be one of: {valid_types}")

        supabase = get_supabase_client()
        if not supabase:
            logger.error("Supabase client not available")
            raise HTTPException(status_code=500, detail="Database connection not available")

        # Check if parent exists (if parent_id is provided)
        if category.parent_id:
            parent_response = supabase.supabase.table('website_categories').select('id,name,type').eq('id', category.parent_id).execute()
            if not parent_response.data:
                raise HTTPException(status_code=400, detail="Parent category not found")

        # Check if category name already exists at the same level
        if category.parent_id:
            existing_response = supabase.supabase.table('website_categories').select('id').eq('name', category.name).eq('parent_id', category.parent_id).execute()
        else:
            existing_response = supabase.supabase.table('website_categories').select('id').eq('name', category.name).is_('parent_id', 'null').execute()

        if existing_response.data:
            raise HTTPException(status_code=400, detail="Category with this name already exists at this level")

        # Prepare category data for database
        category_data = {
            "id": str(uuid.uuid4()),  # Generate UUID for the category
            "name": category.name,
            "type": category.type,
            "parent_id": category.parent_id if category.parent_id else None,
            "description": category.description or "",
            "sort_order": category.sort_order or 0,
            "is_active": True,
            "created_at": datetime.utcnow().isoformat() + "Z",
            "updated_at": datetime.utcnow().isoformat() + "Z"
        }

        # Create category in database
        new_category = await create_category_in_db(category_data, 'website_categories')

        logger.info(f"Successfully created website category: {category.name} with ID: {new_category['id']}")
        return {
            "success": True,
            "message": "Website category created successfully",
            "id": str(new_category['id']),
            "category": new_category
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in create_website_category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Delete website category
@router.delete("/website/{category_id}")
async def delete_website_category(category_id: str):
    """Delete a website category."""
    try:
        logger.info(f"Deleting website category: {category_id}")

        # Delete category from database
        deleted_category = await delete_category_from_db(category_id, 'website_categories')

        logger.info(f"Successfully deleted website category: {deleted_category['name']}")
        return {
            "success": True,
            "message": f"Website category '{deleted_category['name']}' deleted successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in delete_website_category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Update website categories
@router.put("/websites/{website_id}/categories")
async def update_website_categories(website_id: str, category_assignment: CategoryAssignment):
    """Update the category assignment for a website."""
    try:
        logger.info(f"Updating categories for website: {website_id}")
        
        # Get Supabase client
        supabase = get_supabase_client()
        if not supabase:
            raise HTTPException(status_code=503, detail="Database connection not available")
            
        # Validate the website exists
        website_response = supabase.supabase.table('websites').select('id').eq('id', website_id).execute()
        if not website_response.data:
            raise HTTPException(status_code=404, detail=f"Website with ID {website_id} not found")
            
        # Validate category IDs if provided
        update_data = {}
        if category_assignment.main_category_id:
            # Verify main category exists
            cat_response = supabase.supabase.table('website_categories').select('id').eq('id', category_assignment.main_category_id).execute()
            if not cat_response.data:
                raise HTTPException(status_code=400, detail=f"Main category {category_assignment.main_category_id} not found")
            update_data['main_category'] = category_assignment.main_category_id
            
        if category_assignment.category_id:
            # Verify category exists
            cat_response = supabase.supabase.table('website_categories').select('id').eq('id', category_assignment.category_id).execute()
            if not cat_response.data:
                raise HTTPException(status_code=400, detail=f"Category {category_assignment.category_id} not found")
            update_data['category_level2'] = category_assignment.category_id
            
        if category_assignment.sub_category_id:
            # Verify sub-category exists
            cat_response = supabase.supabase.table('website_categories').select('id').eq('id', category_assignment.sub_category_id).execute()
            if not cat_response.data:
                raise HTTPException(status_code=400, detail=f"Sub-category {category_assignment.sub_category_id} not found")
            update_data['sub_category'] = category_assignment.sub_category_id
            
        if category_assignment.minor_category_id:
            # Verify minor category exists
            cat_response = supabase.supabase.table('website_categories').select('id').eq('id', category_assignment.minor_category_id).execute()
            if not cat_response.data:
                raise HTTPException(status_code=400, detail=f"Minor category {category_assignment.minor_category_id} not found")
            update_data['minor_category'] = category_assignment.minor_category_id
        
        # Update website with category assignments
        if update_data:
            update_response = supabase.supabase.table('websites').update(update_data).eq('id', website_id).execute()
            if not update_response.data:
                raise HTTPException(status_code=500, detail="Failed to update website categories")
                
            logger.info(f"Successfully updated categories for website {website_id}")
            return {
                "success": True, 
                "message": "Website categories updated successfully",
                "website_id": website_id,
                "updated_categories": update_data
            }
        else:
            return {"success": True, "message": "No category changes requested"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in update_website_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Bulk update website categories
@router.put("/websites/bulk-update-categories")
async def bulk_update_website_categories(category_update: WebsiteCategoryUpdate):
    """Update categories for multiple websites at once."""
    try:
        logger.info(f"Bulk updating categories for {len(category_update.website_ids)} websites")
        
        # Get Supabase client
        supabase = get_supabase_client()
        if not supabase:
            raise HTTPException(status_code=503, detail="Database connection not available")
            
        # Validate category IDs if provided
        update_data = {}
        if category_update.categories.main_category_id:
            cat_response = supabase.supabase.table('website_categories').select('id').eq('id', category_update.categories.main_category_id).execute()
            if not cat_response.data:
                raise HTTPException(status_code=400, detail=f"Main category {category_update.categories.main_category_id} not found")
            update_data['main_category'] = category_update.categories.main_category_id
            
        if category_update.categories.category_id:
            cat_response = supabase.supabase.table('website_categories').select('id').eq('id', category_update.categories.category_id).execute()
            if not cat_response.data:
                raise HTTPException(status_code=400, detail=f"Category {category_update.categories.category_id} not found")
            update_data['category_level2'] = category_update.categories.category_id
            
        if category_update.categories.sub_category_id:
            cat_response = supabase.supabase.table('website_categories').select('id').eq('id', category_update.categories.sub_category_id).execute()
            if not cat_response.data:
                raise HTTPException(status_code=400, detail=f"Sub-category {category_update.categories.sub_category_id} not found")
            update_data['sub_category'] = category_update.categories.sub_category_id
            
        if category_update.categories.minor_category_id:
            cat_response = supabase.supabase.table('website_categories').select('id').eq('id', category_update.categories.minor_category_id).execute()
            if not cat_response.data:
                raise HTTPException(status_code=400, detail=f"Minor category {category_update.categories.minor_category_id} not found")
            update_data['minor_category'] = category_update.categories.minor_category_id
        
        # Process each website
        results = []
        for website_id in category_update.website_ids:
            try:
                # Verify website exists
                website_response = supabase.supabase.table('websites').select('id').eq('id', website_id).execute()
                if not website_response.data:
                    results.append({"website_id": website_id, "status": "error", "message": "Website not found"})
                    continue
                
                # Update website categories
                if update_data:
                    update_response = supabase.supabase.table('websites').update(update_data).eq('id', website_id).execute()
                    if update_response.data:
                        results.append({"website_id": website_id, "status": "success", "updated_categories": update_data})
                    else:
                        results.append({"website_id": website_id, "status": "error", "message": "Update failed"})
                else:
                    results.append({"website_id": website_id, "status": "skipped", "message": "No category updates specified"})
            except Exception as e:
                results.append({"website_id": website_id, "status": "error", "message": str(e)})
        
        # Summarize results
        success_count = len([r for r in results if r["status"] == "success"])
        error_count = len([r for r in results if r["status"] == "error"])
        skipped_count = len([r for r in results if r["status"] == "skipped"])
        
        return {
            "success": True,
            "message": f"Updated categories for {success_count}/{len(category_update.website_ids)} websites",
            "statistics": {
                "total": len(category_update.website_ids),
                "succeeded": success_count,
                "failed": error_count,
                "skipped": skipped_count
            },
            "results": results
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in bulk_update_website_categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Create category tables
def create_category_tables(categories):
    # Add category validation
    if not categories:
        raise ValueError("Categories must be provided")
    # ... existing code ...
