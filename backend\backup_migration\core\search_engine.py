"""
RailGPT Search Engine - Unified search with priority system and caching
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from functools import lru_cache
import json
import hashlib
from datetime import datetime, timedelta

from utils.supabase_client import supabase
from core.vector_operations import generate_embedding, cosine_similarity
from core.visual_content_handler import detect_visual_query, process_visual_chunks

logger = logging.getLogger(__name__)

class RailGPTSearchEngine:
    """
    Unified search engine with priority system:
    1. Document chunks (highest priority)
    2. Website chunks (medium priority) 
    3. LLM fallback (lowest priority)
    """
    
    def __init__(self):
        self.cache = {}
        self.cache_ttl = timedelta(hours=1)
        
    def _get_cache_key(self, query_text: str, search_type: str) -> str:
        """Generate cache key for search results"""
        key_data = f"{query_text}_{search_type}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached result if not expired"""
        if cache_key in self.cache:
            result, timestamp = self.cache[cache_key]
            if datetime.now() - timestamp < self.cache_ttl:
                return result
            else:
                # Remove expired cache
                del self.cache[cache_key]
        return None
    
    def _cache_result(self, cache_key: str, result: Dict[str, Any]):
        """Cache search result"""
        self.cache[cache_key] = (result, datetime.now())
    
    async def search_documents(
        self, 
        query_text: str, 
        query_embedding: List[float],
        top_k: int = 10,
        min_threshold: float = 0.3,
        document_filter: Optional[List[str]] = None,
        visual_query_info: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search document chunks with enhanced similarity scoring
        """
        try:
            cache_key = self._get_cache_key(f"{query_text}_docs", "document")
            cached = self._get_cached_result(cache_key)
            if cached:
                logger.info("🔄 Using cached document search results")
                return cached.get('chunks', [])
            
            # Enhanced SQL query with better similarity calculation
            query_sql = """
            SELECT 
                id,
                filename,
                page,
                chunk_id,
                text,
                metadata,
                (1 - (embedding <=> %s::vector)) as similarity,
                CASE 
                    WHEN metadata->>'content_type' = 'text' THEN 1.0
                    WHEN metadata->>'content_type' = 'table' THEN 0.9
                    WHEN metadata->>'content_type' = 'image' THEN 0.8
                    ELSE 0.7
                END as content_boost
            FROM document_chunks
            WHERE (1 - (embedding <=> %s::vector)) > %s
            AND (%s IS NULL OR filename = ANY(%s))
            ORDER BY 
                (1 - (embedding <=> %s::vector)) * content_boost DESC,
                LENGTH(text) DESC
            LIMIT %s
            """
            
            params = [
                json.dumps(query_embedding),  # For similarity calculation 
                json.dumps(query_embedding),  # For WHERE clause
                min_threshold,
                document_filter is None,
                document_filter or [],
                json.dumps(query_embedding),  # For ORDER BY
                top_k
            ]
            
            result = await supabase.fetch(query_sql, params)
            
            # Process visual content if detected
            if visual_query_info and visual_query_info.get('has_visual_intent'):
                result = process_visual_chunks(result, visual_query_info)
            
            # Cache the result
            self._cache_result(cache_key, {'chunks': result})
            
            logger.info(f"📄 Found {len(result)} document chunks (threshold: {min_threshold})")
            return result
            
        except Exception as e:
            logger.error(f"❌ Document search error: {str(e)}")
            return []
    
    async def search_websites(
        self, 
        query_text: str, 
        query_embedding: List[float],
        top_k: int = 8,
        min_threshold: float = 0.2,
        website_filter: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        """
        Search website chunks (only called if no relevant document chunks found)
        """
        try:
            cache_key = self._get_cache_key(f"{query_text}_web", "website")
            cached = self._get_cached_result(cache_key)
            if cached:
                logger.info("🔄 Using cached website search results")
                return cached.get('chunks', [])
            
            query_sql = """
            SELECT 
                id,
                url,
                title,
                text,
                domain,
                (1 - (embedding <=> %s::vector)) as similarity,
                CASE 
                    WHEN domain ILIKE '%%irctc%%' OR domain ILIKE '%%indianrailways%%' THEN 1.2
                    WHEN domain ILIKE '%%railway%%' OR domain ILIKE '%%train%%' THEN 1.1
                    ELSE 1.0
                END as domain_boost
            FROM website_chunks
            WHERE (1 - (embedding <=> %s::vector)) > %s
            AND (%s IS NULL OR url = ANY(%s))
            ORDER BY 
                (1 - (embedding <=> %s::vector)) * domain_boost DESC,
                LENGTH(text) DESC
            LIMIT %s
            """
            
            params = [
                json.dumps(query_embedding),  # For similarity calculation
                json.dumps(query_embedding),  # For WHERE clause
                min_threshold,
                website_filter is None,
                website_filter or [],
                json.dumps(query_embedding),  # For ORDER BY
                top_k
            ]
            
            result = await supabase.fetch(query_sql, params)
            
            # Cache the result
            self._cache_result(cache_key, {'chunks': result})
            
            logger.info(f"🌐 Found {len(result)} website chunks (threshold: {min_threshold})")
            return result
            
        except Exception as e:
            logger.error(f"❌ Website search error: {str(e)}")
            return []
    
    async def unified_search(
        self, 
        query_text: str,
        model_id: str = "gemini-2.0-flash",
        extract_format: str = "paragraph",
        context_mode: str = "flexible"
    ) -> Dict[str, Any]:
        """
        Unified search with priority system:
        1. Try document search first (highest priority)
        2. If no relevant docs, try website search (medium priority)
        3. If still no results, use LLM fallback (lowest priority)
        """
        
        search_results = {
            "answer": "",
            "sources": [],
            "document_sources": [],
            "website_sources": [],
            "llm_fallback": False,
            "llm_model": model_id,
            "visual_content_found": False,
            "visual_content_types": []
        }
        
        try:
            # Detect visual query patterns
            visual_query_info = detect_visual_query(query_text)
            
            # Generate embedding for search
            query_embedding = generate_embedding(query_text, model_id)
            
            # STEP 1: Search documents first (HIGHEST PRIORITY)
            logger.info("🔍 STEP 1: Searching documents...")
            document_chunks = await self.search_documents(
                query_text, 
                query_embedding,
                top_k=10,
                min_threshold=0.3,
                visual_query_info=visual_query_info
            )
            
            # If we have good document results, use them
            if document_chunks and len(document_chunks) > 0:
                # Check if we have high-quality matches
                high_quality_docs = [chunk for chunk in document_chunks if chunk.get('similarity', 0) > 0.4]
                
                if high_quality_docs:
                    logger.info(f"✅ Found {len(high_quality_docs)} high-quality document chunks")
                    from core.answer_generator import generate_answer_from_chunks
                    
                    answer, sources, visual_found, visual_types = await generate_answer_from_chunks(
                        query_text, high_quality_docs, "document", model_id, extract_format
                    )
                    
                    search_results.update({
                        "answer": answer,
                        "document_sources": sources,
                        "sources": sources,
                        "llm_fallback": False,
                        "visual_content_found": visual_found,
                        "visual_content_types": visual_types
                    })
                    
                    # STEP 1.5: Also check websites for additional context
                    logger.info("🔍 STEP 1.5: Checking websites for additional context...")
                    website_chunks = await self.search_websites(
                        query_text, 
                        query_embedding,
                        top_k=5,
                        min_threshold=0.25
                    )
                    
                    if website_chunks:
                        high_quality_web = [chunk for chunk in website_chunks if chunk.get('similarity', 0) > 0.3]
                        if high_quality_web:
                            web_answer, web_sources, _, _ = await generate_answer_from_chunks(
                                query_text, high_quality_web, "website", model_id, extract_format
                            )
                            
                            if web_answer and web_answer.strip():
                                # Combine answers
                                search_results["answer"] += f"\n\n**Additional Web Information:**\n{web_answer}"
                                search_results["website_sources"] = web_sources
                                search_results["sources"].extend(web_sources)
                    
                    return search_results
            
            # STEP 2: No good document results, try websites (MEDIUM PRIORITY)
            logger.info("🔍 STEP 2: No relevant documents found, searching websites...")
            website_chunks = await self.search_websites(
                query_text, 
                query_embedding,
                top_k=8,
                min_threshold=0.2
            )
            
            if website_chunks:
                # Check for decent website matches
                decent_web_chunks = [chunk for chunk in website_chunks if chunk.get('similarity', 0) > 0.25]
                
                if decent_web_chunks:
                    logger.info(f"✅ Found {len(decent_web_chunks)} relevant website chunks")
                    from core.answer_generator import generate_answer_from_chunks
                    
                    answer, sources, visual_found, visual_types = await generate_answer_from_chunks(
                        query_text, decent_web_chunks, "website", model_id, extract_format
                    )
                    
                    search_results.update({
                        "answer": answer,
                        "website_sources": sources,
                        "sources": sources,
                        "llm_fallback": False,
                        "visual_content_found": visual_found,
                        "visual_content_types": visual_types
                    })
                    return search_results
            
            # STEP 3: No relevant chunks found, use LLM fallback (LOWEST PRIORITY)
            logger.info("🔍 STEP 3: No relevant chunks found, using LLM fallback...")
            
            if context_mode != "strict":  # Only use fallback if not in strict mode
                from core.llm_router import generate_llm_fallback_answer
                
                fallback_answer = await generate_llm_fallback_answer(
                    query_text, 
                    model_id=model_id,
                    extract_format=extract_format
                )
                
                search_results.update({
                    "answer": fallback_answer,
                    "sources": [],  # No sources for LLM fallback
                    "llm_fallback": True
                })
                
                logger.info("🤖 Generated LLM fallback answer")
            else:
                search_results["answer"] = "I couldn't find relevant information in the available documents and websites."
                logger.info("❌ No results found in strict mode")
            
            return search_results
            
        except Exception as e:
            logger.error(f"❌ Unified search error: {str(e)}")
            search_results.update({
                "answer": "I encountered an error while searching. Please try again.",
                "llm_fallback": True
            })
            return search_results

# Global search engine instance
search_engine = RailGPTSearchEngine() 