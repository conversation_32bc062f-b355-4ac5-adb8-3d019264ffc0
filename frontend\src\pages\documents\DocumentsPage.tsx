import React, { useState, useEffect } from 'react';
import { Document, DocumentExtractionDetails, ExtractionTool } from '../../types/documents';
import DocumentUploadForm from '../../components/documents/DocumentUploadForm';
import DocumentsTable from '../../components/documents/DocumentsTable';
import DocumentViewModal from '../../components/documents/DocumentViewModal';
import CategorySelectorModal from '../../components/ui/CategorySelectorModal';
import { 
  getDocuments, 
  reassignDocumentCategory, 
  bulkReassignDocumentCategories,
  CategoryReassignmentRequest,
  reprocessDocument
} from '../../services/api';

// Sample data for demonstration
const SAMPLE_DOCUMENTS: Document[] = [
  {
    id: '1',
    name: 'Railway Safety Guidelines 2025',
    uploadedAt: '2025-05-01T10:30:00Z',
    mainCategory: 'Safety',
    category: 'Guidelines',
    subCategory: 'General',
    uploadedBy: '<EMAIL>',
    qualityScore: 92,
    status: 'Extracted',
    filePath: '/documents/railway-safety-guidelines-2025.pdf',
    fileType: 'pdf',
    fileSize: 2457600,
  },
  {
    id: '2',
    name: 'Engine Maintenance Manual',
    uploadedAt: '2025-04-28T14:15:00Z',
    mainCategory: 'Technical',
    category: 'Manuals',
    subCategory: 'Diesel Loco',
    uploadedBy: '<EMAIL>',
    qualityScore: 87,
    status: 'Extracted',
    filePath: '/documents/engine-maintenance-manual.pdf',
    fileType: 'pdf',
    fileSize: 15728640,
  },
  {
    id: '3',
    name: 'Schedule of Train Operations - Q2 2025',
    uploadedAt: '2025-04-15T09:45:00Z',
    mainCategory: 'Operations',
    category: 'Schedules',
    uploadedBy: '<EMAIL>',
    qualityScore: 75,
    status: 'Extracted',
    filePath: '/documents/train-schedule-q2-2025.xlsx',
    fileType: 'xlsx',
    fileSize: 1048576,
  },
  {
    id: '4',
    name: 'Station Master Handbook',
    uploadedAt: '2025-03-20T11:00:00Z',
    mainCategory: 'Administrative',
    category: 'Handbooks',
    subCategory: 'Station Operations',
    minorCategory: 'Management',
    uploadedBy: '<EMAIL>',
    qualityScore: 95,
    status: 'Extracted',
    filePath: '/documents/station-master-handbook.docx',
    fileType: 'docx',
    fileSize: 5242880,
  },
  {
    id: '5',
    name: 'Railway Board Meeting Minutes - April 2025',
    uploadedAt: '2025-05-02T16:20:00Z',
    mainCategory: 'Administrative',
    category: 'Meeting Minutes',
    uploadedBy: '<EMAIL>',
    status: 'Pending',
    filePath: '/documents/railway-board-minutes-apr-2025.pdf',
    fileType: 'pdf',
    fileSize: 524288,
  },
];

// Sample extraction details for demonstration
const SAMPLE_EXTRACTION_DETAILS: DocumentExtractionDetails = {
  extractionMethod: 'PyMuPDF',
  qualityScore: 92,
  warnings: ['Some tables may not be properly extracted'],
  extractedContent: `
# Railway Safety Guidelines 2025

## 1. Introduction

This document outlines the comprehensive safety guidelines for all railway operations in 2025. These guidelines are mandated by the Railway Safety Commission and must be followed by all railway personnel.

## 2. General Safety Protocols

### 2.1 Personal Protective Equipment (PPE)

All railway staff working on or near the tracks must wear:
- High-visibility vest or clothing
- Safety helmet
- Safety boots with ankle support
- Hearing protection when working near operating machinery

### 2.2 Communication Protocols

Clear communication is essential for railway safety:
- Use standard radio communication protocols
- Confirm all instructions with a repeat-back
- Use established hand signals when radio communication is not possible
- Report any communication equipment failures immediately

## 3. Track Maintenance Safety

### 3.1 Track Inspection

Regular track inspections must be conducted:
- Visual inspections daily
- Ultrasonic testing monthly
- Comprehensive structural assessment quarterly

### 3.2 Work Zone Safety

For maintenance work on active tracks:
- Establish clear work zone boundaries
- Assign a dedicated lookout person
- Use track circuit operating devices where available
- Implement temporary speed restrictions on adjacent tracks

## 4. Train Operation Safety

### 4.1 Pre-departure Checks

Before any train departure, complete the following safety checks:
- Brake system functionality
- Signal system responsiveness
- Communication equipment testing
- Door operation verification

### 4.2 Speed Restrictions

Adhere to all speed restrictions, especially in:
- Curves and bends
- Bridges and tunnels
- Areas with ongoing maintenance
- Bad weather conditions

## 5. Emergency Procedures

### 5.1 Accident Response

In case of an accident:
- Immediately secure the site
- Notify central control
- Provide first aid as necessary
- Document all relevant details

### 5.2 Evacuation Protocols

Standard evacuation procedures for railway emergencies:
- Identify safe exit routes
- Guide passengers to designated assembly points
- Account for all passengers and staff
- Provide regular updates to emergency services

## 6. Compliance and Reporting

All safety incidents, near misses, and potential hazards must be reported through the official Railway Safety Management System within 24 hours of occurrence.

Regular safety audits will be conducted to ensure compliance with these guidelines.
  `,
  processingTime: 3450,
  chunks: 12,
};

const DocumentsPage: React.FC = () => {
  const [documents, setDocuments] = useState<Document[]>(SAMPLE_DOCUMENTS);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [extractionDetails, setExtractionDetails] = useState<DocumentExtractionDetails>(SAMPLE_EXTRACTION_DETAILS);
  const [categorySelectorModalOpen, setCategorySelectorModalOpen] = useState(false);
  const [documentForCategoryChange, setDocumentForCategoryChange] = useState<Document | null>(null);
  const [bulkCategoryChangeIds, setBulkCategoryChangeIds] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch documents from the backend
  useEffect(() => {
    let mounted = true;
    
    const loadDocuments = async () => {
      if (mounted) {
        setIsLoading(true);
        await fetchDocuments();
        if (mounted) {
          setIsLoading(false);
        }
      }
    };
    
    loadDocuments();

    // Set up document upload event listener
    const handleDocumentUploaded = (event: CustomEvent) => {
      const newDocument = event.detail;
      setDocuments(prev => [newDocument, ...prev]);
      // Refresh the documents list to get the latest from Supabase
      setTimeout(() => fetchDocuments(), 1000); // Small delay to allow backend processing
    };

    // Add event listener
    window.addEventListener('documentUploaded', handleDocumentUploaded as EventListener);

    // Cleanup
    return () => {
      mounted = false;
      window.removeEventListener('documentUploaded', handleDocumentUploaded as EventListener);
    };
  }, []);

  const fetchDocuments = async () => {
    try {
      const data = await getDocuments();
      if (data && data.length > 0) {
        // Use real data from Supabase, don't mix with sample data
        setDocuments(data);
        console.log(`Loaded ${data.length} documents from Supabase`);
      } else {
        console.log('No documents found from API, using sample data');
        // Only use sample data if no real data is available
        setDocuments(SAMPLE_DOCUMENTS);
      }
    } catch (error) {
      console.error('Error fetching documents:', error);
      // Keep using sample data if API call fails
      setDocuments(SAMPLE_DOCUMENTS);
    }
  };

  const handleViewDocument = (document: Document) => {
    setSelectedDocument(document);

    // Fetch actual extraction details from the backend
    async function fetchExtractionDetails() {
      try {
        // Import our API functions
        const { getDocumentExtractionDetails, getDocumentContent } = await import('../../services/api');

        // First try the specific extraction details endpoint
        try {
          const data = await getDocumentExtractionDetails(document.id);
          console.log('Extraction details response:', data);
          setExtractionDetails(data);
          return;
        } catch (extractionError) {
          console.warn('Failed to get extraction details, trying content endpoint:', extractionError);
        }

        // If that fails, try the general document content endpoint
        try {
          const contentData = await getDocumentContent(document.id);
          console.log('Document content response:', contentData);

          // Create extraction details from content data
          const details = {
            extractedContent: contentData.content || 'No content available',
            extractionMethod: contentData.extraction_method || 'Unknown',
            qualityScore: contentData.quality_score || 75,
            processingTime: contentData.processing_time || 1250,
            chunks: contentData.chunks_count || 5,
            warnings: [],
            fallbackReason: ''
          };

          setExtractionDetails(details);
          return;
        } catch (contentError) {
          console.error('Failed to get document content:', contentError);
          throw contentError; // Re-throw to be caught by the outer try-catch
        }
      } catch (error) {
        console.error('Error fetching extraction details:', error);

        // Fallback data in case of error
        const errorFallbackDetails = {
          extractedContent: `Unable to retrieve content for ${document.name} due to an error: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again later.`,
          extractionMethod: 'Error Fallback',
          qualityScore: 30,
          processingTime: 0,
          chunks: 0,
          warnings: ['Error retrieving content', error instanceof Error ? error.message : 'Unknown error'],
          fallbackReason: 'Error occurred while fetching content'
        };

        setExtractionDetails(errorFallbackDetails);
      }
    }

    fetchExtractionDetails();
    setIsViewModalOpen(true);
  };

  const handleEditDocument = (document: Document) => {
    // In a real app, you would implement an edit modal or navigate to an edit page
    alert(`Edit document: ${document.name}`);
  };

  const handleDeleteDocument = (document: Document) => {
    if (window.confirm(`Are you sure you want to delete "${document.name}"?`)) {
      // In a real app, you would make an API call to delete the document
      // async function deleteDocument() {
      //   try {
      //     await fetch(`/api/documents/${document.id}`, { method: 'DELETE' });
      //     setDocuments(documents.filter(doc => doc.id !== document.id));
      //   } catch (error) {
      //     console.error('Error deleting document:', error);
      //   }
      // }
      // deleteDocument();

      // For the demo, just filter it out
      setDocuments(documents.filter(doc => doc.id !== document.id));
    }
  };

  const handleReprocessDocument = async (document: Document) => {
    try {
      console.log(`Reprocessing document: ${document.name}`);
      
      // Show loading state if needed
      const result = await reprocessDocument(document.id, "auto");
      
      if (result.success) {
        // Refresh documents list to show updated status
        await fetchDocuments();
        alert(`Successfully reprocessed "${document.name}". ${result.chunks_extracted} chunks extracted.`);
      } else {
        alert(`Failed to reprocess "${document.name}": ${result.message}`);
      }
    } catch (error) {
      console.error('Error reprocessing document:', error);
      alert(`Error reprocessing "${document.name}": ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const handleReprocessWithTool = async (document: Document, tool: ExtractionTool) => {
    try {
      console.log(`Reprocessing ${document.name} with ${tool}`);
      
      // Map the tool name to the backend format
      const toolMapping: Record<ExtractionTool, string> = {
        'PyMuPDF': 'pymupdf',
        'PDFPlumber': 'pdfplumber', 
        'Tesseract OCR': 'tesseract',
        'Textract': 'textract',
        'DocX Parser': 'docx'
      };
      
      const backendTool = toolMapping[tool] || 'pymupdf';
      const result = await reprocessDocument(document.id, backendTool);
      
      if (result.success) {
        // Refresh documents list to show updated status
        await fetchDocuments();
        return true;
      } else {
        console.error(`Reprocessing failed: ${result.message}`);
        return false;
      }
    } catch (error) {
      console.error('Error reprocessing with tool:', error);
      return false;
    }
  };

  const handleCategoryUpdate = (updatedDocument: Document) => {
    // Update the document in the local state
    setDocuments(prevDocuments =>
      prevDocuments.map(doc =>
        doc.id === updatedDocument.id ? updatedDocument : doc
      )
    );
  };

  const handleChangeCategory = (document: Document) => {
    setDocumentForCategoryChange(document);
    setBulkCategoryChangeIds([]);
    setCategorySelectorModalOpen(true);
  };

  const handleBulkChangeCategory = (documentIds: string[]) => {
    setBulkCategoryChangeIds(documentIds);
    setDocumentForCategoryChange(null);
    setCategorySelectorModalOpen(true);
  };

  const handleCategorySave = async (categorySelection: CategoryReassignmentRequest) => {
    try {
      if (bulkCategoryChangeIds.length > 0) {
        // Bulk operation
        await bulkReassignDocumentCategories({
          item_ids: bulkCategoryChangeIds,
          ...categorySelection
        });
        
        // Refresh documents list
        await fetchDocuments();
        
        // Show success message
        alert(`Successfully updated categories for ${bulkCategoryChangeIds.length} documents`);
      } else if (documentForCategoryChange) {
        // Single document operation
        await reassignDocumentCategory(documentForCategoryChange.id, categorySelection);
        
        // Refresh documents list
        await fetchDocuments();
        
        // Show success message
        alert(`Successfully updated category for ${documentForCategoryChange.name}`);
      }
    } catch (error) {
      console.error('Error updating categories:', error);
      alert('Failed to update categories. Please try again.');
    }
  };

  if (isLoading) {
    return (
      <div className="h-full flex flex-col bg-gray-50 transition-colors duration-300">
        <div className="bg-white p-4 shadow-sm z-10 transition-colors duration-300">
          <div className="container mx-auto">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-bold text-gray-900">Document Management</h1>
            </div>
          </div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading documents...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-gray-50 transition-colors duration-300">
      {/* Fixed header section */}
      <div className="bg-white p-4 shadow-sm z-10 transition-colors duration-300">
        <div className="container mx-auto">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">Document Management</h1>

          </div>
        </div>
      </div>

      {/* Scrollable content section */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left side: Upload form */}
            <div className="lg:col-span-1">
              <DocumentUploadForm />
            </div>

            {/* Right side: Documents table */}
            <div className="lg:col-span-2">
              <DocumentsTable
                documents={documents}
                onView={handleViewDocument}
                onEdit={handleEditDocument}
                onDelete={handleDeleteDocument}
                onReprocess={handleReprocessDocument}
                onCategoryUpdate={handleCategoryUpdate}
                onChangeCategory={handleChangeCategory}
                onBulkChangeCategory={handleBulkChangeCategory}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Document View Modal */}
      {selectedDocument && (
        <DocumentViewModal
          document={selectedDocument}
          extractionDetails={extractionDetails}
          isOpen={isViewModalOpen}
          onClose={() => setIsViewModalOpen(false)}
          onReprocess={handleReprocessWithTool}
        />
      )}

      {/* Category Selector Modal */}
      <CategorySelectorModal
        isOpen={categorySelectorModalOpen}
        onClose={() => setCategorySelectorModalOpen(false)}
        onSave={handleCategorySave}
        entityType="document"
        entityName={documentForCategoryChange?.name || `${bulkCategoryChangeIds.length} documents`}
        currentCategories={documentForCategoryChange ? {
          mainCategory: documentForCategoryChange.mainCategory,
          category: documentForCategoryChange.category,
          subCategory: documentForCategoryChange.subCategory,
          minorCategory: documentForCategoryChange.minorCategory
        } : undefined}
        isBulkOperation={bulkCategoryChangeIds.length > 0}
        selectedCount={bulkCategoryChangeIds.length}
      />

    </div>
  );
};

export default DocumentsPage;
