-- Drop existing functions to recreate them
DROP FUNCTION IF EXISTS direct_search_document_chunks(vector, float, int);
DROP FUNCTION IF EXISTS direct_search_website_chunks(vector, float, int);

-- Create optimized document chunk search function
CREATE OR REPLACE FUNCTION direct_search_document_chunks(
    query_embedding vector(768),
    match_threshold float,
    match_count int
)
RETURNS TABLE (
    id UUID,
    document_id UUID,
    chunk_index INTEGER,
    page_number INTEGER,
    text TEXT,
    metadata JSONB,
    embedding vector(768),
    similarity float,
    title TEXT,
    source_type TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        dc.metadata,
        dc.embedding,
        (1 - (dc.embedding <=> query_embedding)) as similarity,
        d.display_name as title,
        'document'::TEXT as source_type
    FROM document_chunks dc
    JOIN documents d ON dc.document_id = d.id
    WHERE 
        dc.text IS NOT NULL 
        AND dc.text != ''
        AND dc.embedding IS NOT NULL
        AND (1 - (dc.embedding <=> query_embedding)) > match_threshold
    ORDER BY dc.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Create optimized website chunk search function
CREATE OR REPLACE FUNCTION direct_search_website_chunks(
    query_embedding vector(768),
    match_threshold float,
    match_count int
)
RETURNS TABLE (
    id UUID,
    website_id UUID,
    chunk_index INTEGER,
    text TEXT,
    metadata JSONB,
    embedding vector(768),
    similarity float,
    url TEXT,
    domain TEXT,
    title TEXT,
    source_type TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        wc.id,
        wc.website_id,
        wc.chunk_index,
        wc.text,
        wc.metadata,
        wc.embedding,
        (1 - (wc.embedding <=> query_embedding)) as similarity,
        w.url,
        w.domain,
        w.title,
        'website'::TEXT as source_type
    FROM website_chunks wc
    JOIN websites w ON wc.website_id = w.id
    WHERE 
        wc.text IS NOT NULL 
        AND wc.text != ''
        AND wc.embedding IS NOT NULL
        AND (1 - (wc.embedding <=> query_embedding)) > match_threshold
    ORDER BY wc.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Create a function to refresh embeddings for all chunks
CREATE OR REPLACE FUNCTION refresh_all_embeddings()
RETURNS void
LANGUAGE plpgsql
AS $$
DECLARE
    chunk_count integer;
BEGIN
    -- Count chunks missing embeddings
    SELECT COUNT(*) INTO chunk_count
    FROM (
        SELECT id FROM document_chunks WHERE embedding IS NULL
        UNION ALL
        SELECT id FROM website_chunks WHERE embedding IS NULL
    ) missing_embeddings;
    
    RAISE NOTICE 'Found % chunks missing embeddings', chunk_count;
    
    -- Add a notice to remind about embedding generation
    RAISE NOTICE 'Remember to run the embedding generation script after this!';
END;
$$;
