#!/usr/bin/env python3
"""
Test script to verify the source tracking fix is working correctly.
This will test that only chunks actually used in the LLM prompt are tracked as sources.
"""

import logging
import json
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_source_tracking():
    """Test the source tracking functionality with mock chunks."""
    
    # Test case 1: Document chunks with varying similarity scores
    print("\n" + "="*60)
    print("🧪 TESTING SOURCE TRACKING FIX")
    print("="*60)
    
    # Create mock chunks with different similarity scores
    mock_document_chunks = [
        {
            "document_id": "doc1",
            "filename": "ACP_Manual.pdf",
            "text": "This is highly relevant content about ACP system operations.",
            "page": 1,
            "similarity": 0.85,  # HIGH similarity - should be used
            "metadata": {"content_type": "text"}
        },
        {
            "document_id": "doc1", 
            "filename": "ACP_Manual.pdf",
            "text": "Additional relevant information about ACP troubleshooting.",
            "page": 2,
            "similarity": 0.78,  # HIGH similarity - should be used
            "metadata": {"content_type": "text"}
        },
        {
            "document_id": "doc1",
            "filename": "ACP_Manual.pdf", 
            "text": "More detailed ACP configuration settings.",
            "page": 3,
            "similarity": 0.72,  # MEDIUM-HIGH similarity - should be used
            "metadata": {"content_type": "text"}
        },
        {
            "document_id": "doc1",
            "filename": "ACP_Manual.pdf",
            "text": "Some moderately relevant content that mentions ACP.",
            "page": 4,
            "similarity": 0.45,  # MEDIUM similarity - might be used
            "metadata": {"content_type": "text"}
        },
        {
            "document_id": "doc1",
            "filename": "ACP_Manual.pdf",
            "text": "This content is barely related to railways.",
            "page": 5,
            "similarity": 0.25,  # LOW similarity - should NOT be used
            "metadata": {"content_type": "text"}
        },
        {
            "document_id": "doc1",
            "filename": "ACP_Manual.pdf",
            "text": "Completely irrelevant content about something else.",
            "page": 6,
            "similarity": 0.15,  # VERY LOW similarity - should NOT be used
            "metadata": {"content_type": "text"}
        }
    ]
    
    print(f"\n📊 Input: {len(mock_document_chunks)} total document chunks")
    print("   Similarities:", [f"{chunk['similarity']:.2f}" for chunk in mock_document_chunks])
    
    try:
        # Import the fixed function
        from server import generate_clean_answer_with_sources
        
        # Test with document chunks
        query = "How does the ACP system work?"
        answer, sources, visual_found, visual_types = generate_clean_answer_with_sources(
            query=query,
            chunks=mock_document_chunks,
            source_type="document",
            model_id="gemini-2.0-flash"
        )
        
        print(f"\n✅ RESULTS:")
        print(f"   📝 Answer generated: {'YES' if answer else 'NO'}")
        print(f"   📄 Sources tracked: {len(sources)}")
        print(f"   🎯 Visual content: {visual_found}")
        
        # Analyze which pages were included in sources
        if sources:
            for i, source in enumerate(sources, 1):
                pages = source.get('pages', [source.get('page')])
                display_text = source.get('display_text', 'Unknown')
                relevance = source.get('relevance_score', 0)
                print(f"   {i}. {display_text} (Relevance: {relevance:.3f})")
        
        # Check that only high-relevance chunks were used as sources
        all_source_pages = []
        for source in sources:
            pages = source.get('pages', [])
            all_source_pages.extend(pages)
        
        print(f"\n🔍 ANALYSIS:")
        print(f"   📄 Pages in sources: {sorted(set(all_source_pages))}")
        
        # Expected: Only pages 1, 2, 3 should be in sources (high similarity)
        expected_pages = {1, 2, 3}  # Only high-similarity chunks
        unexpected_pages = {4, 5, 6}  # Low-similarity chunks that shouldn't be included
        
        actual_pages = set(all_source_pages)
        
        # Test success criteria
        high_sim_included = expected_pages.issubset(actual_pages)
        low_sim_excluded = len(actual_pages.intersection(unexpected_pages)) == 0
        
        print(f"   ✅ High-similarity chunks included: {high_sim_included}")
        print(f"   ✅ Low-similarity chunks excluded: {low_sim_excluded}")
        
        if high_sim_included and low_sim_excluded:
            print(f"\n🎉 TEST PASSED: Source tracking is working correctly!")
            print(f"   • Only relevant chunks (pages {sorted(expected_pages)}) are tracked as sources")
            print(f"   • Irrelevant chunks (pages {sorted(unexpected_pages)}) are excluded")
        else:
            print(f"\n❌ TEST FAILED: Source tracking is not working correctly")
            print(f"   • Expected pages: {sorted(expected_pages)}")
            print(f"   • Actual pages: {sorted(actual_pages)}")
            
    except Exception as e:
        print(f"\n❌ ERROR: {str(e)}")
        logger.error(f"Test failed with error: {str(e)}")
        return False
    
    # Test case 2: Website chunks
    print(f"\n" + "-"*60)
    print("🌐 Testing Website Source Tracking")
    print("-"*60)
    
    mock_website_chunks = [
        {
            "url": "https://indianrailways.gov.in/acp-systems",
            "text": "Official information about ACP systems used in Indian Railways.",
            "title": "ACP Systems - Indian Railways",
            "similarity": 0.75,  # HIGH - should be used
        },
        {
            "url": "https://irctc.co.in/general-info", 
            "text": "General railway information not specifically about ACP.",
            "title": "General Railway Info",
            "similarity": 0.20,  # LOW - should NOT be used
        }
    ]
    
    try:
        answer, sources, visual_found, visual_types = generate_clean_answer_with_sources(
            query=query,
            chunks=mock_website_chunks,
            source_type="website",
            model_id="gemini-2.0-flash"
        )
        
        print(f"   📝 Website answer generated: {'YES' if answer else 'NO'}")
        print(f"   🌐 Website sources tracked: {len(sources)}")
        
        if sources:
            for i, source in enumerate(sources, 1):
                url = source.get('url', 'Unknown')
                relevance = source.get('relevance_score', 0)
                print(f"   {i}. {url} (Relevance: {relevance:.3f})")
        
        # Should only include the high-relevance website
        if len(sources) == 1 and sources[0].get('url') == 'https://indianrailways.gov.in/acp-systems':
            print(f"   ✅ Website source tracking working correctly!")
        else:
            print(f"   ❌ Website source tracking not working correctly")
            
    except Exception as e:
        print(f"   ❌ Website test error: {str(e)}")
    
    print(f"\n" + "="*60)
    print("🏁 SOURCE TRACKING TEST COMPLETE")
    print("="*60)

if __name__ == "__main__":
    test_source_tracking() 