#!/usr/bin/env node

// This script validates and configures Supabase for the RailGPT application
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Read environment variables from .env file
const envPath = path.resolve(process.cwd(), '.env');
let supabaseUrl = '';
let supabaseKey = '';

console.log('\n🚄 RailGPT Supabase Setup Utility');
console.log('=====================================\n');

// Load environment variables
const loadEnvVars = () => {
  try {
    if (fs.existsSync(envPath)) {
      const envFile = fs.readFileSync(envPath, 'utf8');
      const envVars = envFile.split('\n');

      for (const line of envVars) {
        if (line.startsWith('SUPABASE_URL=')) {
          supabaseUrl = line.split('=')[1].trim();
        } else if (line.startsWith('SUPABASE_KEY=')) {
          supabaseKey = line.split('=')[1].trim();
        } else if (line.startsWith('REACT_APP_SUPABASE_URL=')) {
          supabaseUrl = supabaseUrl || line.split('=')[1].trim();
        } else if (line.startsWith('REACT_APP_SUPABASE_ANON_KEY=')) {
          supabaseKey = supabaseKey || line.split('=')[1].trim();
        }
      }
    }
  } catch (error) {
    console.error('Error reading .env file:', error.message);
  }
};

// Prompt for Supabase credentials if not found
const promptForCredentials = () => {
  return new Promise((resolve) => {
    if (supabaseUrl && supabaseKey) {
      console.log('✅ Found Supabase credentials in .env file');
      return resolve();
    }

    console.log('⚠️ Supabase credentials not found in .env file\n');

    const getUrl = () => {
      rl.question('Enter your Supabase URL: ', (url) => {
        if (!url) {
          console.log('❌ Supabase URL is required');
          return getUrl();
        }
        supabaseUrl = url.trim();
        getKey();
      });
    };

    const getKey = () => {
      rl.question('Enter your Supabase Service Key: ', (key) => {
        if (!key) {
          console.log('❌ Supabase Service Key is required');
          return getKey();
        }
        supabaseKey = key.trim();
        resolve();
      });
    };

    getUrl();
  });
};

// Test connection to Supabase
const testConnection = async () => {
  console.log('\n🔄 Testing connection to Supabase...');

  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    const { data, error } = await supabase.from('documents').select('id').limit(1);

    if (error) {
      throw new Error(error.message);
    }

    console.log('✅ Successfully connected to Supabase');
    return true;
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    return false;
  }
};

// Check if required tables exist
const checkTables = async () => {
  console.log('\n🔄 Checking database tables...');

  const requiredTables = [
    'documents',
    'document_chunks',
    'websites',
    'website_chunks',
    'queries',
    'chat_sessions'
  ];

  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    let allTablesExist = true;
    const missingTables = [];

    for (const table of requiredTables) {
      const { data, error } = await supabase.from(table).select('id').limit(1);

      if (error && error.code === '42P01') { // Table doesn't exist
        allTablesExist = false;
        missingTables.push(table);
      }
    }

    if (allTablesExist) {
      console.log('✅ All required tables exist');
      return true;
    } else {
      console.log('❌ Missing tables:', missingTables.join(', '));
      return false;
    }
  } catch (error) {
    console.error('❌ Error checking tables:', error.message);
    return false;
  }
};

// Create missing tables
const createTables = async () => {
  console.log('\n🔄 Setting up missing tables...');

  try {
    // Read SQL from file
    const sqlPath = path.resolve(process.cwd(), 'supabase_tables.sql');
    if (!fs.existsSync(sqlPath)) {
      console.error('❌ SQL file not found: supabase_tables.sql');
      return false;
    }

    const sql = fs.readFileSync(sqlPath, 'utf8');
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Execute SQL statements
    const { error } = await supabase.rpc('exec_sql', { sql });

    if (error) {
      console.error('❌ Error creating tables:', error.message);
      console.log('\n⚠️ You may need to create tables manually using the SQL in supabase_tables.sql');
      return false;
    }

    console.log('✅ Successfully created database tables');
    return true;
  } catch (error) {
    console.error('❌ Error creating tables:', error.message);
    return false;
  }
};

// Save environment variables
const saveEnvVars = () => {
  try {
    let envContent = '';

    if (fs.existsSync(envPath)) {
      envContent = fs.readFileSync(envPath, 'utf8');

      // Update existing variables
      let lines = envContent.split('\n');
      let supabaseUrlFound = false;
      let supabaseKeyFound = false;

      lines = lines.map(line => {
        if (line.startsWith('SUPABASE_URL=')) {
          supabaseUrlFound = true;
          return `SUPABASE_URL=${supabaseUrl}`;
        } else if (line.startsWith('SUPABASE_KEY=')) {
          supabaseKeyFound = true;
          return `SUPABASE_KEY=${supabaseKey}`;
        } else if (line.startsWith('REACT_APP_SUPABASE_URL=')) {
          return `REACT_APP_SUPABASE_URL=${supabaseUrl}`;
        } else if (line.startsWith('REACT_APP_SUPABASE_ANON_KEY=')) {
          return `REACT_APP_SUPABASE_ANON_KEY=${supabaseKey}`;
        }
        return line;
      });

      // Add variables if not found
      if (!supabaseUrlFound) {
        lines.push(`SUPABASE_URL=${supabaseUrl}`);
      }
      if (!supabaseKeyFound) {
        lines.push(`SUPABASE_KEY=${supabaseKey}`);
      }

      envContent = lines.join('\n');
    } else {
      // Create new .env file
      envContent = `SUPABASE_URL=${supabaseUrl}\nSUPABASE_KEY=${supabaseKey}\nREACT_APP_SUPABASE_URL=${supabaseUrl}\nREACT_APP_SUPABASE_ANON_KEY=${supabaseKey}\n`;
    }

    fs.writeFileSync(envPath, envContent);
    console.log('✅ Updated .env file with Supabase credentials');
    return true;
  } catch (error) {
    console.error('❌ Error saving environment variables:', error.message);
    return false;
  }
};

// Main function
const main = async () => {
  try {
    loadEnvVars();
    await promptForCredentials();

    const connected = await testConnection();
    if (!connected) {
      console.log('\n❌ Unable to connect to Supabase. Please check your credentials and try again.');
      rl.close();
      return;
    }

    const tablesExist = await checkTables();
    if (!tablesExist) {
      console.log('\n⚠️ Some required tables are missing');

      rl.question('Would you like to create the missing tables? (y/n): ', async (answer) => {
        if (answer.toLowerCase() === 'y') {
          await createTables();
        } else {
          console.log('\n⚠️ Tables not created. The application may not function properly.');
        }

        saveEnvVars();
        console.log('\n🚄 RailGPT Supabase setup complete!');
        rl.close();
      });
    } else {
      saveEnvVars();
      console.log('\n🚄 RailGPT Supabase setup complete!');
      rl.close();
    }
  } catch (error) {
    console.error('\n❌ An error occurred during setup:', error.message);
    rl.close();
  }
};

// Run the script
main();
