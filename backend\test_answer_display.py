#!/usr/bin/env python3
"""Test Answer Display Logic"""

import requests
import json

def test_answer_display():
    """Test the answer display logic according to the specified rules."""
    print("🧪 Testing Answer Display Logic")
    print("=" * 50)
    
    # Test query
    response = requests.post('http://localhost:8000/api/query', json={
        'query': 'What is a locomotive?',
        'model': 'gemini-2.0-flash'
    })
    
    if response.status_code != 200:
        print(f"❌ API Error: {response.status_code}")
        print(response.text)
        return
    
    data = response.json()
    print("✅ Query successful!")
    print(f"Response keys: {list(data.keys())}")
    
    # Check answer display logic components
    print("\n📊 Answer Display Logic Check:")
    doc_answer = data.get('document_answer') or ''
    web_answer = data.get('website_answer') or ''
    has_doc_answer = bool(doc_answer.strip())
    has_web_answer = bool(web_answer.strip())
    has_doc_sources = len(data.get('document_sources', [])) > 0
    has_web_sources = len(data.get('website_sources', [])) > 0
    is_llm_fallback = data.get('llm_fallback', False)
    has_visual_content = data.get('visual_content_found', False)
    
    print(f"Document answer: {'✅' if has_doc_answer else '❌'}")
    print(f"Website answer: {'✅' if has_web_answer else '❌'}")
    print(f"Document sources: {len(data.get('document_sources', []))} items")
    print(f"Website sources: {len(data.get('website_sources', []))} items")
    print(f"LLM fallback: {'✅' if is_llm_fallback else '❌'}")
    print(f"Visual content: {'✅' if has_visual_content else '❌'}")
    
    # Determine display scenario according to rules
    print("\n🎯 Display Scenario Analysis:")
    
    if has_doc_answer and not has_web_answer:
        print("📄 DOCUMENT ONLY SCENARIO")
        print("Should display: 📄 Answer Found in [Document Name] card")
        print("Features:")
        print("  • Embedded tables, images, charts, diagrams support")
        print("  • Render in original format using HTML")
        print("  • Source: DocumentName.pdf - Page X, Page Y")
        print("  • Click opens: /viewer?doc=DocumentName.pdf&page=X")
        if has_doc_sources:
            print(f"  • Available sources: {len(data.get('document_sources', []))}")
    
    elif has_web_answer and not has_doc_answer:
        print("🌐 WEBSITE ONLY SCENARIO")
        print("Should display: 🌐 Answer Found in Extracted Website(s) card")
        print("Features:")
        print("  • Show max 3 contributing sources")
        print("  • Source: https://example.com/page")
        print("  • Click opens /website-preview?url=... in new tab")
        print("  • Hide 'Uploaded Document' source")
        if has_web_sources:
            print(f"  • Available sources: {len(data.get('website_sources', []))}")
    
    elif has_doc_answer and has_web_answer:
        print("📄 + 🌐 BOTH SOURCES SCENARIO")
        print("Should display: Two separate cards")
        print("  1. 📄 From documents (includes page sources)")
        print("  2. 🌐 From websites (with clickable URLs)")
        print("  • Show each source list separately")
        print("  • 3 items max per card, + more dropdown")
        print(f"  • Document sources: {len(data.get('document_sources', []))}")
        print(f"  • Website sources: {len(data.get('website_sources', []))}")
    
    elif is_llm_fallback:
        print("🤖 LLM FALLBACK ONLY SCENARIO")
        print("Should display: Answer Generated by [Selected Model]")
        print("Features:")
        print("  • Do NOT show any Answer Source section")
        print("  • Add ⚠️ No match in uploaded documents or extracted websites")
        print(f"  • Model used: {data.get('llm_model', 'Unknown')}")
    
    else:
        print("❓ UNKNOWN SCENARIO")
        print("This doesn't match any expected display rules!")
        print("Need to investigate the response structure.")
    
    # Visual content check
    if has_visual_content:
        print(f"\n🖼️ Visual Content Found:")
        print(f"  • Types: {data.get('visual_content_types', [])}")
        print("  • Should render: <img>, <table>, <canvas> if chart")
        print("  • Maintain original format from PDF extraction")
    
    # Show answer preview
    answer_preview = data.get('answer', '')[:200]
    print(f"\n📝 Answer Preview:")
    print(f"  {answer_preview}{'...' if len(data.get('answer', '')) > 200 else ''}")
    
    print("\n✅ Answer Display Logic Test Complete!")
    return data

if __name__ == "__main__":
    test_answer_display() 