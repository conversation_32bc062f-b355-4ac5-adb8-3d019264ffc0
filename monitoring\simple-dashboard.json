{"displayName": "RailGPT Backend Monitoring", "mosaicLayout": {"columns": 12, "tiles": [{"width": 6, "height": 4, "widget": {"title": "Cloud Run Request Count", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"railgpt-backend\" AND metric.type=\"run.googleapis.com/request_count\"", "aggregation": {"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM"}}}, "plotType": "LINE"}], "yAxis": {"label": "Requests/sec", "scale": "LINEAR"}}}}, {"width": 6, "height": 4, "xPos": 6, "widget": {"title": "Instance Count", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"railgpt-backend\" AND metric.type=\"run.googleapis.com/container/instance_count\"", "aggregation": {"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_SUM"}}}, "plotType": "STACKED_AREA"}], "yAxis": {"label": "Instances", "scale": "LINEAR"}}}}]}}