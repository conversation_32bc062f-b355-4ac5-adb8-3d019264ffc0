#!/usr/bin/env python3
"""
Populate Standard Categories for RailGPT
Creates a comprehensive four-level category structure for both documents and websites
"""

import asyncio
import logging
from enhanced_category_management import (
    create_category_in_db, 
    get_supabase_client,
    CategoryCreate
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Standard category structure for documents
DOCUMENT_CATEGORIES = [
    # Level 1: Main Categories
    {
        "name": "Safety & Security",
        "type": "main_category",
        "description": "Safety protocols, security procedures, and emergency response documentation",
        "sort_order": 1,
        "children": [
            # Level 2: Categories
            {
                "name": "Emergency Procedures",
                "type": "category",
                "description": "Emergency response and crisis management procedures",
                "sort_order": 1,
                "children": [
                    # Level 3: Sub Categories
                    {
                        "name": "Fire Safety",
                        "type": "sub_category",
                        "description": "Fire prevention, detection, and suppression procedures",
                        "sort_order": 1,
                        "children": [
                            # Level 4: Minor Categories
                            {"name": "Detection Systems", "type": "minor_category", "description": "Fire detection equipment and procedures", "sort_order": 1},
                            {"name": "Suppression Systems", "type": "minor_category", "description": "Fire suppression equipment and procedures", "sort_order": 2},
                            {"name": "Evacuation Procedures", "type": "minor_category", "description": "Fire evacuation protocols and routes", "sort_order": 3}
                        ]
                    },
                    {
                        "name": "Medical Emergency",
                        "type": "sub_category",
                        "description": "Medical emergency response and first aid procedures",
                        "sort_order": 2,
                        "children": [
                            {"name": "First Aid", "type": "minor_category", "description": "Basic first aid procedures and protocols", "sort_order": 1},
                            {"name": "Medical Equipment", "type": "minor_category", "description": "Medical equipment usage and maintenance", "sort_order": 2},
                            {"name": "Emergency Contacts", "type": "minor_category", "description": "Emergency medical contact information", "sort_order": 3}
                        ]
                    }
                ]
            },
            {
                "name": "Security Protocols",
                "type": "category",
                "description": "Security procedures and access control documentation",
                "sort_order": 2,
                "children": [
                    {
                        "name": "Access Control",
                        "type": "sub_category",
                        "description": "Access control systems and procedures",
                        "sort_order": 1,
                        "children": [
                            {"name": "ID Cards", "type": "minor_category", "description": "ID card systems and procedures", "sort_order": 1},
                            {"name": "Biometric Systems", "type": "minor_category", "description": "Biometric access control systems", "sort_order": 2}
                        ]
                    }
                ]
            }
        ]
    },
    {
        "name": "Technical Documentation",
        "type": "main_category",
        "description": "Technical specifications, system documentation, and engineering materials",
        "sort_order": 2,
        "children": [
            {
                "name": "System Specifications",
                "type": "category",
                "description": "Technical system specifications and requirements",
                "sort_order": 1,
                "children": [
                    {
                        "name": "Hardware",
                        "type": "sub_category",
                        "description": "Hardware specifications and requirements",
                        "sort_order": 1,
                        "children": [
                            {"name": "Servers", "type": "minor_category", "description": "Server hardware specifications", "sort_order": 1},
                            {"name": "Network Equipment", "type": "minor_category", "description": "Network hardware specifications", "sort_order": 2},
                            {"name": "Railway Equipment", "type": "minor_category", "description": "Railway-specific hardware", "sort_order": 3}
                        ]
                    },
                    {
                        "name": "Software",
                        "type": "sub_category",
                        "description": "Software specifications and requirements",
                        "sort_order": 2,
                        "children": [
                            {"name": "Operating Systems", "type": "minor_category", "description": "OS specifications and configurations", "sort_order": 1},
                            {"name": "Applications", "type": "minor_category", "description": "Application software specifications", "sort_order": 2}
                        ]
                    }
                ]
            },
            {
                "name": "Maintenance",
                "type": "category",
                "description": "Maintenance procedures and schedules",
                "sort_order": 2,
                "children": [
                    {
                        "name": "Preventive Maintenance",
                        "type": "sub_category",
                        "description": "Scheduled preventive maintenance procedures",
                        "sort_order": 1,
                        "children": [
                            {"name": "Daily Checks", "type": "minor_category", "description": "Daily maintenance check procedures", "sort_order": 1},
                            {"name": "Weekly Maintenance", "type": "minor_category", "description": "Weekly maintenance procedures", "sort_order": 2}
                        ]
                    }
                ]
            }
        ]
    },
    {
        "name": "Operations",
        "type": "main_category",
        "description": "Operational procedures, workflows, and standard operating procedures",
        "sort_order": 3,
        "children": [
            {
                "name": "Standard Operating Procedures",
                "type": "category",
                "description": "Standard operating procedures and workflows",
                "sort_order": 1,
                "children": [
                    {
                        "name": "Daily Operations",
                        "type": "sub_category",
                        "description": "Daily operational procedures",
                        "sort_order": 1,
                        "children": [
                            {"name": "Shift Procedures", "type": "minor_category", "description": "Shift change and handover procedures", "sort_order": 1},
                            {"name": "Reporting", "type": "minor_category", "description": "Daily reporting procedures", "sort_order": 2}
                        ]
                    }
                ]
            }
        ]
    },
    {
        "name": "Administrative",
        "type": "main_category",
        "description": "Administrative documents, policies, and regulatory compliance",
        "sort_order": 4,
        "children": [
            {
                "name": "Policies & Procedures",
                "type": "category",
                "description": "Company policies and administrative procedures",
                "sort_order": 1,
                "children": [
                    {
                        "name": "HR Policies",
                        "type": "sub_category",
                        "description": "Human resources policies and procedures",
                        "sort_order": 1,
                        "children": [
                            {"name": "Employee Handbook", "type": "minor_category", "description": "Employee handbook and guidelines", "sort_order": 1},
                            {"name": "Training Procedures", "type": "minor_category", "description": "Employee training procedures", "sort_order": 2}
                        ]
                    }
                ]
            }
        ]
    }
]

# Standard category structure for websites (similar structure but website-focused)
WEBSITE_CATEGORIES = [
    {
        "name": "Government & Regulatory",
        "type": "main_category",
        "description": "Government websites and regulatory authority information",
        "sort_order": 1,
        "children": [
            {
                "name": "Railway Authorities",
                "type": "category",
                "description": "Railway regulatory and administrative authorities",
                "sort_order": 1,
                "children": [
                    {
                        "name": "National Railways",
                        "type": "sub_category",
                        "description": "National railway authority websites",
                        "sort_order": 1,
                        "children": [
                            {"name": "Indian Railways", "type": "minor_category", "description": "Indian Railways official websites", "sort_order": 1},
                            {"name": "Regional Railways", "type": "minor_category", "description": "Regional railway authority websites", "sort_order": 2}
                        ]
                    }
                ]
            }
        ]
    },
    {
        "name": "Technical Resources",
        "type": "main_category",
        "description": "Technical documentation and resource websites",
        "sort_order": 2,
        "children": [
            {
                "name": "Standards & Specifications",
                "type": "category",
                "description": "Technical standards and specification websites",
                "sort_order": 1,
                "children": [
                    {
                        "name": "Railway Standards",
                        "type": "sub_category",
                        "description": "Railway industry standards and specifications",
                        "sort_order": 1,
                        "children": [
                            {"name": "Safety Standards", "type": "minor_category", "description": "Railway safety standards", "sort_order": 1},
                            {"name": "Technical Standards", "type": "minor_category", "description": "Railway technical standards", "sort_order": 2}
                        ]
                    }
                ]
            }
        ]
    },
    {
        "name": "Industry Resources",
        "type": "main_category",
        "description": "Industry associations, news, and professional resources",
        "sort_order": 3,
        "children": [
            {
                "name": "Professional Organizations",
                "type": "category",
                "description": "Railway industry professional organizations",
                "sort_order": 1,
                "children": [
                    {
                        "name": "Industry Associations",
                        "type": "sub_category",
                        "description": "Railway industry associations and groups",
                        "sort_order": 1,
                        "children": [
                            {"name": "International Associations", "type": "minor_category", "description": "International railway associations", "sort_order": 1},
                            {"name": "National Associations", "type": "minor_category", "description": "National railway associations", "sort_order": 2}
                        ]
                    }
                ]
            }
        ]
    },
    {
        "name": "Commercial & Vendors",
        "type": "main_category",
        "description": "Commercial websites, vendors, and service providers",
        "sort_order": 4,
        "children": [
            {
                "name": "Equipment Suppliers",
                "type": "category",
                "description": "Railway equipment and component suppliers",
                "sort_order": 1,
                "children": [
                    {
                        "name": "Rolling Stock",
                        "type": "sub_category",
                        "description": "Rolling stock manufacturers and suppliers",
                        "sort_order": 1,
                        "children": [
                            {"name": "Locomotive Manufacturers", "type": "minor_category", "description": "Locomotive manufacturing companies", "sort_order": 1},
                            {"name": "Coach Manufacturers", "type": "minor_category", "description": "Railway coach manufacturing companies", "sort_order": 2}
                        ]
                    }
                ]
            }
        ]
    }
]

async def create_category_hierarchy(categories, table_name, parent_id=None):
    """Recursively create category hierarchy"""
    created_categories = []
    
    for category_data in categories:
        # Extract children before creating the category
        children = category_data.pop('children', [])
        
        # Set parent_id
        category_data['parent_id'] = parent_id
        
        try:
            # Create the category
            logger.info(f"Creating {category_data['type']}: {category_data['name']}")
            created_category = await create_category_in_db(category_data, table_name)
            created_categories.append(created_category)
            
            # Create children if any
            if children:
                child_categories = await create_category_hierarchy(children, table_name, created_category['id'])
                created_categories.extend(child_categories)
                
        except Exception as e:
            logger.error(f"Error creating category {category_data['name']}: {str(e)}")
            continue
    
    return created_categories

async def populate_categories():
    """Populate both document and website categories"""
    try:
        logger.info("🚀 Starting category population process")
        
        # Test database connection
        supabase = get_supabase_client()
        if not supabase:
            logger.error("❌ Cannot connect to database")
            return False
        
        logger.info("✅ Database connection successful")
        
        # Populate document categories
        logger.info("📄 Creating document categories...")
        doc_categories = await create_category_hierarchy(DOCUMENT_CATEGORIES.copy(), 'document_categories')
        logger.info(f"✅ Created {len(doc_categories)} document categories")
        
        # Populate website categories
        logger.info("🌐 Creating website categories...")
        web_categories = await create_category_hierarchy(WEBSITE_CATEGORIES.copy(), 'website_categories')
        logger.info(f"✅ Created {len(web_categories)} website categories")
        
        logger.info("🎉 Category population completed successfully!")
        logger.info(f"📊 Total categories created: {len(doc_categories) + len(web_categories)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error populating categories: {str(e)}")
        return False

if __name__ == "__main__":
    asyncio.run(populate_categories())
