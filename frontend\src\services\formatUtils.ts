/**
 * Utility functions for formatting text and display data
 */

/**
 * Truncate a string to a specified length, adding ellipsis if truncated
 * @param text The text to truncate
 * @param maxLength Maximum allowed length before truncation
 * @returns Truncated text with ellipsis if needed
 */
export const truncateText = (text: string, maxLength: number = 100): string => {
  if (!text || text.length <= maxLength) return text;
  return `${text.substring(0, maxLength)}...`;
};

/**
 * Format a page range for display
 * @param pages Array of page numbers
 * @returns Formatted page range string
 */
export const formatPageRange = (pages: number[] = []): string => {
  if (!pages || !Array.isArray(pages) || pages.length === 0) return '';

  // Sort the pages to ensure they're in order
  const sortedPages = [...pages].sort((a, b) => a - b);

  // Check if there are only a few pages - display them individually
  if (sortedPages.length <= 3) {
    return `Pages ${sortedPages.join(', ')}`;
  }

  // For many pages, use a range format
  return `Pages ${sortedPages[0]}-${sortedPages[sortedPages.length - 1]}`;
};

/**
 * Format a date string or timestamp to a user-friendly format
 * @param dateString ISO date string, timestamp, or Date object
 * @returns Formatted date string
 */
export const formatDate = (dateString: string | number | Date): string => {
  if (!dateString) return '';

  try {
    const date = typeof dateString === 'object' ? dateString : new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (e) {
    console.error('Error formatting date:', e);
    return String(dateString);
  }
};

/**
 * Format a file size in bytes to a human-readable format
 * @param bytes Size in bytes
 * @returns Formatted file size string (e.g., "2.5 MB")
 */
export const formatFileSize = (bytes: number): string => {
  if (!bytes || isNaN(bytes) || bytes < 0) return '0 B';

  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`;
};
