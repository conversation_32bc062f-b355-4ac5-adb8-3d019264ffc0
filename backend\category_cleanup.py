# Category Management Cleanup and Optimization Script
# This script cleans up duplicate categories and optimizes the category system

import logging
import asyncio
from typing import List, Dict, Any
from datetime import datetime
import uuid

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_supabase_client():
    """Get Supabase client with proper error handling."""
    try:
        from supabase_client import supabase
        if not supabase or not hasattr(supabase, 'supabase') or supabase.supabase is None:
            logger.error("Supabase client not available")
            return None
        return supabase
    except Exception as e:
        logger.error(f"Error getting Supabase client: {str(e)}")
        return None

async def cleanup_duplicate_categories():
    """Remove duplicate categories keeping the oldest entry for each unique name."""
    try:
        supabase = get_supabase_client()
        if not supabase:
            logger.error("Cannot proceed without Supabase client")
            return False

        # Get all document categories
        logger.info("Fetching all document categories...")
        response = supabase.supabase.table('document_categories').select('*').execute()
        
        if not response.data:
            logger.info("No document categories found")
            return True

        categories = response.data
        logger.info(f"Found {len(categories)} document categories")

        # Group categories by name and type
        category_groups = {}
        for cat in categories:
            key = f"{cat['name']}_{cat['type']}_{cat.get('parent_id', 'null')}"
            if key not in category_groups:
                category_groups[key] = []
            category_groups[key].append(cat)

        # Find duplicates and keep the oldest
        duplicates_to_delete = []
        for key, group in category_groups.items():
            if len(group) > 1:
                # Sort by created_at to keep the oldest
                group.sort(key=lambda x: x['created_at'])
                oldest = group[0]
                duplicates = group[1:]
                
                logger.info(f"Found {len(duplicates)} duplicates for '{oldest['name']}' (keeping {oldest['id']})")
                duplicates_to_delete.extend([dup['id'] for dup in duplicates])

        # Delete duplicates
        if duplicates_to_delete:
            logger.info(f"Deleting {len(duplicates_to_delete)} duplicate categories...")
            for cat_id in duplicates_to_delete:
                try:
                    delete_response = supabase.supabase.table('document_categories').delete().eq('id', cat_id).execute()
                    logger.info(f"Deleted duplicate category: {cat_id}")
                except Exception as e:
                    logger.error(f"Error deleting category {cat_id}: {str(e)}")
        else:
            logger.info("No duplicates found to delete")

        return True

    except Exception as e:
        logger.error(f"Error in cleanup_duplicate_categories: {str(e)}")
        return False

async def cleanup_test_categories():
    """Remove test categories created during development."""
    try:
        supabase = get_supabase_client()
        if not supabase:
            return False

        # Define test category patterns
        test_patterns = [
            'Test Category',
            'Final Test Category',
            'New Test Category',
            'New Website Test Category',
            'ABCD',
            'ABC'
        ]

        logger.info("Removing test categories...")
        
        for pattern in test_patterns:
            try:
                # Delete categories that start with test patterns
                response = supabase.supabase.table('document_categories').delete().like('name', f'{pattern}%').execute()
                if response.data:
                    logger.info(f"Deleted {len(response.data)} categories matching pattern '{pattern}'")
            except Exception as e:
                logger.error(f"Error deleting test categories with pattern '{pattern}': {str(e)}")

        # Also clean up website test categories
        try:
            response = supabase.supabase.table('website_categories').delete().like('name', 'Test Website Category%').execute()
            if response.data:
                logger.info(f"Deleted {len(response.data)} test website categories")
        except Exception as e:
            logger.error(f"Error deleting test website categories: {str(e)}")

        return True

    except Exception as e:
        logger.error(f"Error in cleanup_test_categories: {str(e)}")
        return False

async def create_standard_categories():
    """Create a clean set of standard categories."""
    try:
        supabase = get_supabase_client()
        if not supabase:
            return False

        # Standard document categories
        standard_doc_categories = [
            {
                "id": str(uuid.uuid4()),
                "name": "Safety & Security",
                "type": "main_category",
                "parent_id": None,
                "description": "Safety protocols, security measures, and emergency procedures",
                "sort_order": 1,
                "is_active": True,
                "created_at": datetime.utcnow().isoformat() + "Z",
                "updated_at": datetime.utcnow().isoformat() + "Z"
            },
            {
                "id": str(uuid.uuid4()),
                "name": "Technical Documentation",
                "type": "main_category",
                "parent_id": None,
                "description": "Technical manuals, specifications, and engineering documents",
                "sort_order": 2,
                "is_active": True,
                "created_at": datetime.utcnow().isoformat() + "Z",
                "updated_at": datetime.utcnow().isoformat() + "Z"
            },
            {
                "id": str(uuid.uuid4()),
                "name": "Operations",
                "type": "main_category",
                "parent_id": None,
                "description": "Operational procedures, schedules, and workflows",
                "sort_order": 3,
                "is_active": True,
                "created_at": datetime.utcnow().isoformat() + "Z",
                "updated_at": datetime.utcnow().isoformat() + "Z"
            },
            {
                "id": str(uuid.uuid4()),
                "name": "Administrative",
                "type": "main_category",
                "parent_id": None,
                "description": "Administrative policies, reports, and documentation",
                "sort_order": 4,
                "is_active": True,
                "created_at": datetime.utcnow().isoformat() + "Z",
                "updated_at": datetime.utcnow().isoformat() + "Z"
            },
            {
                "id": str(uuid.uuid4()),
                "name": "Regulatory & Compliance",
                "type": "main_category",
                "parent_id": None,
                "description": "Regulatory documents, compliance guidelines, and legal requirements",
                "sort_order": 5,
                "is_active": True,
                "created_at": datetime.utcnow().isoformat() + "Z",
                "updated_at": datetime.utcnow().isoformat() + "Z"
            },
            {
                "id": str(uuid.uuid4()),
                "name": "Training & Development",
                "type": "main_category",
                "parent_id": None,
                "description": "Training materials, educational resources, and development programs",
                "sort_order": 6,
                "is_active": True,
                "created_at": datetime.utcnow().isoformat() + "Z",
                "updated_at": datetime.utcnow().isoformat() + "Z"
            }
        ]

        logger.info("Creating standard document categories...")
        
        for category in standard_doc_categories:
            try:
                # Check if category already exists
                existing = supabase.supabase.table('document_categories').select('id').eq('name', category['name']).eq('type', category['type']).is_('parent_id', 'null').execute()
                
                if not existing.data:
                    # Create the category
                    response = supabase.supabase.table('document_categories').insert(category).execute()
                    if response.data:
                        logger.info(f"Created standard category: {category['name']}")
                    else:
                        logger.error(f"Failed to create category: {category['name']}")
                else:
                    logger.info(f"Category already exists: {category['name']}")
                    
            except Exception as e:
                logger.error(f"Error creating category {category['name']}: {str(e)}")

        return True

    except Exception as e:
        logger.error(f"Error in create_standard_categories: {str(e)}")
        return False

async def optimize_category_system():
    """Main function to optimize the entire category system."""
    logger.info("Starting category system optimization...")
    
    # Step 1: Clean up test categories
    logger.info("Step 1: Cleaning up test categories...")
    await cleanup_test_categories()
    
    # Step 2: Remove duplicates
    logger.info("Step 2: Removing duplicate categories...")
    await cleanup_duplicate_categories()
    
    # Step 3: Ensure standard categories exist
    logger.info("Step 3: Ensuring standard categories exist...")
    await create_standard_categories()
    
    logger.info("Category system optimization completed!")
    return True

if __name__ == "__main__":
    asyncio.run(optimize_category_system())
