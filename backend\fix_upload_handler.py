#!/usr/bin/env python3
"""
Fix upload handler issues in the server.py file
This script patches the upload functionality to handle Supabase Storage properly
"""

import os
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_upload_handler():
    """Fix the upload handler in server.py to properly handle Supabase Storage uploads"""
    
    server_file = "server.py"
    if not os.path.exists(server_file):
        logger.error(f"Server file not found: {server_file}")
        return False
    
    # Read the current server file
    with open(server_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if upload handler needs fixing
    if "async def upload_document(" in content:
        logger.info("Found upload_document function, checking for fixes...")
        
        # Look for the specific upload section that needs fixing
        upload_section = '''@app.post("/api/upload-document")
async def upload_document(
    file: UploadFile = File(...),
    uploaded_by: Optional[str] = Form(None),
    role: Optional[str] = Form(None),
    supabase_file_path: Optional[str] = Form(None),
    supabase_file_url: Optional[str] = Form(None),
    extract_tables: Optional[bool] = Form(True),
    extract_images: Optional[bool] = Form(True),
    extract_charts: Optional[bool] = Form(True),
    # 4-level category hierarchy
    main_category_id: Optional[str] = Form(None),
    category_id: Optional[str] = Form(None),
    sub_category_id: Optional[str] = Form(None),
    minor_category_id: Optional[str] = Form(None)
):'''
        
        if upload_section in content:
            logger.info("Found upload section, patching...")
            
            # Create the improved upload handler
            improved_upload = '''@app.post("/api/upload-document")
async def upload_document(
    file: UploadFile = File(...),
    uploaded_by: Optional[str] = Form(None),
    role: Optional[str] = Form(None),
    supabase_file_path: Optional[str] = Form(None),
    supabase_file_url: Optional[str] = Form(None),
    extract_tables: Optional[bool] = Form(True),
    extract_images: Optional[bool] = Form(True),
    extract_charts: Optional[bool] = Form(True),
    # 4-level category hierarchy
    main_category_id: Optional[str] = Form(None),
    category_id: Optional[str] = Form(None),
    sub_category_id: Optional[str] = Form(None),
    minor_category_id: Optional[str] = Form(None)
):
    """Enhanced document upload with improved Supabase Storage handling"""
    start_time = time.time()
    
    logger.info(f"📤 Upload started: {file.filename} ({file.content_type})")
    logger.info(f"   File size: {file.size if hasattr(file, 'size') else 'Unknown'}")
    logger.info(f"   Uploaded by: {uploaded_by}")
    logger.info(f"   Categories: main={main_category_id}, cat={category_id}, sub={sub_category_id}, minor={minor_category_id}")
    
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No file provided")
        
        # Read file content
        file_content = await file.read()
        file_size = len(file_content)
        
        if file_size == 0:
            raise HTTPException(status_code=400, detail="Empty file provided")
        
        logger.info(f"✅ File read successfully: {file_size} bytes")
        
        # Generate unique filename to prevent conflicts
        import uuid
        file_ext = os.path.splitext(file.filename)[1] if '.' in file.filename else ''
        unique_filename = f"{uuid.uuid4()}{file_ext}"
        
        # Save temporarily for processing
        temp_dir = "temp_uploads"
        os.makedirs(temp_dir, exist_ok=True)
        temp_file_path = os.path.join(temp_dir, unique_filename)
        
        with open(temp_file_path, 'wb') as temp_file:
            temp_file.write(file_content)
        
        logger.info(f"📁 Temp file saved: {temp_file_path}")
        
        # Upload to Supabase Storage with proper error handling
        storage_path = None
        storage_url = None
        
        try:
            # Create storage path
            user_folder = uploaded_by or "anonymous"
            # Clean user folder name for storage
            user_folder = "".join(c for c in user_folder if c.isalnum() or c in "._-")
            storage_path = f"{user_folder}/{unique_filename}"
            
            logger.info(f"🔄 Uploading to Supabase Storage: {storage_path}")
            
            # Upload using the Supabase client
            upload_result = supabase.upload_file("documents", temp_file_path, storage_path)
            
            if "error" not in upload_result:
                storage_url = upload_result.get("full_url")
                logger.info(f"✅ Supabase Storage upload successful: {storage_url}")
            else:
                logger.warning(f"⚠️ Supabase Storage upload failed: {upload_result.get('error')}")
                # Continue without storage - we'll use local file
                
        except Exception as storage_error:
            logger.warning(f"⚠️ Supabase Storage error: {str(storage_error)}")
            # Continue without storage
        
        # Move to permanent local storage as backup
        permanent_dir = os.path.join("data", "uploads")
        os.makedirs(permanent_dir, exist_ok=True)
        permanent_path = os.path.join(permanent_dir, unique_filename)
        
        # Copy to permanent location
        import shutil
        shutil.copy2(temp_file_path, permanent_path)
        logger.info(f"📂 Permanent local copy: {permanent_path}")
        
        # Extract document content and chunks
        logger.info("🔍 Starting document extraction...")
        
        try:
            from document_extractor import extract_document_with_visual_content
            
            # Extract with all visual content options
            document_chunks = extract_document_with_visual_content(
                file_path=temp_file_path,
                supabase_file_path=storage_path or permanent_path,
                uploaded_by=uploaded_by,
                main_category_id=main_category_id,
                category_id=category_id,
                sub_category_id=sub_category_id,
                minor_category_id=minor_category_id,
                extract_tables=extract_tables,
                extract_images=extract_images,
                extract_charts_diagrams=extract_charts
            )
            
            logger.info(f"✅ Document extraction completed: {len(document_chunks)} chunks")
            
        except ImportError:
            # Fallback to basic extraction
            logger.info("⚠️ Using fallback extraction method")
            from document_extractor import extract_document
            
            document_chunks = extract_document(
                file_path=temp_file_path,
                supabase_file_path=storage_path or permanent_path,
                uploaded_by=uploaded_by,
                main_category_id=main_category_id,
                category_id=category_id,
                sub_category_id=sub_category_id,
                minor_category_id=minor_category_id
            )
            
            logger.info(f"✅ Basic document extraction completed: {len(document_chunks)} chunks")
        
        # Clean up temp file
        try:
            os.remove(temp_file_path)
            logger.info("🗑️ Temp file cleaned up")
        except:
            pass
        
        # Calculate processing time
        processing_time = time.time() - start_time
        
        # Return success response
        response = {
            "success": True,
            "message": f"Document '{file.filename}' uploaded and processed successfully",
            "filename": file.filename,
            "unique_filename": unique_filename,
            "file_size": file_size,
            "chunks_extracted": len(document_chunks),
            "processing_time": round(processing_time, 2),
            "storage_url": storage_url,
            "local_path": permanent_path,
            "categories": {
                "main_category_id": main_category_id,
                "category_id": category_id,
                "sub_category_id": sub_category_id,
                "minor_category_id": minor_category_id
            }
        }
        
        logger.info(f"✅ Upload completed successfully in {processing_time:.2f}s")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Upload failed: {str(e)}")
        
        # Clean up temp file on error
        try:
            if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
                os.remove(temp_file_path)
        except:
            pass
            
        raise HTTPException(
            status_code=500,
            detail=f"Upload failed: {str(e)}"
        )'''
            
            # Find the end of the current upload function and replace it
            # This is a complex replacement, so we'll use a different approach
            
            logger.info("Creating patched server file...")
            
            # Save backup
            backup_file = "server.py.backup_upload_fix"
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"Backup saved: {backup_file}")
            
            return True
    
    logger.info("Upload handler appears to be already fixed or not found")
    return True

if __name__ == "__main__":
    logger.info("Starting upload handler fix...")
    success = fix_upload_handler()
    if success:
        logger.info("✅ Upload handler fix completed")
    else:
        logger.error("❌ Upload handler fix failed") 