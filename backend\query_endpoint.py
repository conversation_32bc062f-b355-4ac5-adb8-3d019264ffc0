from typing import Dict, Any, List, Optional
import logging
import json
from fastapi import APIRouter, HTTPException
from vector_search import search_all_sources
from llm_router import generate_embedding, get_llm_response

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/api/query")
async def query_endpoint(
    query: str,
    model_id: str = "gemini-2.0-flash",
    use_hybrid_search: bool = True,
    fallback_enabled: bool = True
) -> Dict[str, Any]:
    """
    Main query endpoint that implements the priority-based answer flow:
    1. Search document chunks first (highest priority)
    2. If no document matches, search website chunks
    3. Only use LLM fallback if both searches return no results
    """
    try:
        logger.info(f"Processing query: {query}")
        
        # Generate query embedding
        query_embedding = generate_embedding(query)
        if not query_embedding:
            raise HTTPException(status_code=500, detail="Failed to generate query embedding")
            
        # Search all sources in priority order
        search_results = await search_all_sources(
            query_text=query,
            query_embedding=query_embedding,
            top_k=30  # Increased to ensure good coverage
        )
        
        # Log search results
        logger.info(f"Found {search_results['total_chunks']} chunks from {', '.join(search_results['source_types'])}")
        logger.info(f"LLM fallback used: {search_results['llm_fallback_used']}")
        
        # Generate answer based on chunks or fallback
        if search_results["chunks"]:
            # Use chunks to generate answer
            chunks_for_context = search_results["chunks"][:10]  # Use top 10 chunks
            
            # Create prompt with chunk context
            chunk_texts = []
            source_info = []
            for chunk in chunks_for_context:
                # Add text content
                chunk_texts.append(chunk["text"])
                
                # Add source information
                if chunk["source_type"] == "document":
                    source = f"{chunk['source']['filename']} – Page {chunk['source']['page_number']}"
                else:
                    source = f"{chunk['source']['title']} ({chunk['source']['url']})"
                    
                if source not in source_info:
                    source_info.append(source)
                    
                # Track chunk ID for debugging
                logger.debug(f"Using chunk ID: {chunk.get('id')} from {source}")
            
            # Build prompt with chunks
            context = "\n\n".join(chunk_texts)
            prompt = f"""Answer the following question using only the provided context. Include relevant tables, images, or diagrams if they are mentioned in the context.

Context:
{context}

Question: {query}

Answer:"""

            # Get answer from LLM
            answer = await get_llm_response(prompt, model_id=model_id)
            
            # Prepare visual content
            visual_content = []
            for chunk in chunks_for_context:
                if "visual_content" in chunk:
                    visual_content.extend([{
                        "type": content_type,
                        "data": content_data,
                        "page": chunk["source"]["page_number"],
                        "document": chunk["source"]["filename"]
                    } for content_type, content_list in chunk["visual_content"].items() 
                    for content_data in content_list])
            
            return {
                "answer": answer,
                "sources": source_info,
                "visual_content": visual_content,
                "llm_fallback_used": False,
                "debug": search_results["debug_info"]
            }
            
        elif fallback_enabled:
            # No chunks found, use LLM fallback
            logger.info("No relevant chunks found, using LLM fallback")
            prompt = f"Answer this question to the best of your ability: {query}"
            answer = await get_llm_response(prompt, model_id=model_id)
            
            return {
                "answer": answer,
                "sources": [],
                "visual_content": [],
                "llm_fallback_used": True,
                "debug": search_results["debug_info"]
            }
        else:
            # No chunks and fallback disabled
            return {
                "answer": "I couldn't find any relevant information to answer your question.",
                "sources": [],
                "visual_content": [],
                "llm_fallback_used": False,
                "debug": search_results["debug_info"]
            }
    
    except Exception as e:
        logger.error(f"Error processing query: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))