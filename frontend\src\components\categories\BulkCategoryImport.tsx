import React, { useState } from 'react';
import { Upload, Download, FileText, AlertTriangle, Check, X } from 'lucide-react';
import { createCategory, createWebsiteCategory } from '../../services/categoryApi';
import { CategoryCreate } from '../../types/documents';

interface BulkCategoryImportProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'document' | 'website';
  onImportComplete?: () => void;
}

interface ImportResult {
  success: number;
  failed: number;
  errors: string[];
}

const BulkCategoryImport: React.FC<BulkCategoryImportProps> = ({
  isOpen,
  onClose,
  type,
  onImportComplete
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [importing, setImporting] = useState(false);
  const [result, setResult] = useState<ImportResult | null>(null);
  const [dragOver, setDragOver] = useState(false);

  const handleFileSelect = (selectedFile: File) => {
    if (selectedFile.type === 'text/csv' || selectedFile.type === 'application/json' || selectedFile.name.endsWith('.csv') || selectedFile.name.endsWith('.json')) {
      setFile(selectedFile);
      setResult(null);
    } else {
      alert('Please select a CSV or JSON file');
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile) {
      handleFileSelect(droppedFile);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const parseCSV = (content: string): CategoryCreate[] => {
    const lines = content.split('\n').filter(line => line.trim());
    if (lines.length < 2) throw new Error('CSV must have at least a header and one data row');

    const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
    const requiredHeaders = ['name', 'type'];
    
    if (!requiredHeaders.every(h => headers.includes(h))) {
      throw new Error('CSV must have columns: name, type (optional: parent_name, description)');
    }

    const categories: CategoryCreate[] = [];
    
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim().replace(/^"|"$/g, ''));
      const category: any = {};
      
      headers.forEach((header, index) => {
        if (values[index]) {
          category[header] = values[index];
        }
      });

      if (category.name && category.type) {
        categories.push({
          name: category.name,
          type: category.type,
          parent_id: undefined, // Will be resolved later
          description: category.description || '',
          sort_order: 0
        });
      }
    }

    return categories;
  };

  const parseJSON = (content: string): CategoryCreate[] => {
    const data = JSON.parse(content);
    if (!Array.isArray(data)) {
      throw new Error('JSON must be an array of category objects');
    }

    return data.map((item: any) => ({
      name: item.name,
      type: item.type,
      parent_id: undefined, // Will be resolved later
      description: item.description || '',
      sort_order: item.sort_order || 0
    }));
  };

  const handleImport = async () => {
    if (!file) return;

    setImporting(true);
    const result: ImportResult = { success: 0, failed: 0, errors: [] };

    try {
      const content = await file.text();
      let categories: CategoryCreate[];

      if (file.name.endsWith('.csv')) {
        categories = parseCSV(content);
      } else {
        categories = parseJSON(content);
      }

      // Import categories one by one
      for (const category of categories) {
        try {
          if (type === 'document') {
            await createCategory(category);
          } else {
            await createWebsiteCategory(category);
          }
          result.success++;
        } catch (error: any) {
          result.failed++;
          result.errors.push(`${category.name}: ${error.message || 'Unknown error'}`);
        }
      }

      setResult(result);
      onImportComplete?.();

    } catch (error: any) {
      result.failed = 1;
      result.errors.push(error.message || 'Failed to parse file');
      setResult(result);
    } finally {
      setImporting(false);
    }
  };

  const downloadTemplate = () => {
    const csvContent = `name,type,description
Safety Guidelines,main_category,Safety related documents
Technical Manuals,main_category,Technical documentation
Maintenance,category,Equipment maintenance procedures
Operations,category,Operational procedures
Emergency Procedures,sub_category,Emergency response protocols
Daily Maintenance,sub_category,Regular maintenance tasks
Critical Alerts,minor_category,High priority safety alerts
Standard Procedures,minor_category,Standard operating procedures`;

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${type}_categories_template.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const reset = () => {
    setFile(null);
    setResult(null);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <Upload className="text-blue-600" size={24} />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Bulk Import {type === 'document' ? 'Document' : 'Website'} Categories
              </h2>
              <p className="text-sm text-gray-600">
                Upload CSV or JSON file to create multiple categories at once
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {!result ? (
            <div className="space-y-6">
              {/* Template Download */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center gap-3 mb-2">
                  <Download className="text-blue-600" size={20} />
                  <h3 className="font-medium text-blue-900">Download Template</h3>
                </div>
                <p className="text-sm text-blue-700 mb-3">
                  Download a sample CSV template to see the required format.
                </p>
                <button
                  onClick={downloadTemplate}
                  className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <Download size={16} />
                  Download Template
                </button>
              </div>

              {/* File Upload */}
              <div>
                <h3 className="font-medium text-gray-900 mb-3">Upload Categories File</h3>
                <div
                  className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                    dragOver
                      ? 'border-blue-500 bg-blue-50'
                      : file
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                  onDrop={handleDrop}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                >
                  {file ? (
                    <div className="space-y-2">
                      <FileText className="mx-auto text-green-600" size={48} />
                      <p className="text-lg font-medium text-green-900">{file.name}</p>
                      <p className="text-sm text-green-700">
                        {(file.size / 1024).toFixed(1)} KB • {file.type || 'Unknown type'}
                      </p>
                      <button
                        onClick={reset}
                        className="text-sm text-red-600 hover:text-red-800 underline"
                      >
                        Remove file
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <Upload className="mx-auto text-gray-400" size={48} />
                      <div>
                        <p className="text-lg font-medium text-gray-900">
                          Drop your CSV or JSON file here
                        </p>
                        <p className="text-sm text-gray-600">or click to browse</p>
                      </div>
                      <input
                        type="file"
                        accept=".csv,.json"
                        onChange={(e) => e.target.files?.[0] && handleFileSelect(e.target.files[0])}
                        className="hidden"
                        id="file-upload"
                      />
                      <label
                        htmlFor="file-upload"
                        className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer transition-colors"
                      >
                        <Upload size={16} />
                        Choose File
                      </label>
                    </div>
                  )}
                </div>
              </div>

              {/* Format Information */}
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">File Format Requirements</h4>
                <div className="text-sm text-gray-700 space-y-2">
                  <p><strong>CSV Format:</strong> Must have columns: name, type</p>
                  <p><strong>Optional columns:</strong> description, parent_name</p>
                  <p><strong>Valid types:</strong> main_category, category, sub_category, minor_category</p>
                  <p><strong>JSON Format:</strong> Array of objects with name, type, and optional description</p>
                </div>
              </div>
            </div>
          ) : (
            /* Results */
            <div className="space-y-4">
              <div className="text-center">
                <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-full ${
                  result.failed === 0 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {result.failed === 0 ? <Check size={20} /> : <AlertTriangle size={20} />}
                  <span className="font-medium">
                    Import {result.failed === 0 ? 'Completed' : 'Completed with Errors'}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">{result.success}</div>
                  <div className="text-sm text-green-700">Successfully Created</div>
                </div>
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold text-red-600">{result.failed}</div>
                  <div className="text-sm text-red-700">Failed</div>
                </div>
              </div>

              {result.errors.length > 0 && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-medium text-red-900 mb-2">Errors:</h4>
                  <div className="max-h-32 overflow-y-auto">
                    {result.errors.map((error, index) => (
                      <p key={index} className="text-sm text-red-700 mb-1">
                        • {error}
                      </p>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200">
          {result ? (
            <>
              <button
                onClick={reset}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              >
                Import Another File
              </button>
              <button
                onClick={onClose}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                Close
              </button>
            </>
          ) : (
            <>
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleImport}
                disabled={!file || importing}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {importing ? 'Importing...' : 'Import Categories'}
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default BulkCategoryImport;
