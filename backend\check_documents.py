#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def check_documents():
    base_url = "http://localhost:8000"
    
    print("📚 Checking Documents in Database")
    print("=" * 50)
    
    # Make a query to see what documents we can find
    print("\n1. Testing search for 'ACP 110V'")
    try:
        query_data = {
            "query": "ACP 110V",
            "model": "gemini-2.0-flash",
            "fallback_enabled": True,
            "use_hybrid_search": True
        }
        response = requests.post(f"{base_url}/api/query", json=query_data, timeout=60)
        if response.status_code == 200:
            data = response.json()
            print("✅ ACP 110V query completed")
            
            doc_sources = data.get("document_sources", [])
            print(f"   Document sources found: {len(doc_sources)}")
            
            if doc_sources:
                print(f"\n   📄 All Document Sources:")
                for i, source in enumerate(doc_sources):
                    print(f"      {i+1}. File: {source.get('filename', 'unknown')}")
                    print(f"         Page: {source.get('page_number', 'N/A')}")
                    print(f"         Chunk ID: {source.get('id', 'N/A')}")
                    print(f"         Content type: {source.get('content_type', 'N/A')}")
                    content = source.get('content', '')
                    print(f"         Content length: {len(content)} characters")
                    if content:
                        preview = content[:200] + "..." if len(content) > 200 else content
                        print(f"         Content preview: {preview}")
                    else:
                        print(f"         Content preview: [EMPTY]")
                    print()
        else:
            print(f"❌ Query failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Query error: {str(e)}")
    
    # Try a broader search
    print("\n2. Testing broader search for documents")
    try:
        query_data = {
            "query": "railway document",
            "model": "gemini-2.0-flash",
            "fallback_enabled": True,
            "use_hybrid_search": True
        }
        response = requests.post(f"{base_url}/api/query", json=query_data, timeout=60)
        if response.status_code == 200:
            data = response.json()
            print("✅ Railway document query completed")
            
            doc_sources = data.get("document_sources", [])
            print(f"   Document sources found: {len(doc_sources)}")
            
            # List all unique filenames
            filenames = set()
            for source in doc_sources:
                filename = source.get('filename', 'unknown')
                filenames.add(filename)
            
            print(f"\n   📂 Unique files in database:")
            for filename in sorted(filenames):
                print(f"      - {filename}")
                
        else:
            print(f"❌ Query failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Query error: {str(e)}")
    
    # Check if ACP 110V.pdf specifically exists
    print("\n3. Searching specifically for 'ACP 110V.pdf'")
    try:
        query_data = {
            "query": "110V",
            "model": "gemini-2.0-flash",
            "fallback_enabled": True,
            "use_hybrid_search": True
        }
        response = requests.post(f"{base_url}/api/query", json=query_data, timeout=60)
        if response.status_code == 200:
            data = response.json()
            doc_sources = data.get("document_sources", [])
            
            acp_found = False
            for source in doc_sources:
                if "ACP" in source.get('filename', '') or "110V" in source.get('filename', ''):
                    acp_found = True
                    print(f"✅ Found ACP-related document: {source.get('filename', 'unknown')}")
                    content = source.get('content', '')
                    print(f"   Content length: {len(content)} characters")
                    if content:
                        preview = content[:300] + "..." if len(content) > 300 else content
                        print(f"   Content preview: {preview}")
                    break
            
            if not acp_found:
                print("❌ ACP 110V.pdf not found in search results")
                print("   This means either:")
                print("   1. The document wasn't uploaded successfully")
                print("   2. The document wasn't processed/chunked properly")
                print("   3. The search isn't finding the right chunks")
                
        else:
            print(f"❌ Query failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Query error: {str(e)}")

if __name__ == "__main__":
    check_documents() 