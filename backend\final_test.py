import requests
import json

print("🔍 TESTING NON-TEXT DATA & SOURCE ATTRIBUTION")
print("=" * 50)

# Test query for tables and images  
response = requests.post("http://localhost:8000/api/query", json={
    "query": "Show me the isolation table and any diagrams from FSDS",
    "use_hybrid_search": True
})

result = response.json()

print(f"✅ Sources found: {len(result.get('sources', []))}")
print(f"🎨 Visual content: {result.get('visual_content_found', False)}")
print(f"📋 Visual types: {result.get('visual_content_types', [])}")
print(f"🤖 LLM fallback: {result.get('llm_fallback', True)}")

# Check source attribution
unique_docs = {}
for source in result.get('sources', []):
    filename = source.get('filename', 'Unknown')
    pages = source.get('pages', [source.get('page')])
    
    if filename not in unique_docs:
        unique_docs[filename] = set()
    
    if isinstance(pages, list):
        unique_docs[filename].update(pages)
    else:
        unique_docs[filename].add(pages)

print(f"\n📄 Document sources:")
for doc, pages in unique_docs.items():
    sorted_pages = sorted(list(pages))
    print(f"   - {doc}: Pages {sorted_pages}")

# Check table and image data
table_count = 0
image_count = 0

for source in result.get('sources', []):
    visual = source.get('visual_content', {})
    
    if source.get('content_type') == 'table':
        table_count += 1
        if 'table_html' in visual:
            print(f"✅ Table HTML: {len(visual['table_html'])} chars")
        else:
            print(f"❌ Missing table_html")
    
    elif source.get('content_type') == 'image':
        image_count += 1
        if 'images' in visual:
            print(f"✅ Images array: {len(visual['images'])} items")
        else:
            print(f"❌ Missing images array")

print(f"\n📊 RESULTS:")
print(f"  Tables: {table_count}")
print(f"  Images: {image_count}")
print(f"  Documents: {len(unique_docs)}")

if table_count > 0 and image_count > 0:
    print(f"\n🎯 ✅ NON-TEXT DATA WORKING!")
else:
    print(f"\n⚠️ NON-TEXT DATA NEEDS ATTENTION")

def test_complete_functionality():
    print("🔍 TESTING COMPLETE NON-TEXT DATA & SOURCE ATTRIBUTION FUNCTIONALITY")
    print("=" * 70)
    
    # Test 1: Query for tables and images
    print("\n1️⃣ TESTING TABLE & IMAGE EXTRACTION")
    response = requests.post("http://localhost:8000/api/query", json={
        "query": "Show me the isolation table and any diagrams from FSDS",
        "use_hybrid_search": True,
        "extract_format": "paragraph"
    })
    
    result = response.json()
    
    print(f"✅ Query executed successfully")
    print(f"📊 Sources found: {len(result.get('sources', []))}")
    print(f"🎨 Visual content found: {result.get('visual_content_found', False)}")
    print(f"📋 Visual types: {result.get('visual_content_types', [])}")
    print(f"🤖 LLM fallback used: {result.get('llm_fallback', True)}")
    
    # Check source attribution
    print(f"\n2️⃣ TESTING SOURCE ATTRIBUTION")
    unique_docs = {}
    for source in result.get('sources', []):
        filename = source.get('filename', 'Unknown')
        pages = source.get('pages', [source.get('page')])
        
        if filename not in unique_docs:
            unique_docs[filename] = set()
        
        if isinstance(pages, list):
            unique_docs[filename].update(pages)
        else:
            unique_docs[filename].add(pages)
    
    print(f"📄 Unique documents: {len(unique_docs)}")
    for doc, pages in unique_docs.items():
        sorted_pages = sorted(list(pages))
        print(f"   - {doc}: Pages {sorted_pages}")
    
    # Check non-text data rendering
    print(f"\n3️⃣ TESTING NON-TEXT DATA RENDERING")
    table_count = 0
    image_count = 0
    
    for i, source in enumerate(result.get('sources', []), 1):
        content_type = source.get('content_type', 'unknown')
        visual_content = source.get('visual_content', {})
        
        print(f"\nSource {i}: {source.get('filename')} (Page {source.get('page')})")
        print(f"  Content type: {content_type}")
        
        if content_type == 'table' or visual_content.get('visual_content_type') == 'table':
            table_count += 1
            if 'table_html' in visual_content:
                html_len = len(visual_content['table_html'])
                print(f"  ✅ Table HTML: {html_len} characters")
                if html_len > 50:
                    print(f"  📋 Preview: {visual_content['table_html'][:100]}...")
            else:
                print(f"  ❌ Missing table_html")
                
            if 'table_data' in visual_content:
                print(f"  📊 Table data: {len(visual_content['table_data'])} rows")
        
        elif content_type == 'image' or visual_content.get('visual_content_type') == 'image':
            image_count += 1
            if 'images' in visual_content:
                images = visual_content['images']
                print(f"  ✅ Images array: {len(images)} items")
                for j, img in enumerate(images[:2], 1):  # Show first 2
                    if isinstance(img, str):
                        print(f"    {j}. URL: {img[:50]}...")
                    elif isinstance(img, dict):
                        print(f"    {j}. Object: {list(img.keys())}")
            else:
                print(f"  ❌ Missing images array")
                
            if 'storage_url' in visual_content:
                print(f"  🔗 Storage URL: {visual_content['storage_url'][:50]}...")
                
    print(f"\n📊 SUMMARY:")
    print(f"  Tables found: {table_count}")
    print(f"  Images found: {image_count}")
    print(f"  Total visual sources: {table_count + image_count}")
    
    # Test 2: Frontend compatibility test
    print(f"\n4️⃣ TESTING FRONTEND COMPATIBILITY")
    frontend_ready = True
    
    for source in result.get('sources', []):
        visual = source.get('visual_content', {})
        
        # Check table rendering readiness
        if source.get('content_type') == 'table':
            if 'table_html' not in visual:
                print(f"  ❌ Table source missing table_html for frontend")
                frontend_ready = False
        
        # Check image rendering readiness
        elif source.get('content_type') == 'image':
            if 'images' not in visual and 'storage_url' not in visual and 'base64_data' not in visual:
                print(f"  ❌ Image source missing display data for frontend")
                frontend_ready = False
    
    if frontend_ready:
        print(f"  ✅ All sources are frontend-ready!")
    
    # Test 3: LLM fallback behavior
    print(f"\n5️⃣ TESTING LLM FALLBACK BEHAVIOR")
    fallback_response = requests.post("http://localhost:8000/api/query", json={
        "query": "What is the meaning of life and universe",  # Should not match any documents
        "use_hybrid_search": True
    })
    
    fallback_result = fallback_response.json()
    fallback_used = fallback_result.get('llm_fallback', False)
    fallback_sources = len(fallback_result.get('sources', []))
    
    print(f"  LLM fallback used: {fallback_used}")
    print(f"  Sources when fallback: {fallback_sources}")
    
    if fallback_used and fallback_sources == 0:
        print(f"  ✅ Correct fallback behavior!")
    elif not fallback_used and fallback_sources > 0:
        print(f"  ✅ Chunks found, no fallback needed!")
    else:
        print(f"  ⚠️ Unexpected fallback behavior")
    
    print(f"\n🎯 FINAL ASSESSMENT:")
    if table_count > 0 and image_count > 0 and len(unique_docs) > 0 and frontend_ready:
        print(f"✅ ALL FUNCTIONALITY WORKING!")
        print(f"✅ Tables render inline with HTML")
        print(f"✅ Images display in galleries")  
        print(f"✅ Sources show true document attribution")
        print(f"✅ LLM fallback only when no chunks found")
    else:
        print(f"❌ Some functionality needs attention:")
        if table_count == 0:
            print(f"   - No tables with HTML found")
        if image_count == 0:
            print(f"   - No images with display data found")
        if len(unique_docs) == 0:
            print(f"   - No proper source attribution")
        if not frontend_ready:
            print(f"   - Frontend compatibility issues")

if __name__ == "__main__":
    test_complete_functionality() 