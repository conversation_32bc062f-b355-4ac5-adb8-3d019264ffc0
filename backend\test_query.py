#!/usr/bin/env python3
import requests
import json
import sys

def test_query():
    url = "http://localhost:8000/api/query"
    
    # Test query for tables and images
    query_data = {
        "query": "Show me the isolation table from FSDS",
        "use_hybrid_search": True,
        "extract_format": "paragraph"
    }
    
    try:
        response = requests.post(url, json=query_data)
        response.raise_for_status()
        
        result = response.json()
        
        print("=== QUERY RESPONSE ===")
        print(f"Answer: {result.get('answer', 'No answer')[:200]}...")
        print(f"Sources count: {len(result.get('sources', []))}")
        print(f"LLM fallback used: {result.get('llm_fallback', 'Unknown')}")
        print(f"Visual content found: {result.get('visual_content_found', 'Unknown')}")
        print(f"Visual content types: {result.get('visual_content_types', [])}")
        
        print("\n=== SOURCES BREAKDOWN ===")
        for i, source in enumerate(result.get('sources', []), 1):
            print(f"{i}. Type: {source.get('source_type', 'unknown')}")
            print(f"   Filename: {source.get('filename', 'unknown')}")
            print(f"   Page: {source.get('page', 'unknown')}")
            print(f"   Pages: {source.get('pages', 'unknown')}")
            print(f"   Content type: {source.get('content_type', 'unknown')}")
            
            if source.get('visual_content'):
                visual = source['visual_content']
                print(f"   Visual content type: {visual.get('visual_content_type', 'unknown')}")
                if 'table_html' in visual:
                    print(f"   Table HTML present: {len(visual['table_html'])} chars")
                if 'images' in visual:
                    print(f"   Images: {len(visual.get('images', []))} items")
            print()
        
        # Test a simpler query for tables only
        print("\n=== TESTING TABLE-SPECIFIC QUERY ===")
        table_query = {
            "query": "What are the isolation requirements table from FSDS",
            "use_hybrid_search": True,
            "extract_format": "table"
        }
        
        table_response = requests.post(url, json=table_query)
        table_result = table_response.json()
        
        print(f"Table query answer: {table_result.get('answer', 'No answer')[:200]}...")
        print(f"Table query sources: {len(table_result.get('sources', []))}")
        
        # Check if any sources have table_html
        table_sources_with_html = [s for s in table_result.get('sources', []) 
                                 if s.get('visual_content', {}).get('table_html')]
        print(f"Sources with table_html: {len(table_sources_with_html)}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("Error: Cannot connect to server. Is it running on http://localhost:8000?")
        return False
    except Exception as e:
        print(f"Error testing query: {e}")
        return False

if __name__ == "__main__":
    success = test_query()
    sys.exit(0 if success else 1) 