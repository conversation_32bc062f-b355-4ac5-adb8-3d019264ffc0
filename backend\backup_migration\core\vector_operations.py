"""
Vector Operations Module for RailGPT
Handles embedding generation and similarity calculations with caching
"""

import logging
import os
from typing import List, Optional
from functools import lru_cache
import numpy as np
import google.generativeai as genai
from dotenv import load_dotenv

load_dotenv()
logger = logging.getLogger(__name__)

# Configure Gemini API
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)
    logger.info("✅ Gemini API configured for embeddings")
else:
    logger.warning("⚠️ GEMINI_API_KEY not found. Using fallback embeddings.")

# Embedding cache to reduce API calls
EMBEDDING_CACHE = {}
CACHE_MAX_SIZE = 10000

@lru_cache(maxsize=CACHE_MAX_SIZE)
def generate_embedding(text: str, model_id: str = "gemini-2.0-flash") -> List[float]:
    """
    Generate optimized embeddings with caching
    """
    if not text or not text.strip():
        logger.warning("Empty text provided for embedding generation")
        return list(np.random.rand(768))
    
    # Check cache first
    cache_key = f"{model_id}:{hash(text)}"
    if cache_key in EMBEDDING_CACHE:
        return EMBEDDING_CACHE[cache_key]
    
    try:
        if GEMINI_API_KEY:
            # Use Gemini for high-quality embeddings
            response = genai.embed_content(
                model="models/embedding-001",
                content=text,
                task_type="retrieval_document"
            )
            embedding = list(response['embedding'])
            
            # Cache the result
            if len(EMBEDDING_CACHE) < CACHE_MAX_SIZE:
                EMBEDDING_CACHE[cache_key] = embedding
            
            return embedding
        else:
            # Fallback: simple hash-based pseudo-embedding
            logger.warning("Using fallback embedding for: " + text[:50] + "...")
            hash_val = hash(text) % (2**32)
            embedding = list(np.random.RandomState(hash_val).rand(768))
            return embedding
            
    except Exception as e:
        logger.error(f"❌ Embedding generation failed: {str(e)}")
        # Return deterministic fallback based on text hash
        hash_val = hash(text) % (2**32)
        return list(np.random.RandomState(hash_val).rand(768))

def cosine_similarity(embedding1: List[float], embedding2: List[float]) -> float:
    """
    Calculate cosine similarity between two embeddings
    """
    try:
        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)
        
        # Handle zero vectors
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        # Calculate cosine similarity
        similarity = np.dot(vec1, vec2) / (norm1 * norm2)
        return float(similarity)
        
    except Exception as e:
        logger.error(f"❌ Cosine similarity calculation failed: {str(e)}")
        return 0.0

def euclidean_distance(embedding1: List[float], embedding2: List[float]) -> float:
    """
    Calculate Euclidean distance between two embeddings
    """
    try:
        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)
        return float(np.linalg.norm(vec1 - vec2))
    except Exception as e:
        logger.error(f"❌ Euclidean distance calculation failed: {str(e)}")
        return float('inf')

def semantic_similarity_score(
    query_embedding: List[float], 
    chunk_embedding: List[float],
    boost_factors: Optional[dict] = None
) -> float:
    """
    Calculate enhanced semantic similarity with optional boost factors
    """
    base_similarity = cosine_similarity(query_embedding, chunk_embedding)
    
    if boost_factors:
        # Apply content type boost
        content_boost = boost_factors.get('content_boost', 1.0)
        domain_boost = boost_factors.get('domain_boost', 1.0)
        
        enhanced_similarity = base_similarity * content_boost * domain_boost
        return min(enhanced_similarity, 1.0)  # Cap at 1.0
    
    return base_similarity

def batch_similarity_calculation(
    query_embedding: List[float], 
    chunk_embeddings: List[List[float]]
) -> List[float]:
    """
    Calculate similarities in batch for performance
    """
    try:
        query_vec = np.array(query_embedding)
        chunk_matrix = np.array(chunk_embeddings)
        
        # Vectorized cosine similarity calculation
        query_norm = np.linalg.norm(query_vec)
        chunk_norms = np.linalg.norm(chunk_matrix, axis=1)
        
        # Handle zero norms
        valid_indices = (query_norm > 0) & (chunk_norms > 0)
        similarities = np.zeros(len(chunk_embeddings))
        
        if np.any(valid_indices):
            dot_products = np.dot(chunk_matrix[valid_indices], query_vec)
            similarities[valid_indices] = dot_products / (chunk_norms[valid_indices] * query_norm)
        
        return similarities.tolist()
        
    except Exception as e:
        logger.error(f"❌ Batch similarity calculation failed: {str(e)}")
        return [0.0] * len(chunk_embeddings)

def clear_embedding_cache():
    """Clear the embedding cache"""
    global EMBEDDING_CACHE
    EMBEDDING_CACHE.clear()
    logger.info("🧹 Embedding cache cleared")

def get_cache_stats() -> dict:
    """Get embedding cache statistics"""
    return {
        "cache_size": len(EMBEDDING_CACHE),
        "max_size": CACHE_MAX_SIZE,
        "cache_hit_ratio": len(EMBEDDING_CACHE) / max(1, CACHE_MAX_SIZE)
    } 