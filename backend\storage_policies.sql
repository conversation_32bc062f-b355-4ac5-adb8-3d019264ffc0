-- Supabase Storage Policies for IR App
-- Run this in your Supabase SQL Editor to fix storage upload and access issues

-- First, ensure the documents bucket exists and is configured properly
-- You may need to run this in the Supabase Dashboard > Storage section first:
-- CREATE BUCKET 'documents' WITH (public = true, file_size_limit = 52428800);

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow authenticated uploads to documents bucket" ON storage.objects;
DROP POLICY IF EXISTS "Allow public downloads from documents bucket" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated updates to documents bucket" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated deletions from documents bucket" ON storage.objects;
DROP POLICY IF EXISTS "Allow anonymous uploads to documents bucket" ON storage.objects;

-- Enable RLS on storage.objects if not already enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Create comprehensive storage policies for the documents bucket

-- Policy 1: Allow anyone to upload to documents bucket (most permissive)
CREATE POLICY "Public upload access to documents"
ON storage.objects
FOR INSERT
WITH CHECK (bucket_id = 'documents');

-- Policy 2: Allow anyone to read from documents bucket
CREATE POLICY "Public read access to documents"
ON storage.objects
FOR SELECT
USING (bucket_id = 'documents');

-- Policy 3: Allow updates to documents bucket
CREATE POLICY "Public update access to documents"
ON storage.objects
FOR UPDATE
USING (bucket_id = 'documents')
WITH CHECK (bucket_id = 'documents');

-- Policy 4: Allow deletions from documents bucket
CREATE POLICY "Public delete access to documents"
ON storage.objects
FOR DELETE
USING (bucket_id = 'documents');

-- Alternative: More restrictive policies (use these instead if you want more security)
/*
-- Allow authenticated users to upload
CREATE POLICY "Authenticated upload to documents"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'documents' AND
  auth.role() = 'authenticated'
);

-- Allow public read access
CREATE POLICY "Public read from documents"
ON storage.objects
FOR SELECT
USING (bucket_id = 'documents');
*/

-- Verify the setup (optional checks)
-- SELECT * FROM storage.buckets WHERE id = 'documents';
-- SELECT policyname, cmd, qual FROM pg_policies WHERE tablename = 'objects' AND schemaname = 'storage';

-- Test upload (run this to verify the setup works)
/*
SELECT storage.upload(
  'documents',
  'test-upload.txt',
  'This is a test file to verify upload permissions',
  'text/plain'
);
*/ 