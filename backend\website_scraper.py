import os
import re
import logging
import hashlib
import tempfile
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse
import httpx
import trafilatura
from bs4 import BeautifulSoup
import fitz  # PyMuPDF for PDF processing

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clean_text(text: str) -> str:
    """Clean and normalize text from website extraction."""
    if not text:
        return ""
    # Replace multiple newlines with single newline
    text = re.sub(r'\n+', '\n', text)
    # Replace multiple spaces with single space
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def extract_with_trafilatura(url: str) -> Optional[str]:
    """Extract website content using Trafilatura."""
    try:
        logger.info(f"Extracting content from {url} using Trafilatura")
        downloaded = trafilatura.fetch_url(url)

        if downloaded is None:
            logger.warning(f"Trafilatura failed to fetch URL: {url}")
            return None

        text = trafilatura.extract(downloaded, include_comments=False, include_tables=True)

        if not text or len(text) < 500:  # Minimal content threshold
            logger.warning(f"Trafilatura extracted too little content ({len(text) if text else 0} chars): {url}")
            return None

        return clean_text(text)

    except Exception as e:
        logger.error(f"Error extracting content with Trafilatura: {str(e)}")
        return None

def extract_with_beautifulsoup(url: str) -> Optional[str]:
    """Extract website content using BeautifulSoup as a fallback."""
    try:
        logger.info(f"Extracting content from {url} using BeautifulSoup")

        # Set timeout and headers to avoid blocking
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        # Fetch the URL with a timeout
        response = httpx.get(url, headers=headers, timeout=30.0, follow_redirects=True)

        if response.status_code != 200:
            logger.warning(f"Failed to fetch URL: {url}, status code: {response.status_code}")
            return None

        # Parse HTML
        soup = BeautifulSoup(response.text, 'html.parser')

        # Remove script and style elements
        for script in soup(["script", "style", "nav", "footer", "header"]):
            script.decompose()

        # Extract text from main content elements
        extracted_texts = []

        # Extract headings
        for heading in soup.find_all(re.compile('^h[1-6]$')):
            extracted_texts.append(heading.get_text())

        # Extract paragraphs
        for paragraph in soup.find_all('p'):
            extracted_texts.append(paragraph.get_text())

        # Extract list items
        for list_item in soup.find_all('li'):
            extracted_texts.append(list_item.get_text())

        # Extract tables
        for table in soup.find_all('table'):
            for row in table.find_all('tr'):
                cells = [cell.get_text() for cell in row.find_all(['td', 'th'])]
                if cells:
                    extracted_texts.append(' | '.join(cells))

        # Join all extracted text
        combined_text = '\n'.join(extracted_texts)
        cleaned_text = clean_text(combined_text)

        if not cleaned_text or len(cleaned_text) < 500:  # Minimal content threshold
            logger.warning(f"BeautifulSoup extracted too little content ({len(cleaned_text) if cleaned_text else 0} chars): {url}")
            return None

        return cleaned_text

    except Exception as e:
        logger.error(f"Error extracting content with BeautifulSoup: {str(e)}")
        return None

def extract_with_scrapy(url: str) -> Optional[str]:
    """
    Extract website content using Scrapy as a last resort fallback.
    Note: This is a simplistic implementation and would be better as a proper Scrapy spider.
    """
    # In a production environment, you would create a Scrapy spider
    # For simplicity, we'll just log an error and return None
    logger.warning(f"Scrapy extraction not implemented for URL: {url}")
    return None

def extract_pdf_from_url(url: str) -> Optional[str]:
    """
    Download and extract text from a PDF file at the given URL.
    Returns the extracted text as a string.
    """
    try:
        logger.info(f"Extracting PDF content from URL: {url}")

        # Set headers to avoid blocking
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        # Download the PDF file
        response = httpx.get(url, headers=headers, timeout=60.0, follow_redirects=True)

        if response.status_code != 200:
            logger.warning(f"Failed to download PDF from URL: {url}, status code: {response.status_code}")
            return None

        # Create a temporary file to save the PDF
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
            temp_file_path = temp_file.name
            temp_file.write(response.content)

        logger.info(f"PDF downloaded to temporary file: {temp_file_path}")

        # Extract text from the PDF using PyMuPDF
        extracted_text = ""
        try:
            with fitz.open(temp_file_path) as pdf:
                logger.info(f"PDF has {len(pdf)} pages")

                # Extract text from each page
                for page_num, page in enumerate(pdf):
                    page_text = page.get_text()
                    if page_text:
                        extracted_text += f"\n\n--- Page {page_num + 1} ---\n\n"
                        extracted_text += page_text

            # Clean up the temporary file
            os.unlink(temp_file_path)

            if not extracted_text or len(extracted_text) < 500:  # Minimal content threshold
                logger.warning(f"PDF extraction yielded too little content ({len(extracted_text) if extracted_text else 0} chars): {url}")
                return None

            return clean_text(extracted_text)

        except Exception as pdf_error:
            logger.error(f"Error extracting text from PDF: {str(pdf_error)}")
            # Clean up the temporary file
            os.unlink(temp_file_path)
            return None

    except Exception as e:
        logger.error(f"Error downloading or processing PDF from URL: {str(e)}")
        return None

def create_chunks_from_website(text: str, url: str, target_size: int = 400, overlap: int = 100) -> List[Dict[str, Any]]:
    """
    Split website text into chunks with metadata.

    Improvements:
    - Increased overlap from 50 to 100 words to better preserve context
    - Added sentence boundary detection to avoid splitting in the middle of sentences
    - Added more metadata for better context
    - Added chunk position metadata (start, middle, end) for better context awareness
    """
    chunks = []

    if not text:
        logger.warning(f"No text to chunk for URL: {url}")
        return chunks

    # Generate a unique identifier for the URL
    url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
    parsed_url = urlparse(url)
    domain = parsed_url.netloc

    # Try to get a title from the URL path
    path_parts = [p for p in parsed_url.path.split('/') if p]
    title = path_parts[-1] if path_parts else domain

    # Add URL as context prefix
    context_prefix = f"Website: {domain}, URL: {url}\n\n"

    # Split text into sentences first
    sentences = re.split(r'(?<=[.!?])\s+', text)

    # Group sentences into chunks
    current_chunk = []
    current_word_count = 0
    chunk_index = 0

    for sentence in sentences:
        # Count words in this sentence
        sentence_words = sentence.split()
        sentence_word_count = len(sentence_words)

        # If adding this sentence would exceed the target size and we already have content,
        # create a chunk and start a new one
        if current_word_count + sentence_word_count > target_size and current_word_count > 0:
            # Create chunk from current sentences
            chunk_text = " ".join(current_chunk)

            # Determine chunk position (start, middle, end)
            position = "start" if chunk_index == 0 else "middle"

            # Create unique chunk ID
            chunk_id = f"{domain}_{url_hash}_{chunk_index}"

            # Add context prefix to the chunk text
            full_chunk_text = context_prefix + chunk_text

            # Create metadata
            metadata = {
                "url": url,
                "domain": domain,
                "title": title,
                "position": position,
                "word_count": current_word_count
            }

            chunks.append({
                "source_type": "website",  # Always use "website" for compatibility with search
                "url": url,
                "domain": domain,
                "title": title,
                "chunk_id": chunk_id,
                "text": full_chunk_text,
                "metadata": metadata,
                "chunk_index": chunk_index
            })

            # Start a new chunk with overlap
            # Take the last few sentences to maintain context
            overlap_sentences = []
            overlap_word_count = 0

            # Work backwards through current_chunk to get overlap sentences
            for s in reversed(current_chunk):
                s_words = s.split()
                if overlap_word_count + len(s_words) <= overlap:
                    overlap_sentences.insert(0, s)
                    overlap_word_count += len(s_words)
                else:
                    break

            # Reset current chunk with overlap sentences
            current_chunk = overlap_sentences
            current_word_count = overlap_word_count
            chunk_index += 1

        # Add the current sentence to the chunk
        current_chunk.append(sentence)
        current_word_count += sentence_word_count

    # Don't forget the last chunk if there's anything left
    if current_chunk:
        chunk_text = " ".join(current_chunk)

        # Mark as the end chunk
        position = "end" if chunk_index > 0 else "single"

        # Create unique chunk ID
        chunk_id = f"{domain}_{url_hash}_{chunk_index}"

        # Add context prefix to the chunk text
        full_chunk_text = context_prefix + chunk_text

        # Create metadata
        metadata = {
            "url": url,
            "domain": domain,
            "title": title,
            "position": position,
            "word_count": current_word_count
        }

        chunks.append({
            "source_type": "website",  # Always use "website" for compatibility with search
            "url": url,
            "domain": domain,
            "title": title,
            "chunk_id": chunk_id,
            "text": full_chunk_text,
            "metadata": metadata,
            "chunk_index": chunk_index
        })

    return chunks

def extract_website_text(url: str) -> List[Dict[str, Any]]:
    """
    Extract text from a website URL using multiple fallback methods.
    Returns chunked text with metadata.
    """
    logger.info(f"Extracting website content from: {url}")

    # Validate URL format
    try:
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            logger.error(f"Invalid URL: {url}")
            return []
    except Exception as e:
        logger.error(f"Error parsing URL {url}: {str(e)}")
        return []

    # Check if the URL points to a PDF file
    is_pdf_url = False
    path_lower = parsed_url.path.lower()
    if path_lower.endswith('.pdf'):
        is_pdf_url = True
        logger.info(f"Detected PDF URL: {url}")

    # Try extraction methods in fallback order
    content = None
    extraction_method = ""

    if is_pdf_url:
        # For PDF URLs, use the PDF extraction method directly
        content = extract_pdf_from_url(url)
        extraction_method = "pdf_extraction"
    else:
        # For regular web pages, use the standard extraction methods
        # Method 1: Trafilatura (best for article content)
        content = extract_with_trafilatura(url)
        extraction_method = "trafilatura"

        # Fallback 1: BeautifulSoup
        if content is None:
            content = extract_with_beautifulsoup(url)
            extraction_method = "beautifulsoup"

        # Fallback 2: Scrapy (not fully implemented)
        if content is None:
            content = extract_with_scrapy(url)
            extraction_method = "scrapy"

        # Fallback 3: If all web extraction methods fail, try PDF extraction
        # This handles cases where the URL doesn't end with .pdf but serves a PDF file
        if content is None:
            logger.info(f"All web extraction methods failed, trying PDF extraction for: {url}")
            content = extract_pdf_from_url(url)
            if content is not None:
                extraction_method = "pdf_extraction"

    if content is None:
        logger.error(f"All extraction methods failed for URL: {url}")
        return []

    try:
        # Validate extracted content
        if not content or len(content.strip()) < 100:
            raise ValueError("Insufficient content extracted")
    except Exception as e:
        logger.error(f"Website scraping failed: {str(e)}")
        return []

    # Create chunks from extracted content
    chunks = create_chunks_from_website(content, url)

    # Add extraction method to each chunk
    for chunk in chunks:
        chunk["extraction_method"] = extraction_method

        # For PDF files, add additional metadata
        if is_pdf_url or extraction_method == "pdf_extraction":
            # Use standard "website" source_type for compatibility with search
            chunk["source_type"] = "website"
            # Add additional metadata to identify it as a PDF
            chunk["file_type"] = "pdf"
            chunk["is_pdf"] = True

            # Extract filename from URL path
            filename = os.path.basename(parsed_url.path)
            if filename:
                chunk["filename"] = filename

    logger.info(f"Extracted {len(chunks)} chunks from {url} using {extraction_method}")
    return chunks
