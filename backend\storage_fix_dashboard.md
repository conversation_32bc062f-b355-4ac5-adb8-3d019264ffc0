# Fix Supabase Storage Issues via Dashboard (No SQL Required)

## Problem
The SQL approach failed with: `ERROR: 42501: must be owner of table objects`
This means you don't have direct SQL permissions on the storage.objects table.

## Solution: Use Supabase Dashboard Instead

### Step 1: Create/Configure Documents Bucket
1. Go to your Supabase Dashboard
2. Click **Storage** in the left sidebar
3. If you don't see a `documents` bucket:
   - Click **"New bucket"**
   - Name: `documents`
   - **✅ Check "Public bucket"** (this is crucial!)
   - Click **"Create bucket"**
4. If the bucket exists but isn't public:
   - Click on the `documents` bucket
   - Click **"Settings"** (gear icon)
   - **✅ Enable "Public bucket"**
   - Click **"Save"**

### Step 2: Create Storage Policies (Dashboard Method)
1. In Supabase Dashboard, go to **Authentication** > **Policies**
2. Find the **"storage.objects"** table section
3. Click **"New Policy"**
4. Create these 4 policies:

#### Policy 1: Allow Uploads
- **Policy name:** `Allow public uploads to documents`
- **Allowed operation:** `INSERT`
- **Target roles:** `public`
- **USING expression:** Leave empty
- **WITH CHECK expression:** `bucket_id = 'documents'`

#### Policy 2: Allow Downloads  
- **Policy name:** `Allow public downloads from documents`
- **Allowed operation:** `SELECT`
- **Target roles:** `public`
- **USING expression:** `bucket_id = 'documents'`
- **WITH CHECK expression:** Leave empty

#### Policy 3: Allow Updates
- **Policy name:** `Allow public updates to documents`
- **Allowed operation:** `UPDATE`
- **Target roles:** `public`
- **USING expression:** `bucket_id = 'documents'`
- **WITH CHECK expression:** `bucket_id = 'documents'`

#### Policy 4: Allow Deletes
- **Policy name:** `Allow public deletes from documents`
- **Allowed operation:** `DELETE`
- **Target roles:** `public`
- **USING expression:** `bucket_id = 'documents'`
- **WITH CHECK expression:** Leave empty

### Step 3: Alternative Simplified Approach
If the above is too complex, try this simpler approach:

1. Go to **Storage** > **Policies** in Supabase Dashboard
2. Click **"New policy"** on the storage.objects table
3. Choose **"Get started quickly"** 
4. Select **"Allow public access"**
5. Choose **"Full customization"**
6. Use this policy:
   ```sql
   -- Allow all operations for documents bucket
   ((bucket_id = 'documents'::text))
   ```

### Step 4: Test the Fix
After setting up the policies, test with our script:
```bash
python test_storage_fix.py
```

## Why This Happens
- Supabase RLS (Row Level Security) protects the storage system
- Direct SQL access to storage.objects requires superuser privileges
- The Dashboard provides a safer interface for policy management
- This is the recommended approach for most users

## Expected Results
After completing these steps:
- ✅ File uploads should work without 400 errors
- ✅ Document content endpoint should work (already working from our earlier fix)
- ✅ No more "must be owner of table objects" errors

## Troubleshooting
If uploads still fail after this:
1. **Double-check bucket is PUBLIC**: Storage > documents bucket > Settings > Public bucket = ON
2. **Verify policies exist**: Authentication > Policies > storage.objects (should show your new policies)
3. **Check bucket permissions**: Some Supabase plans have different permission requirements
4. **Try with anonymous/public access first**: Don't use authentication initially for testing

## Alternative: Disable RLS Temporarily (Advanced)
If you have database admin access, you can temporarily disable RLS:
```sql
-- Only use if you have admin access
ALTER TABLE storage.objects DISABLE ROW LEVEL SECURITY;
```
But this is NOT recommended for production - use the Dashboard method instead. 