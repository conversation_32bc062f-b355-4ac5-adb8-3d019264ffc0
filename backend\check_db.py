from supabase_client import supabase

# Check all content types in the database
query = """
SELECT 
    content_type,
    COUNT(*) as count
FROM document_chunks 
GROUP BY content_type
ORDER BY count DESC
"""

result = supabase.execute_query(query)

print("=== CONTENT TYPES IN DATABASE ===")
for row in result:
    print(f"Content type: {row.get('content_type')} - Count: {row.get('count')}")

# Check chunks with table-related text
query2 = """
SELECT 
    dc.id, 
    dc.text, 
    dc.content_type,
    dc.metadata,
    d.display_name as filename,
    dc.page_number
FROM document_chunks dc
JOIN documents d ON dc.document_id = d.id
WHERE LOWER(dc.text) LIKE '%table%' 
   OR dc.metadata::text LIKE '%table%'
   OR dc.metadata::text LIKE '%visual_content_type%'
ORDER BY dc.created_at DESC
LIMIT 5
"""

result2 = supabase.execute_query(query2)

print(f"\n=== CHUNKS WITH TABLE-RELATED CONTENT ===")
for i, chunk in enumerate(result2[:5], 1):
    print(f"\nChunk {i}:")
    print(f"  Filename: {chunk.get('filename')}")
    print(f"  Page: {chunk.get('page_number')}")
    print(f"  Content type: {chunk.get('content_type')}")
    print(f"  Text preview: {chunk.get('text', '')[:100]}...")
    
    metadata = chunk.get('metadata', {})
    visual_content_type = metadata.get('visual_content_type', 'none')
    print(f"  Visual content type: {visual_content_type}")
    
    if 'table' in metadata.get('visual_content_type', ''):
        print(f"  ✅ This is a table chunk!")
        if 'table_html' in metadata:
            print(f"  Table HTML length: {len(metadata['table_html'])}")
        else:
            print(f"  ❌ Missing table_html") 