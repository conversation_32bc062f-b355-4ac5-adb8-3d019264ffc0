"""
LLM Router Module for RailGPT
Handles different language models with fallback support and optimized routing
"""

import logging
import os
import asyncio
from typing import Dict, List, Any, Optional
import google.generativeai as genai
from dotenv import load_dotenv

load_dotenv()
logger = logging.getLogger(__name__)

# Configure API keys
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if GEMINI_API_KEY:
    genai.configure(api_key=GEMINI_API_KEY)
    logger.info("✅ Gemini API configured for LLM routing")

class LLMRouter:
    """
    Optimized LLM router with fallback support
    """
    
    def __init__(self):
        self.models = {
            "gemini-2.0-flash": {
                "name": "Gemini 2.0 Flash",
                "provider": "google",
                "available": bool(GEMINI_API_KEY),
                "max_tokens": 2048,
                "timeout": 30
            }
        }
        self.default_model = "gemini-2.0-flash"
    
    def is_model_available(self, model_id: str) -> bool:
        """Check if a model is available"""
        return self.models.get(model_id, {}).get("available", False)
    
    def get_available_models(self) -> List[Dict[str, Any]]:
        """Get list of available models"""
        return [
            {
                "id": model_id,
                **config
            }
            for model_id, config in self.models.items()
            if config.get("available", False)
        ]
    
    async def generate_answer(
        self,
        query: str,
        context: str = "",
        system_prompt: Optional[str] = None,
        model_id: str = None,
        extract_format: str = "paragraph",
        max_tokens: int = 2048
    ) -> str:
        """
        Generate answer using the specified model with fallback
        """
        if not model_id:
            model_id = self.default_model
        
        if not self.is_model_available(model_id):
            logger.warning(f"Model {model_id} not available, using {self.default_model}")
            model_id = self.default_model
        
        try:
            # Build the prompt
            if system_prompt:
                full_prompt = f"{system_prompt}\n\n"
            else:
                full_prompt = self._get_default_system_prompt(extract_format)
            
            if context and context.strip():
                full_prompt += f"Context:\n{context}\n\n"
            
            full_prompt += f"Question: {query}\n\nAnswer:"
            
            # Generate response based on model
            if model_id == "gemini-2.0-flash":
                return await self._generate_gemini_response(
                    full_prompt, max_tokens, model_id
                )
            
        except Exception as e:
            logger.error(f"❌ LLM generation failed for {model_id}: {str(e)}")
            return "I encountered an error while generating the response. Please try again."
    
    async def _generate_gemini_response(
        self, 
        prompt: str, 
        max_tokens: int,
        model_id: str
    ) -> str:
        """Generate response using Gemini"""
        try:
            model = genai.GenerativeModel('gemini-2.0-flash-exp')
            
            # Configure generation parameters
            generation_config = genai.types.GenerationConfig(
                max_output_tokens=max_tokens,
                temperature=0.3,  # Lower temperature for more focused responses
                top_p=0.8,
                top_k=40
            )
            
            # Generate response with timeout
            response = await asyncio.wait_for(
                self._async_generate_content(model, prompt, generation_config),
                timeout=30
            )
            
            if response and response.text:
                return response.text.strip()
            else:
                logger.warning("Empty response from Gemini")
                return "I couldn't generate a proper response. Please try rephrasing your question."
                
        except asyncio.TimeoutError:
            logger.error("Gemini API timeout")
            return "The response took too long to generate. Please try again."
        except Exception as e:
            logger.error(f"Gemini generation error: {str(e)}")
            return "I encountered an error while generating the response."
    
    async def _async_generate_content(self, model, prompt, config):
        """Async wrapper for Gemini content generation"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, 
            lambda: model.generate_content(prompt, generation_config=config)
        )
    
    def _get_default_system_prompt(self, extract_format: str) -> str:
        """Get default system prompt based on format"""
        base_prompt = """You are RailGPT, an AI assistant specializing in Indian Railways information.

Guidelines:
- Provide accurate, helpful information based on the given context
- If you cannot find relevant information in the context, say so clearly
- Use clear, concise language
- For railway-specific terms, provide brief explanations if needed
- Focus on practical, actionable information

"""
        
        if extract_format == "bullet":
            base_prompt += "Format your response as clear bullet points.\n\n"
        elif extract_format == "table":
            base_prompt += "When appropriate, format information in table format.\n\n"
        else:
            base_prompt += "Provide your response in clear paragraphs.\n\n"
        
        return base_prompt
    
    async def generate_llm_fallback_answer(
        self,
        query: str,
        model_id: str = None,
        extract_format: str = "paragraph"
    ) -> str:
        """
        Generate fallback answer when no relevant chunks are found
        """
        if not model_id:
            model_id = self.default_model
        
        fallback_prompt = """You are RailGPT, an AI assistant for Indian Railways.

The user has asked a question, but no relevant information was found in the available documents or websites.

Please provide a helpful response that:
1. Acknowledges that specific information wasn't found in the knowledge base
2. Provides general guidance if possible
3. Suggests alternative resources (like IRCTC website, railway inquiry, etc.)
4. Remains focused on railway-related topics

Be honest about limitations while still being helpful.

"""
        
        return await self.generate_answer(
            query=query,
            context="",
            system_prompt=fallback_prompt,
            model_id=model_id,
            extract_format=extract_format
        )

# Global LLM router instance
llm_router = LLMRouter()

# Convenience functions for backward compatibility
async def generate_llm_fallback_answer(
    query: str,
    model_id: str = "gemini-2.0-flash",
    extract_format: str = "paragraph"
) -> str:
    """Generate LLM fallback answer"""
    return await llm_router.generate_llm_fallback_answer(query, model_id, extract_format)

async def generate_llm_answer(
    query: str,
    similar_chunks: List[Dict[str, Any]],
    system_prompt: Optional[str] = None,
    model_id: str = "gemini-2.0-flash",
    extract_format: str = "paragraph"
) -> str:
    """Generate answer from chunks using LLM"""
    
    # Build context from chunks
    context_parts = []
    for i, chunk in enumerate(similar_chunks[:5]):  # Limit to top 5 chunks
        chunk_text = chunk.get("text", "")
        if chunk.get("filename"):
            context_parts.append(f"Source {i+1} ({chunk['filename']}): {chunk_text}")
        elif chunk.get("url"):
            context_parts.append(f"Source {i+1} ({chunk['url']}): {chunk_text}")
        else:
            context_parts.append(f"Source {i+1}: {chunk_text}")
    
    context = "\n\n".join(context_parts)
    
    return await llm_router.generate_answer(
        query=query,
        context=context,
        system_prompt=system_prompt,
        model_id=model_id,
        extract_format=extract_format
    ) 