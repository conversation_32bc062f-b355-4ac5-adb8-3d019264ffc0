import React, { useState, useEffect } from 'react';
import { Document, DocumentExtractionDetails, ExtractionTool } from '../../types/documents';
import PDFViewer from './PDFViewer';
import { Alert, AlertDescription, AlertTitle } from "../../components/ui/alert";
import { Loader2 } from "lucide-react";
import { API_URL } from '../../services/api';

interface DocumentViewModalProps {
  document: Document;
  extractionDetails: DocumentExtractionDetails;
  isOpen: boolean;
  onClose: () => void;
  onReprocess: (document: Document, tool: ExtractionTool) => void;
}

const DocumentViewModal: React.FC<DocumentViewModalProps> = ({
  document,
  extractionDetails,
  isOpen,
  onClose,
  onReprocess,
}) => {
  const [activeTab, setActiveTab] = useState<'content' | 'details' | 'preview'>('content');
  const [selectedTool, setSelectedTool] = useState<ExtractionTool>('PyMuPDF');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPDFViewer, setShowPDFViewer] = useState<boolean>(document.fileType.toLowerCase() === 'pdf');
  const [pdfUrl, setPdfUrl] = useState<string>('');
  
  useEffect(() => {
    // Initialize PDF URL if it's a PDF document
    if (document.fileType.toLowerCase() === 'pdf') {
      // Prefer the actual filename rather than internal ID when building the viewer URL
      const filename = document.filePath
        ? (document.filePath.split('/').pop() || document.filePath)
        : document.name;
      const url = `${API_URL}/api/documents/view/${encodeURIComponent(filename)}`;
      setPdfUrl(url);
      setShowPDFViewer(true);
    } else {
      setShowPDFViewer(false);
    }
    
    // Reset error state when document changes
    setError(null);
  }, [document]);

  if (!isOpen) {
    return null;
  }

  const handleReprocess = async () => {
    setIsProcessing(true);
    setError(null);
    try {
      await onReprocess(document, selectedTool);
      // Success message could be shown here
    } catch (error) {
      console.error('Error reprocessing document:', error);
      setError(error instanceof Error ? error.message : 'An unexpected error occurred during reprocessing');
    } finally {
      setIsProcessing(false);
    }
  };

  const renderQualityIndicator = () => {
    const { qualityScore } = extractionDetails;
    let color;
    if (qualityScore >= 80) color = 'bg-green-500';
    else if (qualityScore >= 60) color = 'bg-green-400';
    else if (qualityScore >= 40) color = 'bg-yellow-500';
    else if (qualityScore >= 20) color = 'bg-orange-500';
    else color = 'bg-red-500';

    return (
      <div className="flex items-center">
        <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2">
          <div className={`h-2.5 rounded-full ${color}`} style={{ width: `${qualityScore}%` }}></div>
        </div>
        <span className="text-sm font-medium">{qualityScore}%</span>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-lg font-semibold">{document.name}</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          <button
            className={`px-4 py-2 font-medium ${
              activeTab === 'content'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('content')}
          >
            Extracted Content
          </button>
          {showPDFViewer && (
            <button
              className={`px-4 py-2 font-medium ${
                activeTab === 'preview'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('preview')}
            >
              PDF Preview
            </button>
          )}
          <button
            className={`px-4 py-2 font-medium ${
              activeTab === 'details'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('details')}
          >
            Processing Details
          </button>
        </div>
        
        {/* Error Display */}
        {error && (
          <div className="p-4">
            <Alert variant="destructive">
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 overflow-auto p-4">
          {activeTab === 'content' ? (
            <div className="prose max-w-none">
              <div className="whitespace-pre-wrap text-sm bg-gray-50 p-4 rounded-md overflow-auto h-[60vh]">
                {document.extractedContent || extractionDetails.extractedContent || 
                (document.chunks && document.chunks.length > 0 ? 
                  document.chunks.map((chunk: any) => chunk.text).join('\n\n') : 
                  'No extracted content available')}
              </div>
            </div>
          ) : activeTab === 'preview' && showPDFViewer ? (
            <div className="h-[60vh]">
              {isProcessing ? (
                <div className="flex justify-center items-center h-full">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                  <span className="ml-2">Loading PDF preview...</span>
                </div>
              ) : (
                <PDFViewer url={pdfUrl} fileName={document.name} />
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-900">Extraction Method</h3>
                <p className="mt-1 text-sm text-gray-600">{extractionDetails.extractionMethod}</p>
              </div>

              <div>
                <h3 className="font-medium text-gray-900">Quality Score</h3>
                <div className="mt-1">{renderQualityIndicator()}</div>
              </div>

              <div>
                <h3 className="font-medium text-gray-900">Processing Time</h3>
                <p className="mt-1 text-sm text-gray-600">
                  {extractionDetails.processingTime} ms ({(extractionDetails.processingTime / 1000).toFixed(2)} seconds)
                </p>
              </div>

              <div>
                <h3 className="font-medium text-gray-900">Chunks Created</h3>
                <p className="mt-1 text-sm text-gray-600">{extractionDetails.chunks}</p>
              </div>

              {extractionDetails.warnings.length > 0 && (
                <div>
                  <h3 className="font-medium text-gray-900">Warnings</h3>
                  <ul className="mt-1 text-sm text-gray-600 list-disc pl-5">
                    {extractionDetails.warnings.map((warning, index) => (
                      <li key={index} className="text-yellow-600">{warning}</li>
                    ))}
                  </ul>
                </div>
              )}

              {extractionDetails.fallbackReason && (
                <div>
                  <h3 className="font-medium text-gray-900">Fallback Reason</h3>
                  <p className="mt-1 text-sm text-red-600">{extractionDetails.fallbackReason}</p>
                </div>
              )}

              <div className="pt-4 border-t border-gray-200">
                <h3 className="font-medium text-gray-900">Retry Extraction</h3>
                <div className="mt-2 flex items-end gap-3">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Extraction Tool
                    </label>
                    <select
                      value={selectedTool}
                      onChange={(e) => setSelectedTool(e.target.value as ExtractionTool)}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={isProcessing}
                    >
                      <option value="PyMuPDF">PyMuPDF</option>
                      <option value="PDFPlumber">PDF Plumber</option>
                      <option value="Tesseract OCR">Tesseract OCR</option>
                      <option value="Textract">AWS Textract</option>
                      <option value="DocX Parser">DocX Parser</option>
                    </select>
                  </div>
                  <button
                    onClick={handleReprocess}
                    disabled={isProcessing}
                    className={`px-4 py-2 rounded-md ${
                      isProcessing
                        ? 'bg-gray-300 cursor-not-allowed'
                        : 'bg-blue-600 text-white hover:bg-blue-700'
                    }`}
                  >
                    {isProcessing ? 'Processing...' : 'Reprocess Document'}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentViewModal;
