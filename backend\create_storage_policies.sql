-- Supabase Storage Policies for IR App
-- Run this in your Supabase SQL Editor to fix storage upload and access issues

-- First, ensure the documents bucket exists and is configured properly
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'documents',
  'documents',
  true,
  52428800,  -- 50MB limit
  ARRAY['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'image/png', 'image/jpeg', 'image/gif']
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 52428800,
  allowed_mime_types = ARRAY['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'image/png', 'image/jpeg', 'image/gif'];

-- Create storage policies for the documents bucket
-- Policy for uploads (INSERT operations)
CREATE POLICY "Allow authenticated uploads to documents bucket"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'documents' AND
  (auth.role() = 'authenticated' OR auth.role() = 'anon')
);

-- Policy for downloads (SELECT operations)  
CREATE POLICY "Allow public downloads from documents bucket"
ON storage.objects
FOR SELECT
USING (bucket_id = 'documents');

-- Policy for updates (UPDATE operations)
CREATE POLICY "Allow authenticated updates to documents bucket"
ON storage.objects
FOR UPDATE
USING (
  bucket_id = 'documents' AND
  (auth.role() = 'authenticated' OR auth.role() = 'anon')
)
WITH CHECK (
  bucket_id = 'documents' AND
  (auth.role() = 'authenticated' OR auth.role() = 'anon')
);

-- Policy for deletions (DELETE operations)
CREATE POLICY "Allow authenticated deletions from documents bucket"
ON storage.objects
FOR DELETE
USING (
  bucket_id = 'documents' AND
  (auth.role() = 'authenticated' OR auth.role() = 'anon')
);

-- Create a more permissive policy for anonymous users if needed
CREATE POLICY "Allow anonymous uploads to documents bucket"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'documents'
);

-- Also create the doc-images bucket for visual content
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'doc-images',
  'doc-images', 
  true,
  10485760,  -- 10MB limit for images
  ARRAY['image/png', 'image/jpeg', 'image/gif', 'image/webp', 'text/html']
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 10485760,
  allowed_mime_types = ARRAY['image/png', 'image/jpeg', 'image/gif', 'image/webp', 'text/html'];

-- Policies for doc-images bucket
CREATE POLICY "Allow uploads to doc-images bucket"
ON storage.objects
FOR INSERT
WITH CHECK (bucket_id = 'doc-images');

CREATE POLICY "Allow public downloads from doc-images bucket"
ON storage.objects
FOR SELECT
USING (bucket_id = 'doc-images');

-- Enable RLS on storage.objects if not already enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Verify the policies (run these SELECT statements to check)
-- SELECT * FROM storage.objects WHERE bucket_id = 'documents' LIMIT 5;
-- SELECT * FROM storage.buckets WHERE id IN ('documents', 'doc-images');

-- Test upload function (optional - for debugging)
/*
SELECT storage.upload(
  'documents',
  'test-file.txt',
  'Hello World Test Content',
  'text/plain'
);
*/ 