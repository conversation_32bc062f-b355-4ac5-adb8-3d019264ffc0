"""
Cache Manager for RailGPT
Handles intelligent caching of search results, embeddings, and responses
"""

import logging
import json
import hashlib
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from functools import lru_cache

logger = logging.getLogger(__name__)

class CacheManager:
    """
    Intelligent cache manager for RailGPT
    """
    
    def __init__(self):
        self.search_cache = {}
        self.embedding_cache = {}
        self.response_cache = {}
        
        # Cache TTL settings
        self.cache_ttl = {
            'search': timedelta(hours=2),
            'embedding': timedelta(days=1),
            'response': timedelta(hours=1)
        }
        
        # Cache size limits
        self.max_cache_sizes = {
            'search': 1000,
            'embedding': 5000,
            'response': 500
        }
    
    def _generate_cache_key(self, data: Any) -> str:
        """Generate consistent cache key from data"""
        if isinstance(data, str):
            return hashlib.md5(data.encode()).hexdigest()
        else:
            json_str = json.dumps(data, sort_keys=True)
            return hashlib.md5(json_str.encode()).hexdigest()
    
    def _is_cache_valid(self, timestamp: datetime, cache_type: str) -> bool:
        """Check if cache entry is still valid"""
        ttl = self.cache_ttl.get(cache_type, timedelta(hours=1))
        return datetime.now() - timestamp < ttl
    
    def _cleanup_cache(self, cache_dict: Dict, cache_type: str):
        """Clean up expired cache entries"""
        current_time = datetime.now()
        expired_keys = []
        
        for key, (data, timestamp) in cache_dict.items():
            if not self._is_cache_valid(timestamp, cache_type):
                expired_keys.append(key)
        
        for key in expired_keys:
            del cache_dict[key]
        
        # Enforce size limits
        max_size = self.max_cache_sizes.get(cache_type, 1000)
        if len(cache_dict) > max_size:
            # Remove oldest entries
            sorted_items = sorted(
                cache_dict.items(), 
                key=lambda x: x[1][1]  # Sort by timestamp
            )
            
            for key, _ in sorted_items[:-max_size]:
                del cache_dict[key]
    
    # Search result caching
    def get_search_cache(self, query: str, search_type: str) -> Optional[List[Dict[str, Any]]]:
        """Get cached search results"""
        cache_key = self._generate_cache_key(f"{query}_{search_type}")
        
        if cache_key in self.search_cache:
            data, timestamp = self.search_cache[cache_key]
            if self._is_cache_valid(timestamp, 'search'):
                logger.info(f"🔄 Cache hit for {search_type} search: {query[:50]}...")
                return data
            else:
                # Remove expired entry
                del self.search_cache[cache_key]
        
        return None
    
    def set_search_cache(self, query: str, search_type: str, results: List[Dict[str, Any]]):
        """Cache search results"""
        cache_key = self._generate_cache_key(f"{query}_{search_type}")
        self.search_cache[cache_key] = (results, datetime.now())
        
        # Cleanup if needed
        if len(self.search_cache) % 100 == 0:  # Cleanup every 100 entries
            self._cleanup_cache(self.search_cache, 'search')
    
    # Embedding caching
    def get_embedding_cache(self, text: str, model_id: str) -> Optional[List[float]]:
        """Get cached embedding"""
        cache_key = self._generate_cache_key(f"{text}_{model_id}")
        
        if cache_key in self.embedding_cache:
            data, timestamp = self.embedding_cache[cache_key]
            if self._is_cache_valid(timestamp, 'embedding'):
                return data
            else:
                del self.embedding_cache[cache_key]
        
        return None
    
    def set_embedding_cache(self, text: str, model_id: str, embedding: List[float]):
        """Cache embedding"""
        cache_key = self._generate_cache_key(f"{text}_{model_id}")
        self.embedding_cache[cache_key] = (embedding, datetime.now())
        
        # Cleanup if needed
        if len(self.embedding_cache) % 200 == 0:
            self._cleanup_cache(self.embedding_cache, 'embedding')
    
    # Response caching
    def get_response_cache(self, query: str, context_hash: str) -> Optional[Dict[str, Any]]:
        """Get cached response"""
        cache_key = self._generate_cache_key(f"{query}_{context_hash}")
        
        if cache_key in self.response_cache:
            data, timestamp = self.response_cache[cache_key]
            if self._is_cache_valid(timestamp, 'response'):
                logger.info(f"🔄 Cache hit for response: {query[:50]}...")
                return data
            else:
                del self.response_cache[cache_key]
        
        return None
    
    def set_response_cache(self, query: str, context_hash: str, response: Dict[str, Any]):
        """Cache response"""
        cache_key = self._generate_cache_key(f"{query}_{context_hash}")
        self.response_cache[cache_key] = (response, datetime.now())
        
        # Cleanup if needed
        if len(self.response_cache) % 50 == 0:
            self._cleanup_cache(self.response_cache, 'response')
    
    # Cache management
    def clear_cache(self, cache_type: Optional[str] = None):
        """Clear cache"""
        if cache_type == 'search' or cache_type is None:
            self.search_cache.clear()
        if cache_type == 'embedding' or cache_type is None:
            self.embedding_cache.clear()
        if cache_type == 'response' or cache_type is None:
            self.response_cache.clear()
        
        logger.info(f"🧹 Cleared {cache_type or 'all'} cache(s)")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            'search_cache': {
                'size': len(self.search_cache),
                'max_size': self.max_cache_sizes['search']
            },
            'embedding_cache': {
                'size': len(self.embedding_cache),
                'max_size': self.max_cache_sizes['embedding']
            },
            'response_cache': {
                'size': len(self.response_cache),
                'max_size': self.max_cache_sizes['response']
            }
        }
    
    def cleanup_all_caches(self):
        """Clean up all caches"""
        self._cleanup_cache(self.search_cache, 'search')
        self._cleanup_cache(self.embedding_cache, 'embedding')
        self._cleanup_cache(self.response_cache, 'response')
        logger.info("🧹 Performed full cache cleanup")

# Global cache manager instance
cache_manager = CacheManager() 