#!/usr/bin/env python3
"""
RailGPT Backend Server - Real Implementation
Connects to Supabase database and implements proper query logic
"""

import os
import logging
import json
import requests
from typing import List, Dict, Any, Optional
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Supabase configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

if not SUPABASE_URL or not SUPABASE_KEY:
    logger.error("SUPABASE_URL and SUPABASE_KEY must be set in environment variables")
    raise ValueError("Missing Supabase configuration")

# Supabase headers
SUPABASE_HEADERS = {
    "apikey": SUPABASE_KEY,
    "Authorization": f"Bearer {SUPABASE_KEY}",
    "Content-Type": "application/json",
    "Prefer": "return=representation"
}

# Create FastAPI app
app = FastAPI(title="RailGPT Backend - Real Implementation")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class QueryRequest(BaseModel):
    query: str
    model: str = "gemini-2.0-flash"
    include_documents: bool = True
    include_websites: bool = True
    fallback_enabled: bool = True
    extract_format: str = "paragraph"
    use_hybrid_search: bool = True

class Source(BaseModel):
    source_type: str
    filename: Optional[str] = None
    name: Optional[str] = None
    page: Optional[int] = None
    url: Optional[str] = None
    link: Optional[str] = None
    text: Optional[str] = None

class QueryResponse(BaseModel):
    answer: str
    document_answer: Optional[str] = None
    website_answer: Optional[str] = None
    sources: List[Source] = []
    document_sources: List[Source] = []
    website_sources: List[Source] = []
    llm_model: str = "gemini-2.0-flash"
    llm_fallback: bool = False

# Utility functions
def search_documents_in_supabase(query: str, limit: int = 10) -> List[Dict[str, Any]]:
    """Search for documents in Supabase using multiple search strategies"""
    try:
        # Strategy 1: Direct text search with ilike (case insensitive)
        url = f"{SUPABASE_URL}/rest/v1/document_chunks"
        params = {
            "select": "id,document_id,chunk_index,page_number,text,documents(display_name,file_path)",
            "text": f"ilike.*{query}*",
            "limit": limit
        }

        response = requests.get(url, headers=SUPABASE_HEADERS, params=params, timeout=10)
        results = []

        if response.status_code == 200:
            chunks = response.json()
            logger.info(f"Direct search found {len(chunks)} document chunks for query: {query}")

            # Format results from direct search
            for chunk in chunks:
                doc_info = chunk.get('documents', {})
                result = {
                    'id': chunk.get('id'),
                    'text': chunk.get('text', ''),
                    'page': chunk.get('page_number', 1),
                    'filename': doc_info.get('display_name', 'Unknown Document'),
                    'url': doc_info.get('file_path', ''),
                    'source_type': 'document',
                    'similarity': 0.8  # High similarity for direct matches
                }
                results.append(result)

        # Strategy 2: If no results, try broader search by searching in document names
        if not results:
            logger.info(f"No direct text matches, trying document name search for: {query}")
            params = {
                "select": "id,document_id,chunk_index,page_number,text,documents(display_name,file_path)",
                "documents.display_name": f"ilike.*{query}*",
                "limit": limit
            }

            response = requests.get(url, headers=SUPABASE_HEADERS, params=params, timeout=10)

            if response.status_code == 200:
                chunks = response.json()
                logger.info(f"Document name search found {len(chunks)} chunks")

                for chunk in chunks:
                    doc_info = chunk.get('documents', {})
                    result = {
                        'id': chunk.get('id'),
                        'text': chunk.get('text', ''),
                        'page': chunk.get('page_number', 1),
                        'filename': doc_info.get('display_name', 'Unknown Document'),
                        'url': doc_info.get('file_path', ''),
                        'source_type': 'document',
                        'similarity': 0.6  # Lower similarity for name matches
                    }
                    results.append(result)

        # Strategy 3: If still no results, try partial keyword matching
        if not results:
            logger.info(f"Trying partial keyword search for: {query}")
            # Split query into keywords and search for each
            keywords = query.lower().split()
            for keyword in keywords:
                if len(keyword) > 2:  # Only search for keywords longer than 2 chars
                    params = {
                        "select": "id,document_id,chunk_index,page_number,text,documents(display_name,file_path)",
                        "text": f"ilike.*{keyword}*",
                        "limit": 5
                    }

                    response = requests.get(url, headers=SUPABASE_HEADERS, params=params, timeout=10)

                    if response.status_code == 200:
                        chunks = response.json()
                        logger.info(f"Keyword '{keyword}' found {len(chunks)} chunks")

                        for chunk in chunks:
                            doc_info = chunk.get('documents', {})
                            result = {
                                'id': chunk.get('id'),
                                'text': chunk.get('text', ''),
                                'page': chunk.get('page_number', 1),
                                'filename': doc_info.get('display_name', 'Unknown Document'),
                                'url': doc_info.get('file_path', ''),
                                'source_type': 'document',
                                'similarity': 0.5  # Lower similarity for partial matches
                            }
                            results.append(result)

                        if results:  # Stop after finding results with first keyword
                            break

        # Remove duplicates and limit results
        seen_ids = set()
        unique_results = []
        for result in results:
            if result['id'] not in seen_ids:
                seen_ids.add(result['id'])
                unique_results.append(result)
                if len(unique_results) >= limit:
                    break

        logger.info(f"Final document search returned {len(unique_results)} unique results")
        return unique_results

    except Exception as e:
        logger.error(f"Error searching documents: {str(e)}")
        return []

def search_websites_in_supabase(query: str, limit: int = 10) -> List[Dict[str, Any]]:
    """Search for websites in Supabase using multiple search strategies"""
    try:
        # Strategy 1: Direct text search in website chunks
        url = f"{SUPABASE_URL}/rest/v1/website_chunks"
        params = {
            "select": "id,website_id,chunk_index,text,websites(title,url,domain)",
            "text": f"ilike.*{query}*",
            "limit": limit
        }

        response = requests.get(url, headers=SUPABASE_HEADERS, params=params, timeout=10)
        results = []

        if response.status_code == 200:
            chunks = response.json()
            logger.info(f"Direct website search found {len(chunks)} chunks for query: {query}")

            # Format results from direct search
            for chunk in chunks:
                website_info = chunk.get('websites', {})
                result = {
                    'id': chunk.get('id'),
                    'text': chunk.get('text', ''),
                    'url': website_info.get('url', ''),
                    'title': website_info.get('title', 'Unknown Website'),
                    'domain': website_info.get('domain', ''),
                    'source_type': 'website',
                    'similarity': 0.8  # High similarity for direct matches
                }
                results.append(result)

        # Strategy 2: If no results, try searching by keywords
        if not results:
            logger.info(f"Trying keyword search in websites for: {query}")
            keywords = query.lower().split()
            for keyword in keywords:
                if len(keyword) > 2:  # Only search for keywords longer than 2 chars
                    params = {
                        "select": "id,website_id,chunk_index,text,websites(title,url,domain)",
                        "text": f"ilike.*{keyword}*",
                        "limit": 5
                    }

                    response = requests.get(url, headers=SUPABASE_HEADERS, params=params, timeout=10)

                    if response.status_code == 200:
                        chunks = response.json()
                        logger.info(f"Website keyword '{keyword}' found {len(chunks)} chunks")

                        for chunk in chunks:
                            website_info = chunk.get('websites', {})
                            result = {
                                'id': chunk.get('id'),
                                'text': chunk.get('text', ''),
                                'url': website_info.get('url', ''),
                                'title': website_info.get('title', 'Unknown Website'),
                                'domain': website_info.get('domain', ''),
                                'source_type': 'website',
                                'similarity': 0.6  # Lower similarity for keyword matches
                            }
                            results.append(result)

                        if results:  # Stop after finding results with first keyword
                            break

        # Remove duplicates and limit results
        seen_ids = set()
        unique_results = []
        for result in results:
            if result['id'] not in seen_ids:
                seen_ids.add(result['id'])
                unique_results.append(result)
                if len(unique_results) >= limit:
                    break

        logger.info(f"Final website search returned {len(unique_results)} unique results")
        return unique_results

    except Exception as e:
        logger.error(f"Error searching websites: {str(e)}")
        return []

def generate_answer_with_gemini(query: str, context: str, model: str = "gemini-2.0-flash") -> str:
    """Generate answer using Gemini API"""
    try:
        if not GEMINI_API_KEY:
            return "Gemini API key not configured. Please set GEMINI_API_KEY environment variable."
        
        import google.generativeai as genai
        genai.configure(api_key=GEMINI_API_KEY)
        
        # Use the specified model or fallback to gemini-1.5-flash
        model_name = "gemini-1.5-flash" if model == "gemini-2.0-flash" else "gemini-1.5-flash"
        model_instance = genai.GenerativeModel(model_name)
        
        prompt = f"""You are RailGPT, an AI assistant specializing in Indian Railways. 
        Answer the user's question based on the provided context from railway documents and websites.
        
        Context from railway sources:
        {context}
        
        User Question: {query}
        
        Provide a comprehensive answer based on the context. If the context doesn't contain enough information, 
        mention that and provide general railway knowledge."""
        
        response = model_instance.generate_content(prompt)
        return response.text
        
    except Exception as e:
        logger.error(f"Error generating answer with Gemini: {str(e)}")
        return f"Error generating answer: {str(e)}"

def format_sources(chunks: List[Dict[str, Any]], source_type: str) -> List[Source]:
    """Format chunks into Source objects"""
    sources = []
    for chunk in chunks:
        if source_type == "document":
            source = Source(
                source_type="document",
                filename=chunk.get('filename', 'Unknown Document'),
                name=chunk.get('filename', 'Unknown Document'),
                page=chunk.get('page', 1),
                url=chunk.get('url', ''),
                link=chunk.get('url', ''),
                text=chunk.get('text', '')  # Include the text content
            )
        else:  # website
            source = Source(
                source_type="website",
                name=chunk.get('title', 'Unknown Website'),
                url=chunk.get('url', ''),
                link=chunk.get('url', ''),
                text=chunk.get('text', '')  # Include the text content
            )
        sources.append(source)

    return sources

# API endpoints
@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "RailGPT API is running", "status": "healthy"}

@app.get("/health")
async def health():
    """Health check endpoint"""
    return {"status": "healthy", "service": "railgpt-backend"}

@app.post("/api/query")
async def query_documents(request: QueryRequest) -> QueryResponse:
    """Process user query and return answer with sources"""
    try:
        logger.info(f"Processing query: {request.query}")
        
        # Initialize variables
        document_chunks = []
        website_chunks = []
        document_answer = None
        website_answer = None
        combined_answer = ""
        
        # STEP 1: Search documents if enabled
        if request.include_documents:
            logger.info("Searching documents...")
            document_chunks = search_documents_in_supabase(request.query, limit=10)
            
            if document_chunks:
                # Generate document answer
                doc_context = "\n\n".join([f"Document: {chunk['filename']}, Page: {chunk['page']}\n{chunk['text']}" 
                                         for chunk in document_chunks[:5]])
                document_answer = generate_answer_with_gemini(request.query, doc_context, request.model)
                logger.info(f"Generated document answer from {len(document_chunks)} chunks")
        
        # STEP 2: Search websites if enabled
        if request.include_websites:
            logger.info("Searching websites...")
            website_chunks = search_websites_in_supabase(request.query, limit=10)
            
            if website_chunks:
                # Generate website answer
                web_context = "\n\n".join([f"Website: {chunk['title']} ({chunk['url']})\n{chunk['text']}" 
                                         for chunk in website_chunks[:5]])
                website_answer = generate_answer_with_gemini(request.query, web_context, request.model)
                logger.info(f"Generated website answer from {len(website_chunks)} chunks")
        
        # STEP 3: Apply priority logic
        if document_chunks and website_chunks:
            # Both sources available - prioritize documents
            combined_answer = document_answer or "Answer generated from document and website sources."
            logger.info("Both document and website sources found - showing both")
            
        elif document_chunks and not website_chunks:
            # Only documents available
            combined_answer = document_answer or "Answer generated from document sources."
            website_answer = None
            logger.info("Only document sources found")
            
        elif website_chunks and not document_chunks:
            # Only websites available
            combined_answer = website_answer or "Answer generated from website sources."
            document_answer = None
            logger.info("Only website sources found")
            
        elif request.fallback_enabled:
            # No sources found - use LLM fallback
            combined_answer = generate_answer_with_gemini(
                request.query, 
                "No specific railway documents or websites found. Please provide a general answer about railways.",
                request.model
            )
            document_answer = None
            website_answer = None
            logger.info("No sources found - using LLM fallback")
            
        else:
            # No sources and fallback disabled
            combined_answer = "No relevant information found in uploaded documents or extracted websites."
            logger.info("No sources found and fallback disabled")
        
        # Format sources
        document_sources = format_sources(document_chunks, "document")
        website_sources = format_sources(website_chunks, "website")
        all_sources = document_sources + website_sources
        
        # Create response
        response = QueryResponse(
            answer=combined_answer,
            document_answer=document_answer,
            website_answer=website_answer,
            sources=all_sources,
            document_sources=document_sources,
            website_sources=website_sources,
            llm_model=request.model,
            llm_fallback=not (document_chunks or website_chunks)
        )
        
        logger.info(f"Query processed successfully - {len(document_sources)} doc sources, {len(website_sources)} web sources")
        return response
        
    except Exception as e:
        logger.error(f"Error processing query: {str(e)}")
        return QueryResponse(
            answer=f"Sorry, an error occurred while processing your query: {str(e)}",
            sources=[],
            document_sources=[],
            website_sources=[],
            llm_model=request.model,
            llm_fallback=True
        )

@app.get("/api/documents")
async def get_documents():
    """Get list of uploaded documents"""
    try:
        url = f"{SUPABASE_URL}/rest/v1/documents"
        params = {"select": "id,display_name,file_type,created_at", "limit": 100}
        
        response = requests.get(url, headers=SUPABASE_HEADERS, params=params, timeout=10)
        
        if response.status_code == 200:
            documents = response.json()
            return {
                "total": len(documents),
                "documents": [{"id": doc["id"], "title": doc["display_name"], "type": doc["file_type"]} for doc in documents]
            }
        else:
            return {"total": 0, "documents": []}
            
    except Exception as e:
        logger.error(f"Error fetching documents: {str(e)}")
        return {"total": 0, "documents": []}

@app.get("/api/websites")
async def get_websites():
    """Get list of extracted websites"""
    try:
        url = f"{SUPABASE_URL}/rest/v1/websites"
        params = {"select": "id,title,url,created_at", "limit": 100}
        
        response = requests.get(url, headers=SUPABASE_HEADERS, params=params, timeout=10)
        
        if response.status_code == 200:
            websites = response.json()
            return {
                "total": len(websites),
                "websites": [{"id": site["id"], "title": site["title"], "url": site["url"]} for site in websites]
            }
        else:
            return {"total": 0, "websites": []}
            
    except Exception as e:
        logger.error(f"Error fetching websites: {str(e)}")
        return {"total": 0, "websites": []}

@app.post("/api/upload-document")
async def upload_document_placeholder():
    """Placeholder for document upload"""
    return {
        "message": "Document upload functionality will be available in the next update",
        "status": "coming_soon"
    }

@app.post("/api/add-website")
async def add_website_placeholder(request: dict):
    """Placeholder for website addition"""
    url = request.get("url", "unknown")
    return {
        "message": f"Website {url} will be processed in the next update",
        "status": "coming_soon"
    }

if __name__ == "__main__":
    import uvicorn
    port = int(os.getenv("PORT", 8000))
    uvicorn.run(app, host="0.0.0.0", port=port)
