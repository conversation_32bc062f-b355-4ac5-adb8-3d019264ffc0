{"displayName": "RailGPT Backend High Error Rate", "documentation": {"content": "This alert fires when the RailGPT backend has a high error rate (>5% of requests failing)", "mimeType": "text/markdown"}, "conditions": [{"displayName": "High Error Rate", "conditionThreshold": {"filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"railgpt-backend\" AND metric.type=\"run.googleapis.com/request_count\" AND metric.labels.response_code_class!=\"2xx\"", "comparison": "COMPARISON_GREATER_THAN", "thresholdValue": 0.05, "duration": "300s", "aggregations": [{"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM"}]}}], "combiner": "OR", "enabled": true, "notificationChannels": [], "alertStrategy": {"autoClose": "1800s"}}