import os
import re
import logging
import numpy as np
import shutil
import time
import uuid
import json
import random
from typing import List, Dict, Any, Optional, Tuple
import fitz  # PyMuPDF
from fastapi import FastAPI, HTTPException, Depends, Request, BackgroundTasks, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse, RedirectResponse
from pydantic import BaseModel, Field
from dotenv import load_dotenv
from fastapi import Query
try:
    from fastapi import run_in_threadpool
except ImportError:
    # For newer versions of FastAPI, run_in_threadpool is in fastapi.concurrency
    try:
        from fastapi.concurrency import run_in_threadpool
    except ImportError:
        # Fallback for even newer versions or alternative implementations
        import asyncio
        from concurrent.futures import ThreadPoolExecutor
        import functools
        
        # Create our own run_in_threadpool function
        _thread_pool = ThreadPoolExecutor()
        
        async def run_in_threadpool(func, *args, **kwargs):
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(_thread_pool, functools.partial(func, *args, **kwargs))
from uuid import uuid4
from datetime import datetime

# Load environment variables FIRST before importing custom modules
load_dotenv()

# Import custom modules
from website_scraper import extract_website_text
from document_extractor import extract_document, extract_document_with_visual_content
from vector_db import vector_db  # Import the vector database
import llm_router  # Import the new LLM router module
from feedback import FeedbackData, send_feedback_email, get_feedback_emails, update_feedback_emails, FeedbackEmailConfig  # Import feedback module
from supabase_client import supabase  # Import Supabase client
from config import config  # Import secure configuration
import vector_search  # Import standardized vector search
from category_management import router as category_router  # Import category management

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Direct document search functions
def search_documents_by_title(query: str, limit: int = 5):
    """Search for documents by title."""
    logger.info(f"Searching for documents with title containing '{query}'...")

    # Prepare the query
    search_query = f"""
    SELECT
        d.id,
        d.display_name,
        d.file_path,
        d.file_type,
        d.created_at,
        d.updated_at
    FROM
        documents d
    WHERE
        LOWER(d.display_name) LIKE LOWER('%{query}%')
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(search_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching documents by title: {result['error']}")
            return []

        logger.info(f"Found {len(result)} documents matching '{query}'")
        return result
    except Exception as e:
        logger.error(f"Error searching documents by title: {str(e)}")
        return []

def get_document_chunks(document_id: str, limit: int = 10):
    """Get chunks for a specific document."""
    logger.info(f"Getting chunks for document {document_id}...")

    # Prepare the query
    chunks_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        d.display_name as filename,
        d.file_path as url,
        'document' as source_type
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        dc.document_id = '{document_id}'
    ORDER BY
        dc.page_number, dc.chunk_index
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(chunks_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error getting document chunks: {result['error']}")
            return []

        logger.info(f"Found {len(result)} chunks for document {document_id}")
        return result
    except Exception as e:
        logger.error(f"Error getting document chunks: {str(e)}")
        return []

def search_documents_by_content(query: str, limit: int = 10):
    """Search for documents by content using text search."""
    logger.info(f"Searching for documents with content containing '{query}'...")

    # Extract key terms for better search
    import re
    from string import punctuation

    # Check if this is an acronym search (e.g., "full form of ACP")
    acronym_match = re.search(r'(?:full\s+form\s+of|what\s+(?:does|is|are)\s+the\s+full\s+form\s+of|what\s+(?:does|is|are)\s+)\s*([A-Z]{2,})', query, re.IGNORECASE)

    if acronym_match:
        # This is an acronym search, extract the acronym
        acronym = acronym_match.group(1).upper()
        logger.info(f"Detected acronym search for: {acronym}")

        # Special handling for acronym searches
        return search_for_acronym(acronym, limit)

    # Clean the query and extract key terms
    clean_query = re.sub(r'[' + punctuation + ']', ' ', query.lower())
    words = clean_query.split()
    stop_words = {'what', 'is', 'the', 'of', 'in', 'on', 'at', 'and', 'or', 'for', 'to', 'a', 'an', 'by', 'with', 'about'}
    key_terms = [word for word in words if word not in stop_words and len(word) > 2]

    # If no key terms found, use the original query
    if not key_terms:
        key_terms = [query]

    logger.info(f"Extracted key terms for content search: {key_terms}")

    # Create a tsquery string with OR operators between terms for broader matches
    ts_query_terms = " | ".join([term.replace("'", "''") for term in key_terms])

    # Sanitize the full query for logging
    sanitized_full_query = query.replace("'", "''")

    # Prepare the query with both exact phrase matching and key term matching
    # Join with documents table to get proper document information
    search_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        COALESCE(d.display_name, d.file_name, d.name, 'Unknown Document') as filename,
        d.file_path as url,
        'document' as source_type,
        GREATEST(
            ts_rank(to_tsvector('english', dc.text), to_tsquery('english', '{ts_query_terms}')),
            ts_rank(to_tsvector('english', dc.text), plainto_tsquery('english', '{sanitized_full_query}'))
        ) AS similarity
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        to_tsvector('english', dc.text) @@ to_tsquery('english', '{ts_query_terms}')
        OR to_tsvector('english', dc.text) @@ plainto_tsquery('english', '{sanitized_full_query}')
        OR dc.text ILIKE '%{sanitized_full_query}%'
    ORDER BY
        similarity DESC
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(search_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching documents by content: {result['error']}")
            return []

        logger.info(f"Found {len(result)} documents matching '{query}'")
        return result
    except Exception as e:
        logger.error(f"Error searching documents by content: {str(e)}")
        return []

def search_for_acronym(acronym: str, limit: int = 5):
    """Special search function for acronyms."""
    logger.info(f"Performing specialized acronym search for: {acronym}")

    # Sanitize the acronym
    sanitized_acronym = acronym.replace("'", "''")

    # Pattern 1: Look for exact acronym followed by words in parentheses (e.g., "ACP (Alarm Chain Pulling)")
    # Pattern 2: Look for exact acronym followed by "stands for" or "means" or "is"
    # Pattern 3: Look for exact acronym followed by a dash or colon and then words
    # Pattern 4: Look for the words "full form" near the acronym
    search_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        d.display_name as filename,
        d.file_path as url,
        'document' as source_type,
        1.0 AS similarity
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        dc.text ~* '\\b{sanitized_acronym}\\s*\\([^)]+\\)'
        OR dc.text ~* '\\b{sanitized_acronym}\\s+(stands\\s+for|means|is)\\b'
        OR dc.text ~* '\\b{sanitized_acronym}\\s*[-:]'
        OR (dc.text ~* '\\b{sanitized_acronym}\\b' AND dc.text ~* 'full\\s+form')
        OR dc.text ~* '\\b{sanitized_acronym}\\b.{{0,30}}(stands\\s+for|means|refers\\s+to|is)'
    ORDER BY
        CASE
            WHEN dc.text ~* '\\b{sanitized_acronym}\\s*\\([^)]+\\)' THEN 1
            WHEN dc.text ~* 'full\\s+form.{{0,20}}\\b{sanitized_acronym}\\b' THEN 2
            WHEN dc.text ~* '\\b{sanitized_acronym}\\b.{{0,20}}full\\s+form' THEN 3
            ELSE 4
        END
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(search_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching for acronym: {result['error']}")
            return []

        logger.info(f"Found {len(result)} document chunks matching acronym '{acronym}'")

        # If no results found with the specialized search, try a more general search
        if not result:
            logger.info(f"No specialized matches for acronym '{acronym}', trying general search")

            # Fallback to a more general search
            general_query = f"""
            SELECT
                dc.id,
                dc.document_id,
                dc.chunk_index,
                dc.page_number,
                dc.text,
                d.display_name as filename,
                d.file_path as url,
                'document' as source_type,
                1.0 AS similarity
            FROM
                document_chunks dc
            JOIN
                documents d ON dc.document_id = d.id
            WHERE
                dc.text ~* '\\b{sanitized_acronym}\\b'
            LIMIT {limit}
            """

            result = supabase.execute_query(general_query)

            if isinstance(result, dict) and "error" in result:
                logger.error(f"Error with general acronym search: {result['error']}")
                return []

            logger.info(f"Found {len(result)} document chunks with general acronym search")

        return result
    except Exception as e:
        logger.error(f"Error searching for acronym: {str(e)}")
        return []

def text_based_document_search(query: str, top_k: int = 10):
    """
    Text-based document search using keyword matching with relevance scoring.
    Works with in-memory DOCUMENT_CHUNKS without requiring embeddings.
    """
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Using text-based document search for: '{query}'")

        # Extract key terms from query
        query_lower = query.lower()
        query_words = set(query_lower.split())

        # Railway domain keywords for relevance boost
        railway_keywords = {
            'railway', 'train', 'rail', 'station', 'track', 'locomotive', 'coach',
            'signal', 'platform', 'passenger', 'freight', 'engine', 'diesel',
            'fsds', 'monitoring', 'system', 'safety', 'maintenance', 'inspection'
        }

        scored_chunks = []

        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'document':
                continue

            chunk_text = chunk.get('text', '').lower()
            if not chunk_text:
                continue

            # Calculate relevance score
            score = 0

            # 1. Exact phrase match (highest priority)
            if query_lower in chunk_text:
                score += 2.0

            # 2. Keyword matching (must have multiple matches for general terms)
            chunk_words = set(chunk_text.split())
            common_words = query_words & chunk_words
            if common_words:
                # Require more matches for shorter queries
                min_matches = max(1, len(query_words) // 2)
                if len(common_words) >= min_matches:
                    score += len(common_words) * 0.5

            # 3. Railway domain relevance (strong boost)
            railway_matches = railway_keywords & chunk_words
            if railway_matches:
                score += len(railway_matches) * 0.8

            # 4. Special boost for specific technical terms
            if 'fsds' in chunk_text:
                score += 1.5
            if 'monitoring' in chunk_text and 'system' in chunk_text:
                score += 1.0

            # 5. Penalty for very generic matches
            generic_terms = {'the', 'and', 'or', 'in', 'on', 'at', 'to', 'a', 'an', 'is', 'are'}
            if query_words.issubset(generic_terms):
                score *= 0.1  # Heavy penalty for generic queries

            # Only include chunks with meaningful relevance (increased threshold)
            if score >= 0.8:  # Much higher threshold
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = score
                scored_chunks.append(chunk_copy)

        # Sort by relevance score
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)

        result = scored_chunks[:top_k]
        logger.info(f"Text-based document search found {len(result)} relevant chunks (threshold: 0.8)")

        return result

    except Exception as e:
        logger.error(f"Error in text-based document search: {str(e)}")
        return []

def text_based_website_search(query: str, top_k: int = 10):
    """
    Text-based website search using keyword matching with relevance scoring.
    Works with in-memory DOCUMENT_CHUNKS without requiring embeddings.
    """
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Using text-based website search for: '{query}'")

        # Extract key terms from query
        query_lower = query.lower()
        query_words = set(query_lower.split())

        # Transport/railway domain keywords for relevance boost
        transport_keywords = {
            'railway', 'train', 'rail', 'station', 'transport', 'transportation',
            'public', 'safety', 'travel', 'passenger', 'metro', 'services'
        }

        scored_chunks = []

        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'website':
                continue

            chunk_text = chunk.get('text', '').lower()
            if not chunk_text:
                continue

            # Calculate relevance score
            score = 0

            # 1. Exact phrase match (highest priority)
            if query_lower in chunk_text:
                score += 2.0

            # 2. Keyword matching (must have multiple matches for general terms)
            chunk_words = set(chunk_text.split())
            common_words = query_words & chunk_words
            if common_words:
                # Require more matches for shorter queries
                min_matches = max(1, len(query_words) // 2)
                if len(common_words) >= min_matches:
                    score += len(common_words) * 0.5

            # 3. Transport domain relevance (strong boost)
            transport_matches = transport_keywords & chunk_words
            if transport_matches:
                score += len(transport_matches) * 0.8

            # 4. Special boost for transport safety
            if 'transportation' in chunk_text and 'safety' in chunk_text:
                score += 1.0
            if 'public' in chunk_text and ('transport' in chunk_text or 'railway' in chunk_text):
                score += 1.0

            # 5. Penalty for very generic matches
            generic_terms = {'the', 'and', 'or', 'in', 'on', 'at', 'to', 'a', 'an', 'is', 'are'}
            if query_words.issubset(generic_terms):
                score *= 0.1  # Heavy penalty for generic queries

            # Only include chunks with meaningful relevance (increased threshold)
            if score >= 0.8:  # Much higher threshold
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = score
                scored_chunks.append(chunk_copy)

        # Sort by relevance score
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)

        result = scored_chunks[:top_k]
        logger.info(f"Text-based website search found {len(result)} relevant chunks (threshold: 0.8)")

        return result

    except Exception as e:
        logger.error(f"Error in text-based website search: {str(e)}")
        return []


def search_documents_in_supabase(query: str, limit: int = 10) -> List[Dict[str, Any]]:
    """
    Simple wrapper function for document search that combines multiple search strategies.
    This function is used by the query endpoint for document search.
    """
    try:
        logger.info(f"Searching documents in Supabase for: '{query}' (limit: {limit})")

        # Try multiple search strategies in order of preference

        # 1. First try content-based search (works well for specific queries)
        content_results = search_documents_by_content(query, limit)
        if content_results:
            logger.info(f"Content search found {len(content_results)} results")
            return content_results

        # 2. Try vector search if available
        try:
            from llm_router import generate_embedding
            query_embedding = generate_embedding(query)
            if query_embedding:
                vector_results = search_supabase_document_chunks_enhanced(
                    query_embedding=query_embedding,
                    query_text=query,
                    use_hybrid_search=True,
                    top_k=limit,
                    min_threshold=0.1
                )
                if vector_results:
                    logger.info(f"Vector search found {len(vector_results)} results")
                    return vector_results
        except Exception as e:
            logger.warning(f"Vector search failed: {str(e)}")

        # 3. Try text-based search as fallback
        text_results = text_based_document_search(query, limit)
        if text_results:
            logger.info(f"Text-based search found {len(text_results)} results")
            return text_results

        logger.info("No document results found with any search method")
        return []

    except Exception as e:
        logger.error(f"Error in search_documents_in_supabase: {str(e)}")
        return []


# Configure LLM models through the router
available_models = llm_router.get_available_models()
logger.info(f"Available LLM models: {[model['id'] for model in available_models]}")

# For backward compatibility
api_key = os.getenv("GEMINI_API_KEY")
if not api_key:
    logger.warning("GEMINI_API_KEY not found in environment variables. Using mock embeddings.")

# Global variable to store document chunks with embeddings
DOCUMENT_CHUNKS = []
# Priority weights for different source types
DOCUMENT_PRIORITY_WEIGHT = 2.5  # Higher priority for documents
WEBSITE_PRIORITY_WEIGHT = 1.5   # Medium priority for websites
# Threshold for considering document chunks relevant
RELEVANCE_THRESHOLD = 0.05  # Very low threshold to prioritize documents

# Create FastAPI app
app = FastAPI(title="Document Management System")

# Import and include category management router
try:
    from category_management import router as category_router
    app.include_router(category_router)
    logger.info("Category management router included successfully")
except ImportError as e:
    logger.warning(f"Could not import category management router: {e}")
except Exception as e:
    logger.warning(f"Error including category management router: {e}")

# Import and include enhanced category management router
try:
    from enhanced_category_management import router as enhanced_category_router
    app.include_router(enhanced_category_router, prefix="/api/enhanced")
    logger.info("Enhanced category management router included successfully")
except ImportError as e:
    logger.warning(f"Could not import enhanced category management router: {e}")
except Exception as e:
    logger.warning(f"Error including enhanced category management router: {e}")

# Configure CORS for local development and production
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # React development server
        "http://127.0.0.1:3000",  # Alternative localhost
        "https://railchatbot-cb555.web.app",  # Production domain
        "*"  # Allow all origins for development
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Custom middleware to increase file upload size limit
@app.middleware("http")
async def add_custom_upload_limit(request: Request, call_next):
    # Set a large upload limit (200MB)
    # This is handled at the application level, not the server level
    # For production, you should also configure your ASGI server (Uvicorn) with appropriate limits
    request.scope.setdefault("_body_size_limit", 200 * 1024 * 1024)  # 200MB
    response = await call_next(request)
    return response

# Define API request and response models
class QueryRequest(BaseModel):
    query: str
    model: Optional[str] = "gemini-2.0-flash"  # Changed to gemini-1.5-flash to fix timeout issues
    fallback_enabled: Optional[bool] = True  # Enable LLM fallback by default
    extract_format: Optional[str] = "paragraph"  # paragraph, bullet, or table
    use_hybrid_search: Optional[bool] = True  # Enable hybrid search by default
    retry_on_timeout: Optional[bool] = True  # Try fallback model on timeout
    context_mode: Optional[str] = "flexible"  # strict, flexible, or none (no context)

class WebsiteScrapeRequest(BaseModel):
    url: str

class WebsiteAddRequest(BaseModel):
    url: str
    submitted_by: Optional[str] = None
    role: Optional[str] = None
    follow_links: Optional[bool] = False
    extraction_depth: Optional[int] = 1
    extract_images: Optional[bool] = False
    extract_tables: Optional[bool] = True
    max_pages: Optional[int] = 10
    extractor_type: Optional[str] = "trafilatura"
    domain_category: Optional[str] = "general"
    embedding_model: Optional[str] = "gemini-2.0-flash"  # Model for embeddings
    # 4-level hierarchical category fields (UUIDs)
    main_category_id: Optional[str] = None
    category_id: Optional[str] = None
    sub_category_id: Optional[str] = None
    minor_category_id: Optional[str] = None
    # Legacy fields for backward compatibility
    main_category: Optional[str] = None
    category: Optional[str] = None
    sub_category: Optional[str] = None
    minor_category: Optional[str] = None

class Source(BaseModel):
    source_type: str  # "document" or "website"
    # For documents
    filename: Optional[str] = None
    name: Optional[str] = None  # Display name for the document
    page: Optional[int] = None
    # For websites
    url: Optional[str] = None
    link: Optional[str] = None  # For document viewer links
    # For visual content
    content_type: Optional[str] = None  # "text", "table", "image", "chart_diagram"
    visual_content: Optional[Dict[str, Any]] = None  # Visual content metadata
    storage_url: Optional[str] = None  # URL for stored visual content
    display_type: Optional[str] = None  # "text", "html_table", "image", "base64_image"

class QueryResponse(BaseModel):
    answer: str  # Combined answer from all sources
    document_answer: Optional[str] = None  # Answer only from document sources
    website_answer: Optional[str] = None  # Answer only from website sources
    sources: List[Source]  # All sources
    document_sources: Optional[List[Source]] = None  # Only document sources
    website_sources: Optional[List[Source]] = None  # Only website sources
    llm_model: Optional[str] = None  # The LLM model used for generating the answer
    llm_fallback: Optional[bool] = False  # Whether the answer was generated using LLM fallback
    visual_content_found: Optional[bool] = False  # Whether visual content was found
    visual_content_types: Optional[List[str]] = None  # Types of visual content found

class ChunkData(BaseModel):
    filename: str
    page: int
    chunk_id: str
    text: str
    embedding: Optional[List[float]] = None

# Document processing functions
def clean_text(text: str) -> str:
    """Clean text for processing."""
    return " ".join(text.split()) if text else ""

def detect_visual_query(query: str) -> Dict[str, Any]:
    """
    Detect if query is looking for visual content and what types.
    Enhanced to detect specific logos, brands, and companies.
    
    Returns:
        Dict with flags for different visual content types and specific entities
    """
    query_lower = query.lower()
    
    # Visual-related keywords
    table_keywords = ["table", "chart", "data", "specifications", "schedule", "list", "grid", "column", "row", "quotation"]
    image_keywords = ["image", "picture", "diagram", "figure", "illustration", "photo", "schematic", "project", "logo", "emblem", "symbol", "icon", "graphic", "drawing", "sketch", "map", "layout", "design", "blueprint", "plan", "screenshot", "snapshot", "visual", "artwork"]
    chart_keywords = ["chart", "graph", "diagram", "flowchart", "flow chart", "technical drawing", "blueprint"]
    
    # Logo and brand related keywords
    logo_keywords = ["logo", "emblem", "symbol", "icon", "brand", "company logo", "trademark", "wordmark", "letterhead"]
    
    # General visual keywords
    visual_keywords = ["visual", "show me", "display", "view", "picture", "graphic"]
    
    is_table_query = any(keyword in query_lower for keyword in table_keywords)
    is_image_query = any(keyword in query_lower for keyword in image_keywords)
    is_chart_query = any(keyword in query_lower for keyword in chart_keywords)
    is_logo_query = any(keyword in query_lower for keyword in logo_keywords)
    is_visual_query = any(keyword in query_lower for keyword in visual_keywords) or is_table_query or is_image_query or is_chart_query or is_logo_query
    
    # Extract specific entities (like "Project 1", "Quotation 2", etc.)
    specific_entities = []
    company_entities = []
    import re
    
    # Look for patterns like "Project 1", "Quotation 1", "Table 2", etc.
    entity_patterns = [
        r'project\s*(\d+)',
        r'quotation\s*(\d+)',
        r'table\s*(\d+)',
        r'image\s*(\d+)',
        r'figure\s*(\d+)',
    ]
    
    for pattern in entity_patterns:
        matches = re.findall(pattern, query_lower)
        if matches:
            entity_type = pattern.split('\\')[0]  # Get the entity type (project, quotation, etc.)
            for match in matches:
                specific_entities.append({
                    "type": entity_type,
                    "number": match,
                    "query_text": f"{entity_type} {match}"
                })
    
    # Extract company/brand names for logo queries
    if is_logo_query or "logo" in query_lower:
        # Common patterns for company names in logo queries
        company_patterns = [
            r'(?:logo\s+(?:of\s+)?|show\s+me\s+(?:the\s+)?logo\s+(?:of\s+)?)([A-Z][A-Za-z\s&]+?)(?:\s+logo|\s+enterprises|\s+company|\s+corp|\s+ltd|\s+inc|\s*$)',
            r'([A-Z][A-Za-z\s&]+?)\s+(?:enterprises|company|corp|ltd|inc)\s+logo',
            r'([A-Z][A-Za-z\s&]+?)\s+logo',
        ]
        
        for pattern in company_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            for match in matches:
                company_name = match.strip()
                if len(company_name) > 1:  # Avoid single characters
                    company_entities.append({
                        "type": "company",
                        "name": company_name,
                        "query_text": f"{company_name} logo"
                    })
    
    # Priority content types based on query analysis
    priority_content_types = []
    if is_logo_query:
        priority_content_types.append("logo")
    if is_image_query:
        priority_content_types.append("image")
    if is_table_query:
        priority_content_types.append("table")
    if is_chart_query:
        priority_content_types.append("chart_diagram")
    
    return {
        "is_visual": is_visual_query,
        "wants_tables": is_table_query,
        "wants_images": is_image_query,
        "wants_charts": is_chart_query,
        "wants_logos": is_logo_query,
        "specific_entities": specific_entities,
        "company_entities": company_entities,
        "priority_content_types": priority_content_types,
        "search_terms": [entity["query_text"] for entity in specific_entities + company_entities]
    }

def search_supabase_document_chunks_enhanced(query_embedding, query_text=None, use_hybrid_search=True, 
                                           top_k=30, min_threshold=0.4, document_filter=None,
                                           visual_query_info=None):
    """
    Enhanced document search that prioritizes visual content when needed.
    Simplified to prevent timeouts while maintaining logo detection functionality.
    """
    try:
        logger.info(f"Enhanced search - Visual query info: {visual_query_info}")
        
        # Quick check for logo queries - simplify the logic to prevent timeouts
        if visual_query_info and visual_query_info.get("wants_logos"):
            logger.info("🎯 Logo query detected - searching for visual content")
            
            # For logo queries, use a simple approach that doesn't timeout
            # Search all chunks with a focus on image content and company entities
            company_entities = visual_query_info.get("company_entities", [])
            
            if company_entities:
                # Extract company names for searching
                company_names = [entity.get("name", "").lower() for entity in company_entities]
                logger.info(f"Searching for company logos: {company_names}")
                
                # Use regular search but filter for visual content afterwards
                regular_chunks = search_supabase_document_chunks(
                    query_embedding=query_embedding,
                    query_text=query_text,
                    use_hybrid_search=use_hybrid_search,
                    top_k=top_k,
                    min_threshold=min_threshold
                )
                
                # Filter and boost chunks that contain company names or are images
                enhanced_chunks = []
                for chunk in regular_chunks:
                    chunk_text = chunk.get("text", "").lower()
                    metadata = chunk.get("metadata", {})
                    content_type = metadata.get("content_type", "text")
                    
                    # Check for company name matches
                    score_boost = 0.0
                    for company_name in company_names:
                        if company_name in chunk_text:
                            score_boost += 0.3
                            logger.info(f"Found company '{company_name}' in chunk")
                    
                    # Boost image content for logo queries
                    if content_type == "image":
                        score_boost += 0.4
                        logger.info(f"Found image content - boosting for logo query")
                    
                    # Apply boost and add to results
                    if score_boost > 0:
                        chunk["similarity"] = chunk.get("similarity", 0) + score_boost
                        enhanced_chunks.append(chunk)
                    else:
                        enhanced_chunks.append(chunk)
                
                # Sort by boosted similarity
                enhanced_chunks.sort(key=lambda x: x.get("similarity", 0), reverse=True)
                logger.info(f"Enhanced search returned {len(enhanced_chunks)} chunks with logo boosting")
                return enhanced_chunks
        
        # For non-visual queries or if visual search fails, use regular search
        logger.info("Using regular search (non-visual query or fallback)")
        return search_supabase_document_chunks(
            query_embedding=query_embedding,
            query_text=query_text,
            use_hybrid_search=use_hybrid_search,
            top_k=top_k,
            min_threshold=min_threshold
        )
            
    except Exception as e:
        logger.error(f"Error in enhanced document search: {str(e)}")
        # Fallback to regular search
        return search_supabase_document_chunks(
            query_embedding=query_embedding,
            query_text=query_text,
            use_hybrid_search=use_hybrid_search,
            top_k=top_k,
            min_threshold=min_threshold
        )

def generate_embedding(text: str, model_id: str = "gemini-2.0-flash") -> List[float]:
    """Generate embedding vector for text using the LLM router."""
    try:
        # Use the LLM router to generate embeddings
        return llm_router.generate_embedding(text, model_id)
    except Exception as e:
        logger.error(f"Error generating embedding with {model_id}: {str(e)}")
        # Try with a different model before falling back to random
        try:
            logger.info(f"Retrying embedding generation with default model")
            return llm_router.generate_embedding(text, llm_router.DEFAULT_MODEL)
        except Exception as fallback_error:
            logger.error(f"Fallback embedding generation also failed: {str(fallback_error)}")
            # Fallback to a consistent embedding rather than random
            # This ensures that if we use this fallback, at least all fallbacks will be similar
            # Using a seed ensures consistency
            np.random.seed(hash(text) % 2**32)
            return list(np.random.rand(768))

def cosine_similarity(embedding1, embedding2):
    """
    Calculate cosine similarity between two embeddings, handling string conversions.
    """
    # Convert embeddings to numpy arrays if they are not already
    try:
        # Handle string embeddings (from JSON)
        if isinstance(embedding1, str):
            try:
                embedding1 = json.loads(embedding1)
            except:
                logger.error("Failed to parse string embedding1")
                return 0.0

        if isinstance(embedding2, str):
            try:
                embedding2 = json.loads(embedding2)
            except:
                logger.error("Failed to parse string embedding2")
                return 0.0

        # Ensure embeddings are numpy arrays of float32
        embedding1 = np.array(embedding1, dtype=np.float32)
        embedding2 = np.array(embedding2, dtype=np.float32)

        # Compute cosine similarity
        dot_product = np.dot(embedding1, embedding2)
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)
    except Exception as e:
        logger.error(f"Error calculating cosine similarity: {str(e)}")
        return 0.0  # Return 0 similarity on error

def search_supabase_document_chunks(query_embedding, query_text=None, use_hybrid_search=True, top_k=30, min_threshold=0.4, document_filter=None):
    """
    Fixed professional vector search for document chunks using proper pgvector syntax.
    """
    try:
        logger.info(f"FIXED vector search for documents: threshold={min_threshold}, top_k={top_k}")

        # Strategy 1: Try direct content search first (most reliable)
        if query_text:
            try:
                direct_results = search_documents_by_content(query_text, limit=top_k)
                if direct_results:
                    logger.info(f"Direct content search found {len(direct_results)} document chunks")
                    
                    # Convert to expected format
                    result_chunks = []
                    for chunk in direct_results:
                        chunk_copy = dict(chunk)
                        chunk_copy['similarity'] = 0.9  # High similarity for direct matches
                        chunk_copy['source_type'] = 'document'
                        
                        # Ensure required fields are present
                        if 'page' not in chunk_copy and 'page_number' in chunk_copy:
                            chunk_copy['page'] = chunk_copy['page_number']
                        
                        # Extract or set filename
                        if not chunk_copy.get('filename'):
                            text = chunk_copy.get('text', '')
                            import re
                            match = re.search(r'Document:\s*([^,]+)', text)
                            if match:
                                filename = match.group(1).strip()
                                if not filename.endswith(('.pdf', '.docx', '.txt')):
                                    filename += '.pdf'
                                chunk_copy['filename'] = filename
                            else:
                                chunk_copy['filename'] = 'Unknown document'
                        
                        result_chunks.append(chunk_copy)
                    
                    return result_chunks[:top_k]
                        
            except Exception as e:
                logger.warning(f"Direct content search failed: {str(e)}")

        # Strategy 2: Try RPC function (if it works)
        try:
            from supabase_client import supabase
            # Use Supabase RPC function for vector search (skip if known to fail)
            result = supabase.rpc(
                'search_document_chunks',
                {
                    'query_embedding': query_embedding,
                    'similarity_threshold': min_threshold,
                    'match_count': top_k
                }
            ).execute()

            if result.data and len(result.data) > 0:
                for chunk in result.data:
                    chunk["source_type"] = "document"
                logger.info(f"RPC search found {len(result.data)} document chunks")
                return result.data
        except Exception as e:
            logger.info(f"RPC search failed, trying local approach: {str(e)}")

        # Fallback to local search with proper embeddings
        logger.info("Using local vector search with cosine similarity")
        global DOCUMENT_CHUNKS

        scored_chunks = []
        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'document':
                continue

            chunk_embedding = chunk.get('embedding')
            if not chunk_embedding:
                continue

            # Calculate cosine similarity
            similarity = cosine_similarity(query_embedding, chunk_embedding)

            if similarity >= min_threshold:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = similarity
                scored_chunks.append(chunk_copy)

        # Sort by similarity (highest first)
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)
        result = scored_chunks[:top_k]
        
        # Ensure document metadata is properly attached to all chunks
        # This prevents "Unknown document" issues in source attribution
        try:
            # Get document IDs from chunks
            doc_ids = [chunk.get('document_id') for chunk in result if chunk.get('document_id')]
            if doc_ids:
                # Query documents table to get filenames and other metadata
                from supabase_client import supabase
                query = f"""
                SELECT id, COALESCE(display_name, file_name, name) as filename, file_path, name
                FROM documents
                WHERE id IN ({','.join([f"'{id}'" for id in doc_ids])})
                """
                doc_results = supabase.execute_query(query)
                if doc_results and len(doc_results) > 0:
                    # Create lookup dictionary
                    doc_lookup = {doc['id']: doc for doc in doc_results}
                    # Update all chunks with their document metadata
                    for chunk in result:
                        doc_id = chunk.get('document_id')
                        if doc_id and doc_id in doc_lookup:
                            chunk['filename'] = doc_lookup[doc_id]['filename']
                            chunk['url'] = doc_lookup[doc_id]['file_path'] if 'file_path' in doc_lookup[doc_id] else None
                            logger.info(f"Enhanced chunk metadata with filename: {chunk['filename']}")
        except Exception as e:
            logger.error(f"Error enhancing chunks with document metadata: {str(e)}")

        logger.info(f"Local vector search found {len(result)} document chunks with similarity >= {min_threshold}")
        if result:
            similarities = [f"{c.get('similarity', 0):.3f}" for c in result[:3]]
            logger.info(f"Top similarities: {similarities}")

        return result

    except Exception as e:
        logger.error(f"Error in fixed vector document search: {str(e)}")
        return []

def search_supabase_website_chunks(query_embedding, query_text=None, use_hybrid_search=True, top_k=30, min_threshold=0.2, website_filter=None):
    """
    Professional vector search for website chunks using Supabase pgvector.
    Scalable solution for 100+ websites using semantic similarity.
    """
    try:
        logger.info(f"Professional vector search for websites: threshold={min_threshold}, top_k={top_k}")

        # Try to use Supabase RPC function first, then fallback to direct SQL
        try:
            from supabase_client import supabase
            # Use Supabase RPC function for vector search if available
            result = supabase.rpc(
                'search_website_chunks',
                {
                    'query_embedding': query_embedding,
                    'similarity_threshold': min_threshold,
                    'match_count': top_k
                }
            ).execute()

            if result.data and len(result.data) > 0:
                for chunk in result.data:
                    chunk["source_type"] = "website"
                logger.info(f"RPC website search found {len(result.data)} chunks")
                return result.data
        except Exception as rpc_error:
            logger.info(f"RPC website search failed: {str(rpc_error)}, trying direct SQL")

        # Fallback to direct SQL query
        try:
            # Convert embedding to proper format for pgvector
            embedding_str = str(query_embedding).replace(' ', '')

            # Use direct SQL with pgvector cosine similarity
            query = f"""
            SELECT
                wc.id,
                wc.website_id,
                wc.chunk_index,
                wc.text,
                w.url,
                w.title as website_name,
                wc.metadata,
                1 - (wc.embedding <=> '{embedding_str}') as similarity
            FROM website_chunks wc
            JOIN websites w ON wc.website_id = w.id
            WHERE 1 - (wc.embedding <=> '{embedding_str}') > {min_threshold}
            ORDER BY wc.embedding <=> '{embedding_str}'
            LIMIT {top_k};
            """

            result = supabase.execute_query(query)
        except Exception as sql_error:
            logger.error(f"Direct SQL website search failed: {str(sql_error)}")
            return []

        if not result or not isinstance(result, list):
            logger.info("No website chunks found with vector search")
            return []

        # Add source_type to each chunk
        for chunk in result:
            chunk["source_type"] = "website"

        logger.info(f"Found {len(result)} relevant website chunks with vector similarity")
        return result

    except Exception as e:
        logger.error(f"Error in professional vector website search: {str(e)}")
        return []

def group_chunks_by_source(chunks):
    """
    Group chunks by source type into document and website categories.

    Args:
        chunks: List of chunks with source_type information

    Returns:
        Tuple of (document_chunks, website_chunks)
    """
    document_chunks = []
    website_chunks = []

    for chunk in chunks:
        source_type = chunk.get("source_type", "").lower()
        if source_type == "document":
            document_chunks.append(chunk)
        elif source_type == "website":
            website_chunks.append(chunk)

    return document_chunks, website_chunks

def generate_llm_answer(query: str, similar_chunks: List[Dict[str, Any]], system_prompt: str = None, model_id: str = "gemini-2.0-flash", extract_format: str = "paragraph"):
    """
    Generate a coherent answer using the LLM router based on similar chunks.

    Args:
        query: The user's question
        similar_chunks: List of context chunks to use for generating the answer
        system_prompt: Custom system prompt to use (optional)
        model_id: LLM model to use (default: gemini-2.0-flash)
        extract_format: Preferred format for the extraction (paragraph, bullet, table)

    Returns:
        Tuple of (answer_text, source_data, document_sources, website_sources)
    """
    try:
        # Prepare context from similar chunks
        context_texts = []
        sources = []
        document_sources = []
        website_sources = []

        # Ensure all chunks have valid text
        valid_chunks = []
        for chunk in similar_chunks:
            # Ensure text field exists and is a string
            if "text" not in chunk or not isinstance(chunk["text"], str) or not chunk["text"].strip():
                # Log the issue and try to fix it
                logger.warning(f"Found chunk with missing or invalid text: {chunk.get('id', 'unknown')}")
                # Try to fix it with a default value
                chunk["text"] = chunk.get("text", "") or "No content available"

            # Ensure source_type is set - use a more aggressive approach to reduce warnings
            if "source_type" not in chunk or not chunk["source_type"]:
                if "filename" in chunk or "page" in chunk or "document_id" in chunk:
                    chunk["source_type"] = "document"
                    # Use less verbose logging to avoid flooding logs
                    if random.random() < 0.01:  # Only log 1% of these to reduce noise
                        logger.debug(f"Set source_type to 'document' for chunk {chunk.get('id', 'unknown')}")
                elif "url" in chunk or "website_id" in chunk:
                    chunk["source_type"] = "website"
                    if random.random() < 0.01:  # Only log 1% of these
                        logger.debug(f"Set source_type to 'website' for chunk {chunk.get('id', 'unknown')}")
                else:
                    chunk["source_type"] = "unknown"
                    if random.random() < 0.01:  # Only log 1% of these
                        logger.debug(f"Set source_type to 'unknown' for chunk {chunk.get('id', 'unknown')}")

            valid_chunks.append(chunk)

        # Use only valid chunks
        similar_chunks = valid_chunks

        # If no valid chunks, return early
        if not similar_chunks:
            logger.warning("No valid chunks found for LLM answer generation")
            return "I couldn't find any valid information to answer your question.", [], [], []

        # Log the content of chunks to help diagnose issues
        logger.info(f"Processing {len(similar_chunks)} chunks for answer generation")
        for i, chunk in enumerate(similar_chunks[:3]):  # Log first 3 chunks for debugging
            logger.info(f"Chunk {i}: source_type={chunk.get('source_type', 'unknown')}, similarity={chunk.get('similarity', 0):.3f}, text_length={len(chunk.get('text', ''))}, text_preview={chunk.get('text', '')[:100]}...")

        for chunk in similar_chunks:
            chunk_text = chunk.get("text", "")
            similarity = chunk.get("similarity", 0)

            # Add context with citation information
            if chunk.get("source_type") == "document":
                # Document source
                filename = chunk.get("filename") or chunk.get("title") or "Unknown document"
                page = chunk.get("page", 1)
                context_texts.append(f"From '{filename}' (page {page}, relevance: {similarity:.2f}):\n{chunk_text}\n")

                # Add to sources list
                source = {
                    "source_type": "document",
                    "filename": filename,
                    "page": page,
                    # For UI display
                    "name": os.path.basename(filename),
                    "link": f"/viewer?file={filename}&page={page}"
                }
                if source not in sources:
                    sources.append(source)
                if source not in document_sources:
                    document_sources.append(source)
            elif chunk.get("source_type") == "website":
                # Website source - extract URL from multiple possible fields
                url = chunk.get("url", "")

                # If no URL field, try to extract from text
                if not url or url == "Unknown":
                    chunk_text_for_url = chunk.get("text", "")
                    # Look for URL patterns in the text
                    import re
                    url_match = re.search(r'URL:\s*(https?://[^\s\n]+)', chunk_text_for_url)
                    if url_match:
                        url = url_match.group(1)
                    else:
                        # Look for website domain patterns
                        domain_match = re.search(r'Website:\s*([^\s,\n]+)', chunk_text_for_url)
                        if domain_match:
                            domain = domain_match.group(1)
                            url = f"https://{domain}" if not domain.startswith('http') else domain
                        # Special case handling for known websites
                        elif "rapidresponseapp" in chunk_text_for_url.lower():
                            url = "https://www.rapidresponseapp.com"
                        elif "indianrailways.gov.in" in chunk_text_for_url.lower():
                            url = "https://indianrailways.gov.in"
                        elif "irctc" in chunk_text_for_url.lower():
                            url = "https://www.irctc.co.in"
                        else:
                            url = "Unknown website"

                context_texts.append(f"From '{url}' (relevance: {similarity:.2f}):\n{chunk_text}\n")

                # Add to sources list
                source = {
                    "source_type": "website",
                    "url": url
                }
                if source not in sources:
                    sources.append(source)
                if source not in website_sources:
                    website_sources.append(source)
            else:
                # Unknown source type
                logger.warning(f"Unknown source type for chunk: {chunk.get('id', 'unknown')}")
                context_texts.append(f"From unknown source (relevance: {similarity:.2f}):\n{chunk_text}\n")

        # Combine all context texts
        context = "\n\n".join(context_texts)
        logger.info(f"Created context with {len(context_texts)} context segments, total length: {len(context)} characters")

        # Token limit handling - approximately 4 characters per token for English text
        # 1M tokens is the absolute limit for Gemini, so we'll stay well under that
        MAX_MODEL_TOKENS = 1000000  # Gemini's maximum token limit
        SAFETY_FACTOR = 0.25  # Use only 75% of the maximum to leave room for response
        CHARS_PER_TOKEN = 4  # Approximate chars per token in English text

        max_context_chars = int((MAX_MODEL_TOKENS * SAFETY_FACTOR) * CHARS_PER_TOKEN)
        logger.info(f"Maximum context size: {max_context_chars} chars (~{int(MAX_MODEL_TOKENS * SAFETY_FACTOR)} tokens)")

        if len(context) > max_context_chars:
            logger.warning(f"Context too large ({len(context)} chars / ~{int(len(context)/CHARS_PER_TOKEN)} tokens), truncating to ~{max_context_chars} chars")

            # Sort chunks by similarity before truncating
            sorted_chunks = sorted(similar_chunks, key=lambda x: x.get('similarity', 0), reverse=True)

            # Start with highest similarity chunks
            new_context_texts = []
            new_context_length = 0
            preserved_sources = []
            preserved_doc_sources = []
            preserved_web_sources = []

            # First include top 5 highest similarity chunks regardless of size (to ensure best matches are included)
            top_chunks = sorted_chunks[:5]
            remaining_chunks = sorted_chunks[5:]

            # Add top chunks first
            for chunk in top_chunks:
                if "text" not in chunk or not chunk["text"]:  # Skip chunks with no text
                    continue

                # Truncate extremely large chunks to a reasonable size
                if len(chunk.get("text", "")) > 10000:  # If chunk is > 10K chars
                    chunk["text"] = chunk["text"][:10000] + "...[truncated due to length]"
                    logger.info(f"Truncated very large chunk of {len(chunk.get('text', ''))} chars")

                chunk_text = f"From '{chunk.get('filename', chunk.get('url', 'Unknown'))}' (relevance: {chunk.get('similarity', 0):.2f}):\n{chunk.get('text', '')}\n"
                new_context_texts.append(chunk_text)
                new_context_length += len(chunk_text)

                # Track which sources we're keeping
                if chunk.get("source_type") == "document":
                    source = {
                        "source_type": "document",
                        "filename": chunk.get("filename", "Unknown document"),
                        "page": chunk.get("page", 1),
                        "name": os.path.basename(chunk.get("filename", "Unknown")),
                        "link": f"/viewer?file={chunk.get('filename', 'Unknown')}&page={chunk.get('page', 1)}"
                    }
                    if source not in preserved_sources:
                        preserved_sources.append(source)
                    if source not in preserved_doc_sources:
                        preserved_doc_sources.append(source)
                elif chunk.get("source_type") == "website":
                    source = {
                        "source_type": "website",
                        "url": chunk.get("url", "Unknown website")
                    }
                    if source not in preserved_sources:
                        preserved_sources.append(source)
                    if source not in preserved_web_sources:
                        preserved_web_sources.append(source)

            # Now add remaining chunks up to the limit
            for chunk in remaining_chunks:
                if "text" not in chunk or not chunk["text"]:  # Skip chunks with no text
                    continue

                # Truncate very large chunks
                if len(chunk.get("text", "")) > 5000:  # More aggressive truncation for non-top chunks
                    chunk["text"] = chunk["text"][:5000] + "...[truncated due to length]"

                chunk_text = f"From '{chunk.get('filename', chunk.get('url', 'Unknown'))}' (relevance: {chunk.get('similarity', 0):.2f}):\n{chunk.get('text', '')}\n"

                # Check if adding this chunk would exceed our limit
                if new_context_length + len(chunk_text) < max_context_chars:
                    new_context_texts.append(chunk_text)
                    new_context_length += len(chunk_text)

                    # Track which sources we're keeping
                    if chunk.get("source_type") == "document":
                        source = {
                            "source_type": "document",
                            "filename": chunk.get("filename", "Unknown document"),
                            "page": chunk.get("page", 1),
                            "name": os.path.basename(chunk.get("filename", "Unknown")),
                            "link": f"/viewer?file={chunk.get('filename', 'Unknown')}&page={chunk.get('page', 1)}"
                        }
                        if source not in preserved_sources:
                            preserved_sources.append(source)
                        if source not in preserved_doc_sources:
                            preserved_doc_sources.append(source)
                    elif chunk.get("source_type") == "website":
                        source = {
                            "source_type": "website",
                            "url": chunk.get("url", "Unknown website")
                        }
                        if source not in preserved_sources:
                            preserved_sources.append(source)
                        if source not in preserved_web_sources:
                            preserved_web_sources.append(source)
                else:
                    # We've reached our limit
                    break

            # Replace the original collections with our trimmed versions
            context = "\n\n".join(new_context_texts)
            sources = preserved_sources
            document_sources = preserved_doc_sources
            website_sources = preserved_web_sources

            logger.info(f"Truncated context to {len(context)} chars (~{int(len(context)/CHARS_PER_TOKEN)} tokens), preserving {len(new_context_texts)} highest relevance chunks")

        if not context.strip():
            logger.warning("Combined context is empty after processing chunks")
            return "I don't have information to answer that question.", [], [], []

        # Default system prompt if none provided
        if not system_prompt:
            system_prompt = f"""
You are RailGPT, an expert information retrieval assistant specializing in Indian Railways.

CRITICAL INSTRUCTIONS:
1. You MUST ONLY use the information provided in the context below to answer the question.
2. EXAMINE all provided context CAREFULLY and EXTRACT relevant information to answer the question.
3. If the context contains DOCUMENT sources, you MUST use those to form your answer, even if the similarity score is 0.0 or the content seems only tangentially related.
4. If the context contains WEBSITE sources but no DOCUMENT sources, use the website information, even if the similarity score is 0.0 or the content seems only tangentially related.
5. You MUST include source references for ALL information you provide (document names, pages, website URLs).
6. If the context DOES NOT contain relevant information to answer the question CLEARLY STATE: "I couldn't find any valid information to answer your question." DO NOT make up an answer.
7. DO NOT use your general knowledge under any circumstances.
8. Format your response in a clear, readable manner with proper paragraphs and bullet points where appropriate.
9. IMPORTANT: Even if the information in the context seems incomplete or only partially relevant, you MUST use it and not fall back to general knowledge.

The context information below contains actual document and website content that has been retrieved based on the user's query.
DO NOT say things like "Based on the context provided" or "According to the information given" - just provide the answer directly with references.

CONTEXT:
{context}
"""

            # Additional instructions
            system_prompt += """
NEVER use your general knowledge to answer the question.
ONLY use the information provided in the context.
If the context doesn't contain enough information, say "Based on the available document information..." and then answer with what you can find in the context.
If you're unsure about the answer based on the context, state what you can determine from the context and indicate that the information may be limited.
"""

        # Add format preference to system prompt
        if extract_format == "bullet":
            system_prompt += "\nStructure your answer using bullet points where appropriate for better readability."
        elif extract_format == "table":
            system_prompt += "\nIf the answer contains tabular data, format it as a markdown table for better readability."
        else: # paragraph (default)
            system_prompt += "\nStructure your answer in clear paragraphs for better readability."

        # Use the LLM router to generate the answer
        try:
            # Generate the answer with the LLM
            answer = llm_router.generate_answer(query, context, system_prompt, model_id)

            # Process the results based on the source types
            # If there are only document sources, only return document answer
            if document_sources and not website_sources:
                return answer, sources, document_sources, []
            # If there are only website sources, only return website answer
            elif website_sources and not document_sources:
                return answer, sources, [], website_sources
            # If there are both document and website sources, or no sources
            else:
                return answer, sources, document_sources, website_sources

        except Exception as e:
            logger.error(f"Error generating LLM answer: {str(e)}")
            # Fallback to a simpler prompt
            try:
                fallback_answer = llm_router.generate_answer(
                    query=query,
                    context="",  # Empty context for fallback
                    system_prompt="You are an AI assistant. Answer this question to the best of your ability: " + query,
                    model_id=model_id
                )
                return fallback_answer, [], [], []
            except Exception as fallback_err:
                logger.error(f"Fallback also failed: {str(fallback_err)}")
                return "I'm sorry, I couldn't process your question. Please try again.", [], [], []

    except Exception as e:
        # Catch-all for any other errors
        logger.error(f"Unexpected error in generate_llm_answer with {model_id}: {str(e)}")
        return f"I encountered an error while trying to generate an answer: {str(e)}", [], [], []

def load_documents(data_dir: str = './data'):
    """Load and process all supported documents from a directory."""
    global DOCUMENT_CHUNKS
    DOCUMENT_CHUNKS = []

    logger.info(f"Loading documents on startup")

    # Load document chunks from Supabase first using the correct method
    try:
        from supabase_client import supabase
        logger.info("Loading all document chunks from Supabase")

        # Use the table() method for better compatibility
        chunks_query = "SELECT * FROM document_chunks"
        chunks_result = supabase.execute_query(chunks_query)
        
        logger.info(f"Loaded {len(chunks_result) if chunks_result else 0} document chunks from Supabase")
        
        # Load document chunks
        if chunks_result and len(chunks_result) > 0:
            for chunk in chunks_result:
                # Get document info for each chunk
                try:
                    doc_query = f"SELECT display_name, file_path FROM documents WHERE id = '{chunk.get('document_id')}'"
                    doc_result = supabase.execute_query(doc_query)
                    
                    if doc_result and len(doc_result) > 0:
                        doc_info = doc_result[0]
                        chunk['filename'] = doc_info.get('display_name', 'Unknown')
                        chunk['url'] = doc_info.get('file_path', '')
                    else:
                        chunk['filename'] = 'Unknown Document'
                        chunk['url'] = ''
                    
                    chunk['source_type'] = 'document'
                    chunk['similarity'] = 0.85
                    
                    # Add embedding if needed
                    if "embedding" not in chunk or not chunk["embedding"]:
                        chunk_text = chunk.get("text", "")
                        if chunk_text:
                            try:
                                chunk["embedding"] = generate_embedding(chunk_text)
                            except Exception as e:
                                logger.warning(f"Failed to generate embedding for chunk {chunk.get('id', 'unknown')}: {str(e)}")
                                chunk["embedding"] = [0.01] * 768
                        else:
                            chunk["embedding"] = [0.01] * 768
                    
                    DOCUMENT_CHUNKS.append(chunk)
                except Exception as e:
                    logger.warning(f"Error processing document chunk {chunk.get('id', 'unknown')}: {str(e)}")
                    
        logger.info(f"Loaded {len([c for c in DOCUMENT_CHUNKS if c.get('source_type') == 'document'])} document chunks from Supabase")
    
    except Exception as e:
        logger.error(f"Error loading document chunks from Supabase: {str(e)}")

    # Also load website chunks from Supabase
    try:
        from supabase_client import supabase
        logger.info("Loading all website chunks from Supabase")

        # Use the table() method for better compatibility
        chunks_query = "SELECT * FROM website_chunks"
        chunks_result = supabase.execute_query(chunks_query)
        
        logger.info(f"Loaded {len(chunks_result) if chunks_result else 0} website chunks from Supabase")
        
        # Load website chunks
        if chunks_result and len(chunks_result) > 0:
            for chunk in chunks_result:
                # Get website info for each chunk
                try:
                    website_query = f"SELECT url, domain FROM websites WHERE id = '{chunk.get('website_id')}'"
                    website_result = supabase.execute_query(website_query)
                    
                    if website_result and len(website_result) > 0:
                        website_info = website_result[0]
                        chunk['url'] = website_info.get('url', 'Unknown URL')
                        chunk['domain'] = website_info.get('domain', 'Unknown Domain')
                    else:
                        chunk['url'] = 'Unknown Website'
                        chunk['domain'] = 'Unknown Domain'
                    
                    chunk['source_type'] = 'website'
                    chunk['similarity'] = 0.70
                    
                    # Add embedding if needed
                    if "embedding" not in chunk or not chunk["embedding"]:
                        chunk_text = chunk.get("text", "")
                        if chunk_text:
                            try:
                                chunk["embedding"] = generate_embedding(chunk_text)
                            except Exception as e:
                                logger.warning(f"Failed to generate embedding for website chunk {chunk.get('id', 'unknown')}: {str(e)}")
                                chunk["embedding"] = [0.01] * 768
                        else:
                            chunk["embedding"] = [0.01] * 768
                    
                    DOCUMENT_CHUNKS.append(chunk)
                except Exception as e:
                    logger.warning(f"Error processing website chunk {chunk.get('id', 'unknown')}: {str(e)}")
                    
        logger.info(f"Loaded {len([c for c in DOCUMENT_CHUNKS if c.get('source_type') == 'website'])} website chunks from Supabase")
    
    except Exception as e:
        logger.error(f"Error loading website chunks from Supabase: {str(e)}")

    # Ensure data directory exists
    if not os.path.exists(data_dir):
        logger.warning(f"No supported document files found in {data_dir}")
        return DOCUMENT_CHUNKS

    # Get all supported document files in the directory
    supported_extensions = ['.pdf', '.docx', '.doc', '.txt']
    document_files = []

    for ext in supported_extensions:
        files = [f for f in os.listdir(data_dir) if f.lower().endswith(ext)]
        document_files.extend(files)

    if not document_files:
        logger.warning(f"No supported document files found in {data_dir}")

    return DOCUMENT_CHUNKS

@app.on_event("startup")
async def startup_event():
    """Load documents on server startup and check Supabase configuration."""
    logger.info("Loading documents on startup")
    load_documents()

    # Log how many chunks were loaded
    logger.info(f"Loaded {len(DOCUMENT_CHUNKS)} document chunks on startup")

    # CLEAR and reload chunks properly from Supabase database
    try:
        logger.info("Loading additional chunks from Supabase database...")
        
        # Clear existing chunks first to avoid duplicates
        DOCUMENT_CHUNKS.clear()
        
        # Load document chunks from database
        doc_chunks = supabase.table("document_chunks").select("*").execute()
        if doc_chunks.data:
            for chunk in doc_chunks.data:
                chunk['source_type'] = 'document'
                # Get filename from documents table if not present
                if not chunk.get('filename') and chunk.get('document_id'):
                    try:
                        doc_result = supabase.table("documents").select("display_name, file_path").eq("id", chunk['document_id']).execute()
                        if doc_result.data:
                            chunk['filename'] = doc_result.data[0].get('display_name', 'Unknown')
                            chunk['url'] = doc_result.data[0].get('file_path', '')
                        else:
                            chunk['filename'] = 'Unknown document'
                            chunk['url'] = ''
                    except:
                        chunk['filename'] = 'Unknown document'
                        chunk['url'] = ''
                        
            DOCUMENT_CHUNKS.extend(doc_chunks.data)
            logger.info(f"✅ Loaded {len(doc_chunks.data)} document chunks from database")
        else:
            logger.warning("No document chunks found in database")
        
        # Load website chunks from database
        web_chunks = supabase.table("website_chunks").select("*").execute()
        if web_chunks.data:
            for chunk in web_chunks.data:
                chunk['source_type'] = 'website'
                # Get website info
                if chunk.get('website_id'):
                    try:
                        website_result = supabase.table("websites").select("url, domain").eq("id", chunk['website_id']).execute()
                        if website_result.data:
                            chunk['url'] = website_result.data[0].get('url', 'Unknown URL')
                            chunk['domain'] = website_result.data[0].get('domain', 'Unknown Domain')
                    except:
                        chunk['url'] = 'Unknown URL'
                        chunk['domain'] = 'Unknown Domain'
                        
            DOCUMENT_CHUNKS.extend(web_chunks.data)
            logger.info(f"✅ Loaded {len(web_chunks.data)} website chunks from database")
        else:
            logger.warning("No website chunks found in database")
        
        logger.info(f"🎉 Total chunks now in memory: {len(DOCUMENT_CHUNKS)}")
        
    except Exception as e:
        logger.error(f"Error loading chunks from database: {str(e)}")

    # Check Supabase configuration
    try:
        # Check if Supabase is configured
        if os.getenv("USE_SUPABASE", "true").lower() == "true":
            # Test Supabase connection
            result = supabase.execute_query("SELECT 1 as test")
            if "error" in result:
                logger.error(f"Supabase connection test failed: {result['error']}")
            else:
                logger.info("Supabase connection test successful")

                # Skip schema checks and directly try to create/access buckets
                try:
                    logger.info("Checking Supabase storage setup...")

                    # Directly try to create the documents bucket
                    # This will either succeed or fail gracefully with a mock response
                    bucket_result = supabase.create_bucket_if_not_exists("documents")

                    if "error" in bucket_result:
                        logger.info(f"Using mock storage bucket for 'documents': {bucket_result.get('id')}")
                    else:
                        logger.info(f"Successfully accessed/created bucket: {bucket_result.get('id')}")

                    # Create additional buckets if needed
                    supabase.create_bucket_if_not_exists("websites", is_public=True)
                    supabase.create_bucket_if_not_exists("images", is_public=True)
                except Exception as e:
                    logger.warning(f"Error setting up Supabase storage: {str(e)}")
                    logger.info("Will use mock storage functionality when needed.")
    except Exception as e:
        logger.error(f"Error checking Supabase configuration: {str(e)}")

# Root endpoint
@app.get("/")
async def read_root():
    return {"message": "Document Management System API is running"}

# Health check endpoint
@app.get("/api/health")
async def health_check():
    return {"status": "ok", "message": "API is running"}

# Environment variables check endpoint
@app.get("/api/env-check")
async def env_check():
    """Check environment variables for debugging"""
    import os
    return {
        "SUPABASE_URL": os.getenv("SUPABASE_URL", "NOT_SET"),
        "SUPABASE_KEY_SET": "YES" if os.getenv("SUPABASE_KEY") else "NO",
        "SUPABASE_ANON_KEY_SET": "YES" if os.getenv("SUPABASE_ANON_KEY") else "NO",
        "supabase_client_status": "initialized" if hasattr(supabase, 'supabase') and supabase.supabase else "not_initialized"
    }

# Alternative health check endpoint (for compatibility)
@app.get("/health")
async def health_check_alt():
    return {"status": "ok", "message": "API is running"}

@app.get("/api/debug/paths")
async def debug_paths():
    """Debug endpoint to check server paths"""
    import os

    cwd = os.getcwd()
    script_dir = os.path.dirname(os.path.abspath(__file__))

    test_files = ["SampleRailwayDoc.pdf", "ACP 110V.docx"]
    file_checks = {}

    for filename in test_files:
        possible_paths = [
            os.path.join(script_dir, "data", "uploads", filename),
            os.path.join(script_dir, "data", filename),
            f"./backend/data/uploads/{filename}",
            f"./data/uploads/{filename}",
            f"./uploads/{filename}",
        ]

        file_checks[filename] = []
        for path in possible_paths:
            file_checks[filename].append({
                "path": path,
                "exists": os.path.exists(path)
            })

    return {
        "current_working_directory": cwd,
        "script_directory": script_dir,
        "file_checks": file_checks
    }

@app.get("/api/test/view/{filename}")
async def test_view_document(filename: str):
    """Test endpoint to check if route matching works"""
    return {
        "message": f"Test endpoint reached with filename: {filename}",
        "decoded_filename": filename
    }

# Clear database endpoint (for testing only)
@app.post("/api/clear-database")
async def clear_database():
    """Clear all data from the database (for testing purposes)"""
    try:
        result = supabase.clear_all_data()
        return {"message": "Database cleared successfully", "result": result}
    except Exception as e:
        logger.error(f"Error clearing database: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error clearing database: {str(e)}")

# Get all documents endpoint
@app.get("/api/documents")
async def get_documents():
    """Get all documents from Supabase database"""
    try:
        logger.info("Fetching documents from Supabase database")

        # First, let's see what columns are actually available in the documents table
        try:
            # Get table schema first
            schema_query = "SELECT * FROM documents LIMIT 1"
            schema_result = supabase.execute_query(schema_query)
            if schema_result:
                logger.info(f"Sample document record: {schema_result[0] if schema_result else 'No records'}")
        except Exception as e:
            logger.error(f"Error checking table schema: {str(e)}")

        # Query documents from Supabase with proper error handling
        documents_query = """
        SELECT * FROM documents
        ORDER BY created_at DESC
        """

        result = supabase.execute_query(documents_query)

        if result and len(result) > 0:
            # Process the results to match frontend expectations
            processed_documents = []
            for doc in result:
                logger.info(f"Processing document: {doc}")

                # Map Supabase fields to frontend expected fields
                processed_doc = {
                    "id": doc.get("id", "unknown"),
                    "name": doc.get("display_name") or doc.get("file_name") or doc.get("name") or "Unknown Document",
                    "uploadedAt": doc.get("created_at", ""),
                    "fileType": doc.get("file_type", "unknown"),
                    "fileSize": doc.get("file_size", 0),
                    "filePath": doc.get("file_path", ""),
                    "uploadedBy": doc.get("uploaded_by", "unknown"),
                    "status": doc.get("status", "processed"),
                    "qualityScore": doc.get("extraction_quality", 85),
                    "mainCategory": "Documents",
                    "category": "Uploaded",
                    # Additional fields for debugging
                    "originalData": doc  # Keep original data for debugging
                }
                processed_documents.append(processed_doc)

            logger.info(f"Found {len(processed_documents)} documents in database")
            return processed_documents
        else:
            logger.info("No documents found in database")
            return []

    except Exception as e:
        logger.error(f"Error fetching documents from Supabase: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        # Return empty list on error instead of mock data
        return []

# Get all websites endpoint
@app.get("/api/websites")
async def get_websites():
    """Get all websites from Supabase database"""
    try:
        logger.info("Fetching websites from Supabase database")

        # First, let's see what columns are actually available in the websites table
        try:
            # Get table schema first
            schema_query = "SELECT * FROM websites LIMIT 1"
            schema_result = supabase.execute_query(schema_query)
            if schema_result:
                logger.info(f"Sample website record: {schema_result[0] if schema_result else 'No records'}")
        except Exception as e:
            logger.error(f"Error checking websites table schema: {str(e)}")

        # Query websites from Supabase with proper error handling
        websites_query = """
        SELECT * FROM websites
        ORDER BY created_at DESC
        """

        result = supabase.execute_query(websites_query)

        if result and len(result) > 0:
            # Process the results to match frontend expectations
            processed_websites = []
            for site in result:
                logger.info(f"Processing website: {site}")

                # Extract domain from URL
                url = site.get("url", "")
                try:
                    from urllib.parse import urlparse
                    domain = urlparse(url).netloc.replace('www.', '')
                except:
                    domain = url

                # Map Supabase fields to frontend expected fields
                processed_site = {
                    "id": site.get("id", "unknown"),
                    "name": site.get("name") or site.get("title") or domain,
                    "url": url,
                    "domain": domain,
                    "extractedAt": site.get("created_at", ""),
                    "status": "Success" if site.get("status", "processed") == "processed" else "Failed",
                    "submittedBy": site.get("submitted_by", "unknown"),
                    "domainCategory": site.get("category", "General"),
                    # Hierarchical category fields
                    "mainCategory": site.get("main_category"),
                    "category": site.get("category_level2"),
                    "subCategory": site.get("sub_category"),
                    "minorCategory": site.get("minor_category"),
                    # Additional fields for debugging
                    "originalData": site  # Keep original data for debugging
                }
                processed_websites.append(processed_site)

            logger.info(f"Found {len(processed_websites)} websites in database")
            return processed_websites
        else:
            logger.info("No websites found in database")
            return []

    except Exception as e:
        logger.error(f"Error fetching websites from Supabase: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        # Return empty list on error instead of mock data
        return []


def format_visual_content_for_frontend(metadata, content_type):
    """Format visual content metadata for frontend consumption."""
    if not metadata or content_type == "text":
        return None
    
    formatted_content = {}
    
    if content_type == "table":
        # For tables, extract the table data and markdown
        if "table_data" in metadata:
            formatted_content["table_data"] = metadata["table_data"]
        if "markdown_table" in metadata:
            formatted_content["markdown_table"] = metadata["markdown_table"]
        if "text_table" in metadata:
            formatted_content["text_table"] = metadata["text_table"]
    
    elif content_type == "image":
        # For images, extract base64 data and metadata from nested structure
        if "visual_content" in metadata and "images" in metadata["visual_content"]:
            images = metadata["visual_content"]["images"]
            if isinstance(images, list) and len(images) > 0:
                # Use the first image for now (could be enhanced to show all)
                first_image = images[0]
                if "base64_data" in first_image:
                    formatted_content["base64_data"] = first_image["base64_data"]
                # Store all images for potential display of multiple images
                formatted_content["images"] = images
        # Fallback: look for direct base64_data in metadata
        elif "base64_data" in metadata:
            formatted_content["base64_data"] = metadata["base64_data"]
        if "image_url" in metadata:
            formatted_content["image_url"] = metadata["image_url"]
    
    # Add common fields
    if "storage_url" in metadata:
        formatted_content["storage_url"] = metadata["storage_url"]
    if "visual_content_type" in metadata:
        formatted_content["visual_content_type"] = metadata["visual_content_type"]
    if "display_type" in metadata:
        formatted_content["display_type"] = metadata["display_type"]
    
    return formatted_content

def generate_clean_answer_with_sources(query: str, chunks: List[Dict[str, Any]], source_type: str, model_id: str = "gemini-2.0-flash", extract_format: str = "paragraph"):
    """
    Generate a clean answer with properly formatted sources for a specific source type.
    ONLY tracks chunks that are actually used in the LLM prompt as sources.

    Args:
        query: The user's question
        chunks: List of chunks from a single source type (document OR website)
        source_type: Either "document" or "website"
        model_id: LLM model to use
        extract_format: Preferred format for the extraction

    Returns:
        Tuple of (answer_text, clean_sources_list, visual_content_found, visual_content_types)
    """
    try:
        logger.info(f"🔍 Generating clean {source_type} answer from {len(chunks)} total chunks")

        # STEP 1: Filter and rank chunks by relevance
        min_doc_threshold = 0.25   # FIXED: Increased threshold for documents
        min_web_threshold = 0.15  # FIXED: Increased threshold for websites
        min_threshold = min_doc_threshold if source_type == "document" else min_web_threshold

        # Filter chunks by relevance first
        relevant_chunks = []
        for chunk in chunks:
            similarity = chunk.get('similarity', 0.0)
            chunk_text = chunk.get('text', '').strip()

            # FIXED: Skip chunks with very low similarity, empty text, or insufficient content
            if similarity < min_threshold or not chunk_text or len(chunk_text) < 20:
                logger.info(f"⏭️  [FIXED] Skipping {source_type} chunk with similarity {similarity:.3f} (below threshold {min_threshold}) or insufficient text")
                continue

            relevant_chunks.append(chunk)

        # If no relevant chunks found, return empty result
        if not relevant_chunks:
            logger.info(f"❌ No relevant {source_type} chunks found above threshold {min_threshold}")
            return f"I couldn't find relevant information in the {source_type} content to answer your question.", [], False, []

        # STEP 2: Select TOP chunks to actually use in LLM prompt (CRITICAL CHANGE)
        # Only use the most relevant chunks, not all relevant chunks
        max_chunks_to_use = 4 if source_type == "document" else 3  # FIXED: Reduced chunks used
        
        # Sort by similarity and take top chunks
        relevant_chunks_sorted = sorted(relevant_chunks, key=lambda x: x.get('similarity', 0), reverse=True)
        chunks_to_use = relevant_chunks_sorted[:max_chunks_to_use]
        
        logger.info(f"📊 Using TOP {len(chunks_to_use)} chunks out of {len(relevant_chunks)} relevant chunks (out of {len(chunks)} total)")
        
        # Log which chunks we're actually using
        for i, chunk in enumerate(chunks_to_use, 1):
            similarity = chunk.get('similarity', 0)
            if source_type == "document":
                filename = chunk.get('filename', 'Unknown')
                page = chunk.get('page', 'Unknown')
                logger.info(f"   ✅ {i}. Using: {filename} (Page {page}) - Similarity: {similarity:.3f}")
            else:
                url = chunk.get('url', 'Unknown URL')
                logger.info(f"   ✅ {i}. Using: {url} - Similarity: {similarity:.3f}")

        # STEP 3: Build context ONLY from chunks we're actually using
        context_texts = []
        used_sources_tracker = {}  # FIXED: Track ONLY chunks actually used in LLM prompt
        visual_content_found = False
        visual_content_types = []

        for chunk in chunks_to_use:  # ONLY iterate over chunks we're using
            chunk_text = chunk.get("text", "")
            similarity = chunk.get("similarity", 0)

            if source_type == "document":
                # Document source processing
                document_id = chunk.get("document_id")
                # First try to get filename from document_id
                filename = None
                if document_id:
                    try:
                        from supabase_client import supabase
                        doc_query = f"SELECT COALESCE(display_name, file_name, name) as filename FROM documents WHERE id = '{document_id}'"
                        doc_result = supabase.execute_query(doc_query)
                        if doc_result and len(doc_result) > 0:
                            filename = doc_result[0]['filename']
                    except Exception as e:
                        logger.error(f"Error retrieving document name: {str(e)}")
                
                # Fall back to chunk metadata if needed
                if not filename:
                    filename = chunk.get("filename") or chunk.get("title") or "Unknown document"
                
                page = chunk.get("page") or chunk.get("page_number") or 1

                # Check for visual content
                metadata = chunk.get("metadata", {})
                content_type = metadata.get("content_type", "text")
                visual_content_type = metadata.get("visual_content_type")
                storage_url = metadata.get("storage_url")
                display_type = metadata.get("display_type", "text")

                # Track visual content
                if content_type != "text":
                    visual_content_found = True
                    if content_type not in visual_content_types:
                        visual_content_types.append(content_type)

                # Enhanced context for visual content
                if content_type in ["table", "image", "chart_diagram"]:
                    context_texts.append(f"From '{filename}' (page {page}, {content_type}, relevance: {similarity:.2f}):\n{chunk_text}\n")
                else:
                    context_texts.append(f"From '{filename}' (page {page}, relevance: {similarity:.2f}):\n{chunk_text}\n")

                # Create unique source key for deduplication (include content type for visual content)
                if content_type != "text":
                    source_key = f"{filename}_{content_type}_{page}"
                else:
                    source_key = f"{filename}"
                    
                logger.info(f"📝 [FIXED] Chunk {i+1}: Using '{filename}' page {page} (similarity: {similarity:.3f})")
                    
                if source_key not in used_sources_tracker:
                    used_sources_tracker[source_key] = {
                        "source_type": "document",
                        "filename": filename,
                        "name": os.path.basename(filename),
                        "pages": [],
                        "content_type": content_type,
                        "visual_content_type": visual_content_type,
                        "storage_url": storage_url,
                        "display_type": display_type,
                        "visual_content": format_visual_content_for_frontend(metadata, content_type) if content_type != "text" else None
                    }

                # Add page if not already present
                if page not in used_sources_tracker[source_key]["pages"]:
                    used_sources_tracker[source_key]["pages"].append(page)

            elif source_type == "website":
                # Website source processing - extract URL from multiple possible fields
                url = chunk.get("url", "")

                # If no URL field, try to extract from text
                if not url or url == "Unknown":
                    chunk_text_for_url = chunk.get("text", "")
                    # Look for URL patterns in the text
                    import re
                    url_match = re.search(r'URL:\s*(https?://[^\s\n]+)', chunk_text_for_url)
                    if url_match:
                        url = url_match.group(1)
                    else:
                        # Look for website domain patterns
                        domain_match = re.search(r'Website:\s*([^\s,\n]+)', chunk_text_for_url)
                        if domain_match:
                            domain = domain_match.group(1)
                            url = f"https://{domain}" if not domain.startswith('http') else domain
                        # Special case handling for known websites
                        elif "rapidresponseapp" in chunk_text_for_url.lower():
                            url = "https://www.rapidresponseapp.com"
                        elif "indianrailways.gov.in" in chunk_text_for_url.lower():
                            url = "https://indianrailways.gov.in"
                        elif "irctc" in chunk_text_for_url.lower():
                            url = "https://www.irctc.co.in"
                        else:
                            url = "Unknown website"

                # Add to context
                context_texts.append(f"From '{url}' (relevance: {similarity:.2f}):\n{chunk_text}\n")

                # Create unique source key for deduplication
                source_key = url
                if source_key not in used_sources_tracker:
                    used_sources_tracker[source_key] = {
                        "source_type": "website",
                        "url": url,
                        "title": chunk.get("title", "")
                    }

        # STEP 4: Generate answer using ONLY the selected chunks
        if not context_texts:
            logger.info(f"❌ No valid context generated from {source_type} chunks")
            return f"I couldn't extract meaningful information from the {source_type} content to answer your question.", [], visual_content_found, visual_content_types

        # Combine context
        combined_context = "\n".join(context_texts)
        
        logger.info(f"🤖 Generating {source_type} answer with context from {len(chunks_to_use)} chunks")
        logger.info(f"📊 Context length: {len(combined_context)} characters")

        # Generate the answer using only selected chunks
        answer = generate_llm_answer(
            query=query,
            similar_chunks=chunks_to_use,  # Pass only chunks actually being used
            model_id=model_id,
            extract_format=extract_format
        )

        if not answer or answer.strip() == "":
            logger.warning(f"⚠️  Empty answer generated for {source_type} query")
            return f"I couldn't generate a meaningful answer from the {source_type} content.", [], visual_content_found, visual_content_types

        # STEP 5: Build clean sources list from ONLY the chunks we used
        clean_sources = []
        
        for source_data in used_sources_tracker.values():
            if source_type == "document":
                # Calculate relevance score for this document source
                # Find the highest similarity chunk for this document among USED chunks
                max_similarity = 0
                for chunk in chunks_to_use:  # Only check chunks we actually used
                    if chunk.get("filename") == source_data["filename"]:
                        chunk_similarity = chunk.get("similarity", 0)
                        if chunk_similarity > max_similarity:
                            max_similarity = chunk_similarity

                # Sort pages and create page reference
                pages = sorted(source_data["pages"])
                logger.info(f"📄 Source: '{source_data['name']}' - Pages used: {pages}")
                
                if len(pages) == 1:
                    page_ref = f"Page {pages[0]}"
                else:
                    page_ref = f"Pages {', '.join(map(str, pages))}"

                # Enhanced source with visual content information
                clean_source = {
                    "source_type": "document",
                    "filename": source_data["filename"],
                    "name": source_data["name"],
                    "page": pages[0],  # First page for link generation
                    "pages": pages,    # All pages used
                    "link": f"/viewer?file={source_data['filename']}&page={pages[0]}",
                    "display_text": f"{source_data['name']} – {page_ref}",
                    "relevance_score": max_similarity,
                    "content_type": source_data.get("content_type", "text"),
                    "visual_content": source_data.get("visual_content"),
                    "storage_url": source_data.get("storage_url"),
                    "display_type": source_data.get("display_type", "text")
                }
                clean_sources.append(clean_source)

            elif source_type == "website":
                # Calculate relevance score for this website source among USED chunks
                max_similarity = 0
                for chunk in chunks_to_use:  # Only check chunks we actually used
                    if chunk.get("url") == source_data["url"]:
                        chunk_similarity = chunk.get("similarity", 0)
                        if chunk_similarity > max_similarity:
                            max_similarity = chunk_similarity

                clean_source = {
                    "source_type": "website",
                    "url": source_data["url"],
                    "title": source_data.get("title", ""),
                    "name": source_data.get("title", source_data["url"]),
                    "relevance_score": max_similarity,
                    "display_text": source_data.get("title", source_data["url"])
                }
                clean_sources.append(clean_source)

        # Sort sources by relevance
        clean_sources.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)

        logger.info(f"✅ Generated {source_type} answer with {len(clean_sources)} TRUE sources (from chunks actually used)")
        
        # Log final sources for verification
        for i, source in enumerate(clean_sources, 1):
            if source_type == "document":
                logger.info(f"   📄 {i}. {source['display_text']} (Relevance: {source['relevance_score']:.3f})")
            else:
                logger.info(f"   🌐 {i}. {source['display_text']} (Relevance: {source['relevance_score']:.3f})")

        return answer, clean_sources, visual_content_found, visual_content_types

    except Exception as e:
        logger.error(f"❌ Error in generate_clean_answer_with_sources: {str(e)}")
        return f"I encountered an error while processing the {source_type} content.", [], False, []

# Chunks endpoint
@app.get("/api/chunks")
async def get_chunks():
    # Return document chunks without embeddings for response size efficiency
    chunks_without_embeddings = []
    for chunk in DOCUMENT_CHUNKS:
        chunk_copy = dict(chunk)
        if "embedding" in chunk_copy:
            del chunk_copy["embedding"]
        chunks_without_embeddings.append(chunk_copy)

    return chunks_without_embeddings

@app.post("/api/query", response_model=QueryResponse)
async def query(request: QueryRequest):
    logger.info(f"Processing query: {request.query} using model: {request.model}")

    # Initialize variables
    used_fallback_model = False
    original_model = request.model
    fallback_reason = None
    document_answer = None
    website_answer = None
    combined_answer = ""
    combined_sources = []
    document_sources = []
    website_sources = []
    llm_fallback_used = False
    model_id = request.model
    answer_source = "Unknown"

    try:
        # Validate the requested model and API key
        if not llm_router.is_model_available(request.model):
            logger.warning(f"Model {request.model} not available, falling back to default model")
            model_id = llm_router.DEFAULT_MODEL
            used_fallback_model = True
            fallback_reason = f"Model '{request.model}' is not available"
        else:
            # Check if API key is valid for this model
            model_config = llm_router.get_model_config(model_id)
            key_status = llm_router.check_api_key_validity(model_config["provider"])
            if not key_status.get("valid", False):
                logger.warning(f"Invalid API key for {model_id}, falling back to default model")
                model_id = llm_router.DEFAULT_MODEL
                used_fallback_model = True
                fallback_reason = f"API key for {original_model} is invalid: {key_status.get('message', '')}"

        logger.info("=== IMPLEMENTING STRICT PRIORITY ANSWER DISPLAY LOGIC ===")

        # Generate query embedding for vector search
        logger.info("🔍 Generating query embedding...")
        try:
            query_embedding = generate_embedding(request.query, model_id)
            logger.info(f"✅ Query embedding generated successfully (size: {len(query_embedding) if query_embedding else 0})")
        except Exception as e:
            logger.error(f"❌ Failed to generate query embedding: {str(e)}")
            query_embedding = None

        # STEP 1: Search Document Chunks (HIGHEST PRIORITY)
        logger.info("=== STEP 1: SEARCHING DOCUMENT CHUNKS (HIGHEST PRIORITY) ===")
        document_chunks = None
        document_answer = None
        document_sources = []
        
        # Initialize visual content tracking variables
        doc_visual_found = False
        doc_visual_types = []

        try:
            document_chunks = search_supabase_document_chunks_enhanced(
                query_embedding=query_embedding, 
                query_text=request.query, 
                use_hybrid_search=request.use_hybrid_search,
                visual_query_info=detect_visual_query(request.query)
            )
            if document_chunks:
                logger.info(f"✅ STEP 1: Found {len(document_chunks)} relevant document chunks")
                for i, chunk in enumerate(document_chunks[:3], 1):
                    logger.info(f"   {i}. Similarity: {chunk.get('similarity', 0):.3f} | {chunk.get('filename', 'Unknown')}")
                
                # Generate clean document answer
                document_answer, document_sources, doc_visual_found, doc_visual_types = generate_clean_answer_with_sources(
                    query=request.query,
                    chunks=document_chunks,
                    source_type="document",
                    model_id=model_id,
                    extract_format=request.extract_format
                )
                logger.info(f"✅ Document answer generated with {len(document_sources)} sources")
            else:
                logger.info("⚠️ STEP 1: No relevant document chunks found")
        except Exception as e:
            logger.error(f"Error in document search: {str(e)}")
            document_chunks = []

        # STEP 2: Search Website Chunks  
        logger.info("=== STEP 2: SEARCHING WEBSITE CHUNKS (MEDIUM PRIORITY) ===")
        website_chunks = None
        website_answer = None
        website_sources = []
        
        # Initialize visual content tracking variables
        web_visual_found = False
        web_visual_types = []

        try:
            website_chunks = search_supabase_website_chunks(
                query_embedding=query_embedding,
                query_text=request.query,
                use_hybrid_search=request.use_hybrid_search
            )
            if website_chunks:
                logger.info(f"✅ STEP 2: Found {len(website_chunks)} relevant website chunks")
                for i, chunk in enumerate(website_chunks[:3], 1):
                    url = chunk.get('url', 'Unknown URL')
                    similarity = chunk.get('similarity', 0)
                    logger.info(f"   {i}. Similarity: {similarity:.3f} | {url}")
                    
                    # Log text preview for debugging
                    text_preview = chunk.get('text', '')[:100] + "..." if len(chunk.get('text', '')) > 100 else chunk.get('text', '')
                    logger.info(f"       Text preview: {text_preview}")
                
                # Generate clean website answer
                website_answer, website_sources, web_visual_found, web_visual_types = generate_clean_answer_with_sources(
                    query=request.query,
                    chunks=website_chunks,
                    source_type="website",
                    model_id=model_id,
                    extract_format=request.extract_format
                )
                logger.info(f"✅ Website answer generated with {len(website_sources)} sources")
            else:
                logger.info("⚠️ STEP 2: No relevant website chunks found")
        except Exception as e:
            logger.error(f"Error in website search: {str(e)}")
            website_chunks = []

        # STEP 3: STRICT PRIORITY ANSWER GENERATION
        logger.info("=== STEP 3: STRICT PRIORITY ANSWER GENERATION ===")

        # Clear combined sources for clean handling
        combined_sources = []

        # Generate document answer if document chunks are available
        if document_chunks:
            logger.info(f"📄 Generating document answer from {len(document_chunks)} chunks")
            try:
                document_answer, doc_sources, doc_visual_found, doc_visual_types = generate_clean_answer_with_sources(
                    query=request.query,
                    chunks=document_chunks,
                    source_type="document",
                    model_id=model_id,
                    extract_format=request.extract_format
                )
                        
                # CRITICAL CHECK: Only use if we actually got sources
                if doc_sources and len(doc_sources) > 0:
                    document_sources = doc_sources
                    logger.info(f"✅ FIXED: Document answer generated with {len(doc_sources)} TRUE sources")
                else:
                    logger.info(f"❌ FIXED: Document answer generated but no sources - chunks were not actually relevant")
                    document_answer = None
                    document_sources = []

            except Exception as e:
                logger.error(f"Error generating document answer: {str(e)}")
                document_answer = None
                document_sources = []
        else:
            document_sources = []

        # Generate website answer if website chunks are available
        if website_chunks:
            logger.info(f"🌐 Generating website answer from {len(website_chunks)} chunks")
            try:
                website_answer, web_sources, web_visual_found, web_visual_types = generate_clean_answer_with_sources(
                    query=request.query,
                    chunks=website_chunks,
                    source_type="website",
                    model_id=model_id,
                    extract_format=request.extract_format
                )
                website_sources = web_sources
                combined_sources.extend(web_sources)
                logger.info(f"✅ Website answer generated with {len(web_sources)} sources")

                # If no sources were generated, it means the chunks were irrelevant
                if not web_sources:
                    logger.info(f"🌐 Website chunks were irrelevant - no sources generated")
                    website_answer = None

            except Exception as e:
                logger.error(f"Error generating website answer: {str(e)}")
                website_answer = None
                website_sources = []
        else:
            website_sources = []

        # STEP 4: DETERMINE FINAL RESPONSE STRATEGY (FIXED LOGIC)
        logger.info("=== STEP 4: APPLYING FIXED PRIORITY LOGIC ===")

        # Check if we actually have RELEVANT content (not just any content)
        relevant_document_chunks = []
        relevant_website_chunks = []

        # FIXED: Filter documents by actual relevance (similarity > 0.35) 
        if document_chunks:
            relevant_document_chunks = document_chunks  # Pass all chunks to answer logic
            logger.info(f"Found {len(relevant_document_chunks)} document chunks to process (no pre-filtering)")
            similarities = [chunk.get('similarity', 0) for chunk in document_chunks[:5]]
            logger.info(f"Document chunk similarities: {similarities}")

        # Filter websites by actual relevance - HANDLE NEGATIVE SIMILARITIES
        if website_chunks:
            # For website chunks, we need to handle negative similarities properly
            # Sort by similarity (highest first) and take top chunks regardless of threshold
            sorted_website_chunks = sorted(website_chunks, key=lambda x: x.get('similarity', -999), reverse=True)

            # Take top 5 website chunks regardless of similarity score
            relevant_website_chunks = sorted_website_chunks[:5]

            logger.info(f"Found {len(relevant_website_chunks)} RELEVANT website chunks out of {len(website_chunks)} total (taking top 5)")

            # Log similarities for debugging
            if website_chunks:
                similarities = [chunk.get('similarity', 0) for chunk in website_chunks[:5]]
                logger.info(f"Website chunk similarities: {similarities}")

                # If all similarities are very negative (< -0.5), try text-based matching
                if all(sim < -0.5 for sim in similarities):
                    logger.info("All website chunks have very negative similarities, trying text-based fallback")
                    text_matched_chunks = []
                    query_words = set(request.query.lower().split())

                    for chunk in website_chunks:
                        chunk_text = chunk.get('text', '').lower()
                        # Check if chunk contains any query words
                        chunk_words = set(chunk_text.split())
                        common_words = query_words & chunk_words

                        if len(common_words) >= 1:  # At least one word match
                            chunk_copy = dict(chunk)
                            chunk_copy['similarity'] = 0.2  # Assign minimal similarity
                            text_matched_chunks.append(chunk_copy)

                    if text_matched_chunks:
                        logger.info(f"Text-based fallback found {len(text_matched_chunks)} website chunks")
                        relevant_website_chunks = text_matched_chunks[:5]  # Limit to 5

        # Check if we found any website chunks with the vector search
        if not relevant_website_chunks and website_chunks:
            logger.info("No relevant website chunks found with vector search, trying text-based fallback")

            # Try direct text matching for website chunks
            text_matched_chunks = []
            query_terms = request.query.lower().split()

            for chunk in website_chunks:
                chunk_text = chunk.get('text', '').lower()
                # Count how many query terms appear in the text
                matched_terms = sum(1 for term in query_terms if term in chunk_text)

                # If at least one term matches, include this chunk
                if matched_terms > 0:
                    # Create a copy with calculated similarity based on term matches
                    chunk_copy = dict(chunk)
                    # Calculate similarity as proportion of matching terms (normalized to 0.1-0.5 range)
                    term_similarity = 0.1 + (0.4 * matched_terms / len(query_terms))
                    chunk_copy['similarity'] = term_similarity
                    text_matched_chunks.append(chunk_copy)

            if text_matched_chunks:
                logger.info(f"Text-based fallback found {len(text_matched_chunks)} website chunks")
                # Sort by calculated similarity
                text_matched_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)
                # Use top 5 results
                relevant_website_chunks = text_matched_chunks[:5]
            else:
                logger.info("Text-based fallback also found no relevant website chunks")
                # Don't force irrelevant chunks - let it fall back to LLM instead
                relevant_website_chunks = []

        # PRIORITY LOGIC: Only use sources that are actually relevant AND have valid answers

        # FIXED: Check if we actually have RELEVANT content based on stricter thresholds
        has_relevant_docs = document_sources and len(document_sources) > 0 and document_answer
        has_relevant_websites = website_sources and len(website_sources) > 0 and website_answer

        # NEW LOGIC: Only show cards for ACTUALLY relevant content
        if has_relevant_docs and has_relevant_websites:
            # Both are relevant - show both cards
            answer_source = "document_and_website"
            llm_fallback_used = False
            combined_answer = document_answer or "Answer generated from document and website sources."
            combined_sources = document_sources + website_sources  # Combine all sources
            logger.info(f"🟡 BOTH RELEVANT: Document and website answers available - {len(document_sources)} document sources, {len(website_sources)} website sources")

        elif has_relevant_docs and not has_relevant_websites:
            # Only documents are relevant - show only document card
            answer_source = "document_only"
            llm_fallback_used = False
            combined_answer = document_answer or "Answer generated from document sources."
            combined_sources = document_sources  # Use only document sources
            logger.info(f"🔵 DOCUMENTS ONLY: Document answer - {len(document_sources)} sources")

            # Clear irrelevant website data
            website_answer = None
            website_sources = []

        elif has_relevant_websites and not has_relevant_docs:
            # Only websites are relevant - show only website card
            answer_source = "website_only"
            llm_fallback_used = False
            combined_answer = website_answer or "Answer generated from website sources."
            combined_sources = website_sources  # Use only website sources
            logger.info(f"🟢 WEBSITES ONLY: Website answer - {len(website_sources)} sources")

            # Clear irrelevant document data
            document_answer = None
            document_sources = []

        elif request.fallback_enabled:
            # PRIORITY 3: LLM FALLBACK - Only when no relevant chunks found
            answer_source = "llm_fallback"
            llm_fallback_used = True

            logger.info(f"🟣 PRIORITY 3: Using LLM fallback - no relevant content found")

            # Generate LLM-only answer
            fallback_prompt = f"""You are RailGPT, an AI assistant that specializes in Indian Railways.
            The user's question could not be answered from the uploaded documents or extracted websites.
            Please provide a helpful answer using your general knowledge.
            If the question is about Indian Railways, provide detailed technical information.
            Format your answer clearly with paragraphs and bullet points where appropriate.

            USER QUESTION: {request.query}

            Provide a comprehensive answer."""

            try:
                combined_answer = llm_router.generate_answer(
                    query=request.query,
                    context="",  # No context for pure LLM fallback
                    system_prompt=fallback_prompt,
                    model_id=model_id
                )
            except Exception as e:
                logger.error(f"LLM fallback failed: {str(e)}")
                combined_answer = "I'm sorry, I couldn't process your question. Please try again with a different model or rephrase your question."

            # Clear all sources for LLM fallback
            combined_sources = []
            document_sources = []
            website_sources = []
            document_answer = None
            website_answer = None

        else:
            # PRIORITY 5: FALLBACK DISABLED - No results found
            answer_source = "no_results"
            llm_fallback_used = False

            logger.info("❌ PRIORITY: Fallback disabled and no relevant chunks found")
            combined_answer = "No relevant information was found in the uploaded documents or extracted websites. Please try enabling the LLM fallback option or uploading more relevant documents."

            combined_sources = []
            document_sources = []
            website_sources = []
            document_answer = None
            website_answer = None

        # Add model fallback information if needed
        if used_fallback_model and fallback_reason:
            model_info = f"Note: Using {model_id} instead of {original_model}. {fallback_reason}."
            if combined_answer:
                combined_answer = f"{model_info}\n\n{combined_answer}"

        # Log final results for debugging
        logger.info(f"=== SEARCH COMPLETE ===")
        logger.info(f"Answer source: {answer_source}")
        logger.info(f"Document chunks: {len(document_chunks) if document_chunks else 0}")
        logger.info(f"Website chunks: {len(website_chunks) if website_chunks else 0}")
        logger.info(f"LLM fallback used: {llm_fallback_used}")
        logger.info(f"Document sources: {len(document_sources)}")
        logger.info(f"Website sources: {len(website_sources)}")

        # Track visual content from all sources
        overall_visual_found = False
        overall_visual_types = []
        
        # Track visual content from document sources
        if 'doc_visual_found' in locals() and doc_visual_found:
            overall_visual_found = True
            overall_visual_types.extend(doc_visual_types or [])
            
        # Track visual content from website sources  
        if 'web_visual_found' in locals() and web_visual_found:
            overall_visual_found = True
            overall_visual_types.extend(web_visual_types or [])
            
        # Remove duplicates from visual types
        overall_visual_types = list(set(overall_visual_types))
        
        logger.info(f"Overall visual content found: {overall_visual_found}, types: {overall_visual_types}")

        return QueryResponse(
            answer=combined_answer,
            document_answer=document_answer,
            website_answer=website_answer,
            sources=combined_sources,
            document_sources=document_sources,
            website_sources=website_sources,
            llm_model=model_id,
            llm_fallback=llm_fallback_used,
            visual_content_found=overall_visual_found,
            visual_content_types=overall_visual_types if overall_visual_types else None
        )

    except Exception as e:
        logger.error(f"Error processing query: {str(e)}")
        return QueryResponse(
            answer=f"Sorry, an error occurred while processing your query: {str(e)}",
            sources=[],
            document_sources=[],
            website_sources=[],
            llm_model=llm_router.DEFAULT_MODEL,
            llm_fallback=True,
            visual_content_found=False,
            visual_content_types=None
        )

# Document upload endpoint
@app.post("/api/upload-document")
async def upload_document(
    file: UploadFile = File(...),
    uploaded_by: Optional[str] = Form(None),
    role: Optional[str] = Form(None),
    supabase_file_path: Optional[str] = Form(None),
    supabase_file_url: Optional[str] = Form(None),
    extract_tables: Optional[bool] = Form(True),
    extract_images: Optional[bool] = Form(True),
    extract_charts: Optional[bool] = Form(True),
    # 4-level category hierarchy
    main_category_id: Optional[str] = Form(None),
    category_id: Optional[str] = Form(None),
    sub_category_id: Optional[str] = Form(None),
    minor_category_id: Optional[str] = Form(None)
):
    # Enhanced logging for troubleshooting upload issues
    logger.info(f"===== DOCUMENT UPLOAD STARTED =====")
    logger.info(f"Received document: {file.filename}, size: {file.size if hasattr(file, 'size') else 'unknown'} bytes")
    logger.info(f"Uploaded by: {uploaded_by}, role: {role}")
    logger.info(f"Supabase file path: {supabase_file_path}")
    logger.info(f"Received document upload request: {file.filename}")

    # Check if the file has content
    if not file.filename:
        raise HTTPException(status_code=400, detail="File has no filename")

    try:
        # Create uploads directory if it doesn't exist
        uploads_dir = os.path.join("data", "uploads")
        os.makedirs(uploads_dir, exist_ok=True)
        logger.info(f"Using uploads directory: {os.path.abspath(uploads_dir)}")
        
        # Check if a document with the same name already exists and delete its chunks
        try:
            # Use proper SQL escaping for filename
            escaped_filename = file.filename.replace("'", "''")
            logger.info(f"Checking if document '{escaped_filename}' already exists")
            
            # Query to find documents with matching filename or display_name
            existing_doc_query = f"""
            SELECT id, display_name 
            FROM documents 
            WHERE display_name = '{escaped_filename}' 
               OR file_path LIKE '%{escaped_filename}%'
            """
            
            existing_docs = supabase.execute_query(existing_doc_query)
            
            if existing_docs and len(existing_docs) > 0:
                for doc in existing_docs:
                    doc_id = doc.get('id')
                    doc_name = doc.get('display_name')
                    logger.info(f"Found existing document: {doc_name} (id: {doc_id}). Deleting old chunks...")
                    
                    # Delete existing chunks for this document
                    delete_chunks_query = f"DELETE FROM document_chunks WHERE document_id = '{doc_id}'"
                    supabase.execute_query(delete_chunks_query)
                    logger.info(f"Deleted old chunks for document {doc_id}")
        except Exception as cleanup_error:
            logger.warning(f"Error while cleaning up existing document chunks: {str(cleanup_error)}")
            # Continue with upload even if cleanup fails
        
        # Save the file to the uploads directory
        file_path = os.path.join(uploads_dir, file.filename)
        logger.info(f"Saving file to: {os.path.abspath(file_path)}")

        # Read the content before saving to get the actual file size
        content = await file.read()
        file_size = len(content)
        logger.info(f"Received file content size: {file_size} bytes")

        # Reset file position and save
        await file.seek(0)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        logger.info(f"File saved to {file_path}")

        # Process the document to extract content including visual elements
        # Pass Supabase file path if available
        try:
            logger.info(f"Starting document extraction from {file_path}")
            logger.info(f"Visual extraction options - Tables: {extract_tables}, Images: {extract_images}, Charts: {extract_charts}")

            # Use visual extraction for PDFs, regular extraction for other files
            _, ext = os.path.splitext(file_path)
            if ext.lower() == '.pdf' and (extract_tables or extract_images or extract_charts):
                document_chunks = extract_document_with_visual_content(
                    file_path=file_path,
                    supabase_file_path=supabase_file_path,
                    uploaded_by=uploaded_by,
                    extract_tables=extract_tables,
                    extract_images=extract_images,
                    extract_charts=extract_charts,
                    main_category_id=main_category_id,
                    category_id=category_id,
                    sub_category_id=sub_category_id,
                    minor_category_id=minor_category_id
                                )
            else:
                document_chunks = extract_document(
                    file_path=file_path,
                    supabase_file_path=supabase_file_path,
                    uploaded_by=uploaded_by,
                    main_category_id=main_category_id,
                    category_id=category_id,
                    sub_category_id=sub_category_id,
                    minor_category_id=minor_category_id
                )
            logger.info(f"Document extraction completed with {len(document_chunks)} chunks")
        except Exception as extract_error:
            logger.error(f"Error during document extraction: {str(extract_error)}")
            import traceback
            logger.error(f"Extraction error traceback: {traceback.format_exc()}")

            # Simple fallback extraction
            logger.info("Attempting fallback extraction...")
            try:
                # Simple text extraction fallback
                with open(file_path, 'rb') as f:
                    # Try to read the file as text
                    sample_content = f.read(4096)  # Read first 4KB to detect text

                is_binary = False
                try:
                    sample_content.decode('utf-8')
                except UnicodeDecodeError:
                    is_binary = True

                if not is_binary:
                    # Simple text extraction for text files
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        text = f.read()

                    # Create a minimal chunk
                    document_chunks = [{
                        "filename": os.path.basename(file_path),
                        "text": text,
                        "source_type": "document",
                        "page": 1,
                        "chunk_id": f"{os.path.splitext(os.path.basename(file_path))[0]}_1_0"
                    }]
                    logger.info(f"Fallback extraction created {len(document_chunks)} simple chunks")
                else:
                    # Binary file we can't parse
                    logger.error("Cannot extract text from binary file using fallback method")
                    document_chunks = []
            except Exception as fallback_error:
                logger.error(f"Fallback extraction also failed: {str(fallback_error)}")
                document_chunks = []

        if not document_chunks:
            raise HTTPException(
                status_code=422,
                detail=f"Failed to extract content from {file.filename}"
            )

        # Get document_id from the first chunk if available (set by extract_document when using Supabase)
        document_id = None
        if document_chunks and "document_id" in document_chunks[0]:
            document_id = document_chunks[0]["document_id"]

        # Generate embeddings for each chunk
        for chunk in document_chunks:
            # Add additional metadata
            if uploaded_by:
                chunk["uploaded_by"] = uploaded_by
            if role:
                chunk["role"] = role
            if supabase_file_path:
                chunk["supabase_file_path"] = supabase_file_path
            if supabase_file_url:
                chunk["supabase_file_url"] = supabase_file_url

            # Add embedding
            chunk_text = chunk.get("text", "")
            embedding = generate_embedding(chunk_text)
            chunk["embedding"] = embedding

        # Store each chunk in Supabase database if we have a document_id
        if document_id:
            successful_chunks = 0
            for i, chunk in enumerate(document_chunks):
                # Store chunk in Supabase
                chunk_data = supabase.store_document_chunk(
                    document_id=document_id,
                    chunk_index=chunk.get("chunk_index", i),
                    page_number=chunk.get("page_number", chunk.get("page", 1)),
                    text=chunk["text"],
                    embedding=chunk["embedding"],
                    metadata=chunk.get("metadata", {}),
                    source_type=chunk.get("source_type", "document"),
                    chunk_type=chunk.get("chunk_type", "text"),
                    content_type=chunk.get("content_type", "text")
                )

                if "error" in chunk_data:
                    logger.error(f"Error storing document chunk {i}: {chunk_data['error']}")
                else:
                    successful_chunks += 1
                    logger.info(f"Successfully stored document chunk {i} with ID: {chunk_data.get('id')}")

            logger.info(f"Successfully stored {successful_chunks} out of {len(document_chunks)} document chunks in Supabase")

        # Add to global document chunks (legacy in-memory storage)
        DOCUMENT_CHUNKS.extend(document_chunks)

        # Add to vector database for efficient search
        vector_db.add_chunks(document_chunks)

        logger.info(f"Added {len(document_chunks)} document chunks to knowledge base and vector database")

        # Return chunks without embeddings
        chunks_without_embedding = []
        for chunk in document_chunks:
            chunk_copy = dict(chunk)
            if "embedding" in chunk_copy:
                del chunk_copy["embedding"]
            chunks_without_embedding.append(chunk_copy)

        result = {
            "message": f"Successfully processed {file.filename}",
            "chunks_extracted": len(chunks_without_embedding),
            "chunks": chunks_without_embedding,
            "document_id": document_id  # Return the document ID if available
        }
        logger.info(f"Document processing successful: {len(chunks_without_embedding)} chunks extracted, document_id: {document_id}")
        logger.info(f"===== DOCUMENT UPLOAD COMPLETED =====")
        return result

    except Exception as e:
        logger.error(f"Error processing uploaded document {file.filename}: {str(e)}")
        # Print more detailed exception information for debugging
        import traceback
        logger.error(f"Exception traceback: {traceback.format_exc()}")
        logger.info(f"===== DOCUMENT UPLOAD FAILED =====")
        raise HTTPException(status_code=500, detail=f"Error processing document: {str(e)}")
    finally:
        file.file.close()

# Website add endpoint
@app.post("/api/add-website")
async def add_website(request: WebsiteAddRequest):
    url = request.url
    submitted_by = request.submitted_by
    role = request.role

    logger.info(f"Received request to add website: {url}")

    if not url or not url.strip():
        raise HTTPException(status_code=400, detail="URL cannot be empty")

    try:
        # First, store the website metadata in Supabase
        from urllib.parse import urlparse
        parsed_url = urlparse(url)
        domain = parsed_url.netloc

        # Store website in Supabase with new category structure
        website_data = supabase.store_website(
            url=url,
            domain=domain,
            title=f"{domain} Website",
            description=f"Content extracted from {url}",
            submitted_by=submitted_by or "system",
            # Use new category IDs if provided, fallback to legacy names
            main_category=request.main_category_id or request.main_category,
            category_level2=request.category_id or request.category,
            sub_category=request.sub_category_id or request.sub_category,
            minor_category=request.minor_category_id or request.minor_category
        )

        if "error" in website_data:
            logger.error(f"Error storing website metadata: {website_data['error']}")
            raise HTTPException(status_code=500, detail=f"Error storing website metadata: {website_data['error']}")

        # Get the website ID
        website_id = website_data.get("id")
        if not website_id:
            logger.error("No website ID returned from Supabase")
            raise HTTPException(status_code=500, detail="No website ID returned from Supabase")

        logger.info(f"Successfully stored website with ID: {website_id}")

        # Extract text from website using fallback extraction methods
        website_chunks = extract_website_text(url)

        if not website_chunks:
            raise HTTPException(status_code=422, detail=f"Failed to extract content from {url}")

        # Generate embeddings for each chunk and add website_id
        for i, chunk in enumerate(website_chunks):
            # Add additional metadata
            if submitted_by:
                chunk["submitted_by"] = submitted_by
            if role:
                chunk["role"] = role

            # Add website_id to each chunk (required for vector_db.add_chunks)
            chunk["website_id"] = website_id
            chunk["chunk_index"] = i

            # Add source_type if not present
            if "source_type" not in chunk:
                chunk["source_type"] = "website"

            # Add embedding
            chunk_text = chunk.get("text", "")
            embedding = generate_embedding(chunk_text)
            chunk["embedding"] = embedding

            # Add metadata field if not present
            if "metadata" not in chunk:
                chunk["metadata"] = {
                    "url": url,
                    "domain": domain,
                    "extraction_method": chunk.get("extraction_method", "unknown")
                }

        # Add to global document chunks (legacy in-memory storage)
        DOCUMENT_CHUNKS.extend(website_chunks)

        # Store each chunk in Supabase directly
        successful_chunks = 0
        for i, chunk in enumerate(website_chunks):
            # Store chunk in Supabase
            chunk_data = supabase.store_website_chunk(
                website_id=website_id,
                chunk_index=i,
                text=chunk["text"],
                embedding=chunk["embedding"],
                metadata=chunk.get("metadata", {})
            )

            if "error" in chunk_data:
                logger.error(f"Error storing chunk {i}: {chunk_data['error']}")
            else:
                successful_chunks += 1
                logger.info(f"Successfully stored chunk {i} with ID: {chunk_data.get('id')}")

        logger.info(f"Successfully stored {successful_chunks} out of {len(website_chunks)} chunks in Supabase")

        # Add to vector database for efficient search
        vector_db.add_chunks(website_chunks, source_type="website")

        logger.info(f"Added {len(website_chunks)} website chunks to knowledge base and vector database")

        # Log vector database stats
        logger.info(f"Vector database stats: {vector_db.get_stats()}")

        # Return chunks without embeddings
        chunks_without_embedding = []
        for chunk in website_chunks:
            chunk_copy = dict(chunk)
            if "embedding" in chunk_copy:
                del chunk_copy["embedding"]
            chunks_without_embedding.append(chunk_copy)

        return {
            "message": f"Successfully added website {url}",
            "chunks_extracted": len(chunks_without_embedding),
            "chunks": chunks_without_embedding,
            "website_id": website_id
        }

    except Exception as e:
        logger.error(f"Error adding website {url}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error adding website: {str(e)}")
# Document extraction details endpoint
@app.get("/api/documents/{document_id}/extraction-details")
async def get_document_extraction_details(document_id: str):
    logger.info(f"Fetching extraction details for document ID: {document_id}")

    try:
        # First try to get the document info from the database
        document_query = """
        SELECT id, display_name, extracted_content, file_type, created_at, metadata
        FROM documents 
        WHERE id = %s
        """
        
        document_result = supabase.execute_query(document_query, [document_id])
        
        if not document_result or len(document_result) == 0:
            raise HTTPException(status_code=404, detail="Document not found")
        
        document = document_result[0]
        display_name = document.get('display_name', 'Unknown Document')
        extracted_content = document.get('extracted_content')
        file_type = document.get('file_type', 'unknown')
        metadata = document.get('metadata', {})
        
        # If we have extracted content in the document record, use it for preview
        if extracted_content and extracted_content.strip():
            # Show a preview of the extracted content
            content_preview = extracted_content[:1000] + ("..." if len(extracted_content) > 1000 else "")
        else:
            # Otherwise, get content preview from document chunks
            chunks_query = """
            SELECT text, page_number, chunk_index
            FROM document_chunks 
            WHERE document_id = %s 
            ORDER BY page_number, chunk_index
            LIMIT 3
            """
            
            chunks_result = supabase.execute_query(chunks_query, [document_id])
            
            if chunks_result and len(chunks_result) > 0:
                # Combine first few chunks as sample content for extraction details
                content_preview = f"# {display_name}\n\n"
                
                for i, chunk in enumerate(chunks_result[:3]):
                    chunk_text = chunk.get('text', '')
                    page_num = chunk.get('page_number')
                    if page_num:
                        content_preview += f"**Page {page_num}:**\n"
                    content_preview += chunk_text[:300] + ("..." if len(chunk_text) > 300 else "") + "\n\n"
                
                content_preview = content_preview.strip()
            else:
                content_preview = f"# {display_name}\n\nNo extracted content available for this document."
        
        # Get chunks count and metadata
        chunks_count_query = """
        SELECT COUNT(*) as count
        FROM document_chunks 
        WHERE document_id = %s
        """
        
        chunks_count_result = supabase.execute_query(chunks_count_query, [document_id])
        chunks_count = 0
        try:
            if chunks_count_result and len(chunks_count_result) > 0:
                if isinstance(chunks_count_result[0], dict) and 'count' in chunks_count_result[0]:
                    chunks_count = chunks_count_result[0]['count']
                else:
                    chunks_count = len(chunks_count_result)
        except Exception as e:
            logger.error(f"Error parsing count result: {str(e)}")
            chunks_count = 0
        
        # Calculate quality score based on available data
        quality_score = 85
        if extracted_content and len(extracted_content) > 1000:
            quality_score = 95
        elif chunks_count > 5:
            quality_score = 90
        
        extraction_method = "Document Processing Pipeline"
        if file_type.lower() == 'pdf':
            extraction_method = "PyMuPDF + NLP Processing"
        elif file_type.lower() in ['doc', 'docx']:
            extraction_method = "Python-docx + NLP Processing"
        
        return {
            "extractedContent": content_preview,
            "extractionMethod": extraction_method,
            "qualityScore": quality_score,
            "processingTime": metadata.get('processing_time', 0),
            "chunks": chunks_count,
            "warnings": metadata.get('warnings', []),
            "fallbackReason": metadata.get('fallback_reason', ""),
            "document_name": display_name,
            "file_type": file_type
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching document extraction details: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Error fetching document extraction details: {str(e)}"
        )

# Document content endpoint
@app.get("/api/documents/{document_id}/content")
async def get_document_content(document_id: str):
    logger.info(f"Fetching content for document ID: {document_id}")

    try:
        # First try to get the document info from the database
        document_query = """
        SELECT id, display_name, extracted_content, file_type, created_at
        FROM documents 
        WHERE id = %s
        """
        
        document_result = supabase.execute_query(document_query, [document_id])
        
        if not document_result or len(document_result) == 0:
            raise HTTPException(status_code=404, detail="Document not found")
        
        document = document_result[0]
        display_name = document.get('display_name', 'Unknown Document')
        extracted_content = document.get('extracted_content')
        
        # If we have extracted content in the document record, use it
        if extracted_content and extracted_content.strip():
            content = extracted_content
        else:
            # Otherwise, get content from document chunks
            chunks_query = """
            SELECT text, page_number, chunk_index
            FROM document_chunks 
            WHERE document_id = %s 
            ORDER BY page_number, chunk_index
            """
            
            chunks_result = supabase.execute_query(chunks_query, [document_id])
            
            if chunks_result and len(chunks_result) > 0:
                # Combine all chunks into a single content string
                content = f"# {display_name}\n\n"
                current_page = None
                
                for chunk in chunks_result:
                    page_num = chunk.get('page_number')
                    chunk_text = chunk.get('text', '')
                    
                    # Add page separator if page changes
                    if page_num != current_page and page_num is not None:
                        if current_page is not None:
                            content += f"\n\n---\n\n"
                        content += f"## Page {page_num}\n\n"
                        current_page = page_num
                    
                    content += chunk_text + "\n\n"
                
                # Remove excessive newlines
                content = content.strip()
            else:
                content = f"# {display_name}\n\nNo extracted content available for this document."
        
        # Get additional metadata
        chunks_count_query = """
        SELECT COUNT(*) as count
        FROM document_chunks 
        WHERE document_id = %s
        """
        
        chunks_count_result = supabase.execute_query(chunks_count_query, [document_id])
        try:
            if chunks_count_result and len(chunks_count_result) > 0:
                if isinstance(chunks_count_result[0], dict) and 'count' in chunks_count_result[0]:
                    chunks_count = chunks_count_result[0]['count']
                else:
                    chunks_count = len(chunks_count_result)
            else:
                chunks_count = 0
        except Exception as e:
            logger.error(f"Error parsing count result: {str(e)}")
            chunks_count = 0
        
        return {
            "content": content,
            "extraction_method": "Document Processing Pipeline",
            "quality_score": 95,  # You can calculate this based on content quality
            "processing_time": 0,  # This would be stored during processing
            "chunks_count": chunks_count,
            "document_name": display_name
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching document content: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Error fetching document content: {str(e)}"
        )

# Website extraction details endpoint
@app.get("/api/websites/{website_id}/extraction-details")
async def get_website_extraction_details(website_id: str):
    logger.info(f"Fetching extraction details for website ID: {website_id}")

    try:
        # First try to get the website info from the database
        website_query = """
        SELECT id, url, domain, extracted_content, created_at, metadata
        FROM websites 
        WHERE id = %s
        """
        
        website_result = supabase.execute_query(website_query, [website_id])
        
        if not website_result or len(website_result) == 0:
            raise HTTPException(status_code=404, detail="Website not found")
        
        website = website_result[0]
        url = website.get('url', 'Unknown URL')
        domain = website.get('domain', 'Unknown Domain')
        extracted_content = website.get('extracted_content')
        metadata = website.get('metadata', {})
        
        # If we have extracted content in the website record, use it
        if extracted_content and extracted_content.strip():
            content = extracted_content
        else:
            # Otherwise, get content from website chunks
            chunks_query = """
            SELECT text, chunk_index, metadata
            FROM website_chunks 
            WHERE website_id = %s 
            ORDER BY chunk_index
            LIMIT 3
            """
            
            chunks_result = supabase.execute_query(chunks_query, [website_id])
            
            if chunks_result and len(chunks_result) > 0:
                # Combine first few chunks as sample content for extraction details
                content = f"# Website Content: {domain}\n"
                content += f"**URL:** {url}\n\n"
                
                for i, chunk in enumerate(chunks_result[:3]):
                    chunk_text = chunk.get('text', '')
                    content += chunk_text[:500] + ("..." if len(chunk_text) > 500 else "") + "\n\n"
                
                # Remove excessive newlines
                content = content.strip()
            else:
                content = f"# Website Content: {domain}\n**URL:** {url}\n\nNo extracted content available for this website."
        
        # Get chunks count
        chunks_count_query = """
        SELECT COUNT(*) as count
        FROM website_chunks 
        WHERE website_id = %s
        """
        
        chunks_count_result = supabase.execute_query(chunks_count_query, [website_id])
        try:
            if chunks_count_result and len(chunks_count_result) > 0:
                if isinstance(chunks_count_result[0], dict) and 'count' in chunks_count_result[0]:
                    chunks_count = chunks_count_result[0]['count']
                else:
                    chunks_count = len(chunks_count_result)
            else:
                chunks_count = 0
        except Exception as e:
            logger.error(f"Error parsing count result: {str(e)}")
            chunks_count = 0
        
        return {
            "extractedContent": content,
            "extractionMethod": "Web Scraping (Trafilatura)",
            "fallbackHistory": ["Trafilatura"],
            "contentQuality": 90,  # You can calculate this based on content quality
            "warnings": [],
            "processingTime": 0,  # This would be stored during processing
            "chunks": chunks_count,
            "website_url": url,
            "website_domain": domain
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching website extraction details: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Error fetching website extraction details: {str(e)}"
        )

# Website content endpoint
@app.get("/api/websites/{website_id}/content")
async def get_website_content(website_id: str):
    logger.info(f"Fetching content for website ID: {website_id}")

    try:
        # First try to get the website info from the database
        website_query = """
        SELECT id, url, domain, extracted_content, created_at
        FROM websites 
        WHERE id = %s
        """
        
        website_result = supabase.execute_query(website_query, [website_id])
        
        if not website_result or len(website_result) == 0:
            raise HTTPException(status_code=404, detail="Website not found")
        
        website = website_result[0]
        url = website.get('url', 'Unknown URL')
        domain = website.get('domain', 'Unknown Domain')
        extracted_content = website.get('extracted_content')
        
        # If we have extracted content in the website record, use it
        if extracted_content and extracted_content.strip():
            content = extracted_content
        else:
            # Otherwise, get content from website chunks
            chunks_query = """
            SELECT text, chunk_index, metadata
            FROM website_chunks 
            WHERE website_id = %s 
            ORDER BY chunk_index
            """
            
            chunks_result = supabase.execute_query(chunks_query, [website_id])
            
            if chunks_result and len(chunks_result) > 0:
                # Combine all chunks into a single content string
                content = f"# Website Content: {domain}\n"
                content += f"**URL:** {url}\n\n"
                
                for chunk in chunks_result:
                    chunk_text = chunk.get('text', '')
                    content += chunk_text + "\n\n"
                
                # Remove excessive newlines
                content = content.strip()
            else:
                content = f"# Website Content: {domain}\n**URL:** {url}\n\nNo extracted content available for this website."
        
        # Get additional metadata
        chunks_count_query = """
        SELECT COUNT(*) as count
        FROM website_chunks 
        WHERE website_id = %s
        """
        
        chunks_count_result = supabase.execute_query(chunks_count_query, [website_id])
        try:
            if chunks_count_result and len(chunks_count_result) > 0:
                if isinstance(chunks_count_result[0], dict) and 'count' in chunks_count_result[0]:
                    chunks_count = chunks_count_result[0]['count']
                else:
                    chunks_count = len(chunks_count_result)
            else:
                chunks_count = 0
        except Exception as e:
            logger.error(f"Error parsing count result: {str(e)}")
            chunks_count = 0
        
        return {
            "content": content,
            "extraction_method": "Website Scraping Pipeline",
            "quality_score": 95,  # You can calculate this based on content quality
            "processing_time": 0,  # This would be stored during processing
            "pages_processed": 1,
            "total_links": chunks_count,
            "website_url": url,
            "website_domain": domain
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching website content: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Error fetching website content: {str(e)}"
        )

# Feedback endpoints
@app.post("/api/feedback")
async def submit_feedback(feedback: FeedbackData):
    logger.info(f"Received feedback with issue type: {feedback.issue_type}")
    result = send_feedback_email(feedback)
    return result

@app.get("/api/feedback/emails")
async def get_feedback_notification_emails():
    emails = get_feedback_emails()
    logger.info(f"Retrieved feedback emails: {emails}")
    return {"emails": emails}

@app.post("/api/feedback/emails")
async def update_feedback_notification_emails(config: FeedbackEmailConfig):
    success = update_feedback_emails(config.emails)
    if success:
        return {"success": True, "message": "Feedback emails updated successfully"}
    else:
        raise HTTPException(status_code=400, message="Failed to update feedback emails")

# Debug endpoint to check document chunks
@app.get("/api/debug/document-chunks")
async def debug_document_chunks():
    """Debug endpoint to check document chunks in the database."""
    try:
        # Query to get all document chunks
        chunks_query = """
        SELECT
            dc.id,
            dc.document_id,
            dc.chunk_index,
            dc.page_number,
            dc.text,
            d.display_name as filename,
            d.file_path as url,
            'document' as source_type
        FROM
            document_chunks dc
        JOIN
            documents d ON dc.document_id = d.id
        LIMIT 10
        """

        result = supabase.execute_query(chunks_query)

        if isinstance(result, dict) and "error" in result:
            return {"error": result.get("error")}
        else:
            return {"document_chunks": result, "count": len(result)}
    except Exception as e:
        return {"error": str(e)}

# Debug endpoint to check website chunks
@app.get("/api/debug/website-chunks")
async def debug_website_chunks():
    """Debug endpoint to check website chunks in the database."""
    try:
        # Query to get all website chunks
        chunks_query = """
        SELECT
            wc.id,
            wc.website_id,
            wc.chunk_index,
            wc.text,
            wc.metadata,
            w.url,
            w.domain,
            'website' as source_type
        FROM
            website_chunks wc
        JOIN
            websites w ON wc.website_id = w.id
        LIMIT 20
        """

        result = supabase.execute_query(chunks_query)

        if isinstance(result, dict) and "error" in result:
            return {"error": result.get("error")}
        else:
            return {"website_chunks": result, "count": len(result)}
    except Exception as e:
        return {"error": str(e)}

# Debug endpoint to test PDF extraction from URL
@app.get("/api/debug/extract-pdf")
async def debug_extract_pdf(url: str):
    """Debug endpoint to test PDF extraction from a URL."""
    try:
        from website_scraper import extract_pdf_from_url, extract_website_text

        # First try direct PDF extraction
        pdf_text = extract_pdf_from_url(url)

        # Then try the full website extraction
        website_chunks = extract_website_text(url)

        # Remove embeddings for response size
        for chunk in website_chunks:
            if "embedding" in chunk:
                del chunk["embedding"]

        return {
            "url": url,
            "pdf_text_length": len(pdf_text) if pdf_text else 0,
            "pdf_text_sample": pdf_text[:500] + "..." if pdf_text and len(pdf_text) > 500 else pdf_text,
            "website_chunks_count": len(website_chunks),
            "website_chunks_sample": website_chunks[:2] if website_chunks else []
        }
    except Exception as e:
        return {"error": str(e), "url": url}

# Debug endpoint to test search with a specific query
@app.get("/api/debug/search")
async def debug_search(query: str):
    """Debug endpoint to test search with a specific query."""
    try:
        # Generate embedding for the query
        query_embedding = generate_embedding(query)

        # Extract key terms for better search
        import re
        from string import punctuation

        # Clean the query and extract key terms
        clean_query = re.sub(r'[' + punctuation + ']', ' ', query.lower())
        words = clean_query.split()
        stop_words = {'what', 'is', 'the', 'of', 'in', 'on', 'at', 'and', 'or', 'for', 'to', 'a', 'an', 'by', 'with', 'about'}
        key_terms = [word for word in words if word not in stop_words and len(word) > 2]

        # If no key terms found, use the original query
        if not key_terms:
            key_terms = [query]

        # Direct document search results
        title_results = []
        for term in key_terms:
            results = search_documents_by_title(term)
            title_results.extend(results)

        # Remove duplicates
        unique_ids = set()
        unique_title_results = []
        for doc in title_results:
            doc_id = doc.get("id")
            if doc_id and doc_id not in unique_ids:
                unique_ids.add(doc_id)
                unique_title_results.append(doc)

        title_results = unique_title_results

        # Get chunks for each document found by title
        direct_document_chunks = []
        for doc in title_results:
            doc_id = doc.get("id")
            if doc_id:
                chunks = get_document_chunks(doc_id)
                direct_document_chunks.extend(chunks)

        # Search for documents by content
        content_results = search_documents_by_content(query)

        # Vector search results
        vector_document_chunks = search_supabase_document_chunks(
            query_embedding=query_embedding,
            query_text=query,
            use_hybrid_search=True,
            top_k=5,
            min_threshold=0.0001
        )

        # Direct website search results
        direct_website_chunks = []
        for term in key_terms:
            # Sanitize the term
            sanitized_term = term.replace("'", "''")

            # Query to search website chunks by content
            website_query = f"""
            SELECT
                wc.id,
                wc.website_id,
                wc.chunk_index,
                wc.text,
                wc.url,
                w.name as website_name,
                'website' as source_type,
                ts_rank(to_tsvector('english', wc.text), plainto_tsquery('english', '{sanitized_term}')) AS similarity
            FROM
                website_chunks wc
            JOIN
                websites w ON wc.website_id = w.id
            WHERE
                to_tsvector('english', wc.text) @@ plainto_tsquery('english', '{sanitized_term}')
                OR wc.text ILIKE '%{sanitized_term}%'
            ORDER BY
                similarity DESC
            LIMIT 5
            """

            result = supabase.execute_query(website_query)

            if not isinstance(result, dict) or "error" not in result:
                direct_website_chunks.extend(result)

        # Vector search for website chunks
        vector_website_chunks = search_supabase_website_chunks(
            query_embedding=query_embedding,
            query_text=query,
            use_hybrid_search=True,
            top_k=5,
            min_threshold=0.0001
        )

        # Remove embeddings for response size
        for chunk in vector_document_chunks:
            if "embedding" in chunk:
                del chunk["embedding"]

        for chunk in vector_website_chunks:
            if "embedding" in chunk:
                del chunk["embedding"]

        for chunk in direct_document_chunks:
            if "embedding" in chunk:
                del chunk["embedding"]

        for chunk in content_results:
            if "embedding" in chunk:
                del chunk["embedding"]

        for chunk in direct_website_chunks:
            if "embedding" in chunk:
                del chunk["embedding"]

        return {
            "query": query,
            "key_terms": key_terms,
            "direct_document_search": {
                "title_results": title_results,
                "title_count": len(title_results),
                "content_results": content_results,
                "content_count": len(content_results),
                "direct_document_chunks": direct_document_chunks,
                "direct_document_count": len(direct_document_chunks)
            },
            "vector_document_search": {
                "document_chunks": vector_document_chunks,
                "document_count": len(vector_document_chunks)
            },
            "direct_website_search": {
                "website_chunks": direct_website_chunks,
                "website_count": len(direct_website_chunks)
            },
            "vector_website_search": {
                "website_chunks": vector_website_chunks,
                "website_count": len(vector_website_chunks)
            }
        }
    except Exception as e:
        return {"error": str(e)}

def local_document_search(query_embedding, query_text=None, top_k=20, min_threshold=0.05):
    """
    Local document search using in-memory DOCUMENT_CHUNKS when Supabase is unavailable.
    Uses cosine similarity for vector search.
    """
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Using local document search with {len(DOCUMENT_CHUNKS)} chunks")

        if not DOCUMENT_CHUNKS:
            logger.warning("No document chunks available for local search")
            return []

        scored_chunks = []

        for chunk in DOCUMENT_CHUNKS:
            # Only process document chunks
            if chunk.get('source_type') != 'document':
                continue

            chunk_embedding = chunk.get('embedding')
            if not chunk_embedding:
                continue

            # Calculate similarity
            similarity = cosine_similarity(query_embedding, chunk_embedding)

            if similarity >= min_threshold:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = similarity
                chunk_copy['source_type'] = 'document'
                scored_chunks.append(chunk_copy)

        # Sort by similarity (highest first)
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)

        # Return top_k results
        result = scored_chunks[:top_k]
        logger.info(f"Local document search found {len(result)} results (threshold: {min_threshold})")

        return result

    except Exception as e:
        logger.error(f"Error in local document search: {str(e)}")
        return []

def local_website_search(query_embedding, query_text=None, top_k=20, min_threshold=0.01):
    """
    Local website search using in-memory DOCUMENT_CHUNKS when Supabase is unavailable.
    Uses cosine similarity for vector search.
    """
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Using local website search with {len(DOCUMENT_CHUNKS)} chunks")

        if not DOCUMENT_CHUNKS:
            logger.warning("No document chunks available for local search")
            return []

        scored_chunks = []

        for chunk in DOCUMENT_CHUNKS:
            # Only process website chunks
            if chunk.get('source_type') != 'website':
                continue

            chunk_embedding = chunk.get('embedding')
            if not chunk_embedding:
                continue

            # Calculate similarity
            similarity = cosine_similarity(query_embedding, chunk_embedding)

            if similarity >= min_threshold:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = similarity
                chunk_copy['source_type'] = 'website'
                scored_chunks.append(chunk_copy)

        # Sort by similarity (highest first)
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)

        # Return top_k results
        result = scored_chunks[:top_k]
        logger.info(f"Local website search found {len(result)} results (threshold: {min_threshold})")

        return result

    except Exception as e:
        logger.error(f"Error in local website search: {str(e)}")
        return []

def search_with_fallback(search_func, fallback_func, *args, **kwargs):
    """
    Attempt online search first, fallback to local search if it fails.
    """
    try:
        # Try online search first
        results = search_func(*args, **kwargs)
        if results and len(results) > 0:
            logger.info(f"Online search successful: {len(results)} results")
            return results
        else:
            logger.info("Online search returned no results, trying local fallback")
            return fallback_func(*args, **kwargs)
    except Exception as e:
        logger.warning(f"Online search failed: {str(e)}, trying local fallback")
        return fallback_func(*args, **kwargs)

# Start the server if this is the main module
if __name__ == "__main__":
    import uvicorn
    port = int(os.getenv("PORT", 8000))
    uvicorn.run(app, host="0.0.0.0", port=port)

# Debug endpoint to generate embeddings for all chunks
@app.post("/api/debug/generate-embeddings")
async def debug_generate_embeddings():
    """Debug endpoint to generate embeddings for all chunks in memory."""
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Starting embedding generation for {len(DOCUMENT_CHUNKS)} chunks")

        generated_count = 0
        failed_count = 0

        for i, chunk in enumerate(DOCUMENT_CHUNKS):
            chunk_id = chunk.get('id', f'chunk_{i}')

            # Check if embedding already exists
            if "embedding" in chunk and chunk["embedding"] and len(chunk["embedding"]) > 10:
                continue  # Skip if already has embedding

            # Generate embedding for chunk text
            chunk_text = chunk.get("text", "")
            if chunk_text:
                try:
                    chunk["embedding"] = generate_embedding(chunk_text)
                    generated_count += 1
                    logger.info(f"Generated embedding for chunk {chunk_id} ({generated_count}/{len(DOCUMENT_CHUNKS)})")
                except Exception as e:
                    logger.warning(f"Failed to generate embedding for chunk {chunk_id}: {str(e)}")
                    # Fallback to mock embedding
                    chunk["embedding"] = [0.01] * 768
                    failed_count += 1
            else:
                # No text, use mock embedding
                chunk["embedding"] = [0.01] * 768
                failed_count += 1

        logger.info(f"Embedding generation complete: {generated_count} generated, {failed_count} failed")

        return {
            "message": "Embedding generation completed",
            "total_chunks": len(DOCUMENT_CHUNKS),
            "generated": generated_count,
            "failed": failed_count
        }

    except Exception as e:
        logger.error(f"Error generating embeddings: {str(e)}")
        return {"error": str(e)}

def improved_text_based_document_search(query: str, top_k: int = 10, min_threshold: float = 0.7):
    """
    Improved text-based document search with VERY STRICT relevance requirements.
    Only matches queries that are actually related to railway content.
    """
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Using STRICT text-based document search for: '{query}' (threshold: {min_threshold})")

        # Extract key terms from query
        query_lower = query.lower()
        query_words = set(query_lower.split())

        # Railway domain keywords - MUST have at least one for relevance
        railway_keywords = {
            'railway', 'train', 'rail', 'station', 'track', 'locomotive', 'coach',
            'signal', 'platform', 'passenger', 'freight', 'engine', 'diesel',
            'fsds', 'monitoring', 'system', 'safety', 'maintenance', 'inspection',
            'indian', 'railways', 'irctc', 'booking', 'ticket', 'metro', 'suburban'
        }

        # Non-railway indicators - immediate disqualification
        non_railway_keywords = {
            'weather', 'temperature', 'climate', 'forecast', 'rain', 'sunny', 'cloudy',
            'movie', 'film', 'actor', 'music', 'song', 'sports', 'football', 'cricket',
            'restaurant', 'food', 'recipe', 'cooking', 'shopping', 'price', 'buy',
            'health', 'medicine', 'doctor', 'hospital', 'disease', 'symptoms',
            'politics', 'government', 'election', 'vote', 'party', 'minister'
        }

        # Check if query contains non-railway keywords - immediate rejection
        non_railway_in_query = non_railway_keywords & query_words
        if non_railway_in_query:
            logger.info(f"Query rejected: contains non-railway keywords {non_railway_in_query}")
            return []

        # Check if query has any railway context
        railway_in_query = railway_keywords & query_words
        if not railway_in_query:
            logger.info(f"Query rejected: no railway keywords found in query")
            return []

        scored_chunks = []

        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'document':
                continue

            chunk_text = chunk.get('text', '').lower()
            if not chunk_text:
                continue

            # Calculate relevance score with VERY STRICT requirements
            score = 0

            # 1. Exact phrase match (highest priority)
            if query_lower in chunk_text:
                score += 2.0

            # 2. Keyword matching - require MULTIPLE specific matches
            chunk_words = set(chunk_text.split())
            common_words = query_words & chunk_words
            if len(common_words) >= 2:  # Must have at least 2 words in common
                score += len(common_words) * 0.5

            # 3. Railway domain bonus - but document must also contain railway terms
            chunk_railway_matches = railway_keywords & chunk_words
            if chunk_railway_matches:
                score += len(chunk_railway_matches) * 0.3
            else:
                continue  # Skip if chunk has no railway content

            # 4. Special technical terms
            if 'fsds' in chunk_text:
                score += 1.0
            if 'monitoring' in chunk_text and 'system' in chunk_text:
                score += 0.5

            # 5. Only include if score meets threshold
            if score >= min_threshold:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = score
                scored_chunks.append(chunk_copy)

        # Sort by relevance score
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)

        result = scored_chunks[:top_k]
        logger.info(f"STRICT text-based document search found {len(result)} relevant chunks (threshold: {min_threshold})")

        # If no results found, log it clearly
        if not result:
            logger.info("No relevant document chunks found - query is not railway-related")

        return result

    except Exception as e:
        logger.error(f"Error in strict text-based document search: {str(e)}")
        return []

def improved_text_based_website_search(query: str, top_k: int = 10, min_threshold: float = 0.7):
    """
    Improved text-based website search with STRICT relevance requirements.
    Only matches queries related to transport/railway content.
    """
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Using STRICT text-based website search for: '{query}' (threshold: {min_threshold})")

        # Extract key terms from query
        query_lower = query.lower()
        query_words = set(query_lower.split())

        # Transport/railway domain keywords - MUST have at least one
        transport_keywords = {
            'railway', 'train', 'rail', 'station', 'transport', 'transportation',
            'public', 'safety', 'travel', 'passenger', 'metro', 'services',
            'ticket', 'booking', 'irctc', 'indian', 'railways'
        }

        # Non-transport indicators - immediate disqualification
        non_transport_keywords = {
            'weather', 'temperature', 'climate', 'forecast', 'rain', 'sunny', 'cloudy',
            'movie', 'film', 'actor', 'music', 'song', 'sports', 'football', 'cricket',
            'restaurant', 'food', 'recipe', 'cooking', 'shopping', 'price', 'buy',
            'health', 'medicine', 'doctor', 'hospital', 'disease', 'symptoms'
        }

        # Check if query contains non-transport keywords - immediate rejection
        non_transport_in_query = non_transport_keywords & query_words
        if non_transport_in_query:
            logger.info(f"Website query rejected: contains non-transport keywords {non_transport_in_query}")
            return []

        # Check if query has any transport context
        transport_in_query = transport_keywords & query_words
        if not transport_in_query:
            logger.info(f"Website query rejected: no transport keywords found in query")
            return []

        scored_chunks = []

        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'website':
                continue

            chunk_text = chunk.get('text', '').lower()
            if not chunk_text:
                continue

            # Calculate relevance score
            score = 0

            # 1. Exact phrase match (highest priority)
            if query_lower in chunk_text:
                score += 2.0

            # 2. Keyword matching - require multiple matches
            chunk_words = set(chunk_text.split())
            common_words = query_words & chunk_words
            if len(common_words) >= 2:  # Must have at least 2 words in common
                score += len(common_words) * 0.5

            # 3. Transport domain relevance - chunk must also contain transport terms
            chunk_transport_matches = transport_keywords & chunk_words
            if chunk_transport_matches:
                score += len(chunk_transport_matches) * 0.3
            else:
                continue  # Skip if chunk has no transport content

            # 4. Special boost for transport safety
            if 'transportation' in chunk_text and 'safety' in chunk_text:
                score += 0.5
            if 'public' in chunk_text and ('transport' in chunk_text or 'railway' in chunk_text):
                score += 0.5

            # 5. Only include if score meets threshold
            if score >= min_threshold:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = score
                scored_chunks.append(chunk_copy)

        # Sort by relevance score
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)

        result = scored_chunks[:top_k]
        logger.info(f"STRICT text-based website search found {len(result)} relevant chunks (threshold: {min_threshold})")

        # If no results found, log it clearly
        if not result:
            logger.info("No relevant website chunks found - query is not transport-related")

        return result

    except Exception as e:
        logger.error(f"Error in strict text-based website search: {str(e)}")
        return []


def generate_clean_answer_with_sources(query: str, chunks: List[Dict[str, Any]], source_type: str, model_id: str = "gemini-2.0-flash", extract_format: str = "paragraph"):
    """
    Generate a clean answer with properly formatted sources for a specific source type.
    ONLY tracks chunks that are actually used in the LLM prompt as sources.

    Args:
        query: The user's question
        chunks: List of chunks from a single source type (document OR website)
        source_type: Either "document" or "website"
        model_id: LLM model to use
        extract_format: Preferred format for the extraction

    Returns:
        Tuple of (answer_text, clean_sources_list, visual_content_found, visual_content_types)
    """
    try:
        logger.info(f"🔍 Generating clean {source_type} answer from {len(chunks)} total chunks")

        # STEP 1: Filter and rank chunks by relevance
        min_doc_threshold = 0.25   # FIXED: Increased threshold for documents
        min_web_threshold = 0.15  # FIXED: Increased threshold for websites
        min_threshold = min_doc_threshold if source_type == "document" else min_web_threshold

        # Filter chunks by relevance first
        relevant_chunks = []
        for chunk in chunks:
            similarity = chunk.get('similarity', 0.0)
            chunk_text = chunk.get('text', '').strip()

            # FIXED: Skip chunks with very low similarity, empty text, or insufficient content
            if similarity < min_threshold or not chunk_text or len(chunk_text) < 20:
                logger.info(f"⏭️  [FIXED] Skipping {source_type} chunk with similarity {similarity:.3f} (below threshold {min_threshold}) or insufficient text")
                continue

            relevant_chunks.append(chunk)

        # If no relevant chunks found, return empty result
        if not relevant_chunks:
            logger.info(f"❌ No relevant {source_type} chunks found above threshold {min_threshold}")
            return f"I couldn't find relevant information in the {source_type} content to answer your question.", [], False, []

        # STEP 2: Select TOP chunks to actually use in LLM prompt (CRITICAL CHANGE)
        # Only use the most relevant chunks, not all relevant chunks
        max_chunks_to_use = 4 if source_type == "document" else 3  # FIXED: Reduced chunks used
        
        # Sort by similarity and take top chunks
        relevant_chunks_sorted = sorted(relevant_chunks, key=lambda x: x.get('similarity', 0), reverse=True)
        chunks_to_use = relevant_chunks_sorted[:max_chunks_to_use]
        
        logger.info(f"📊 Using TOP {len(chunks_to_use)} chunks out of {len(relevant_chunks)} relevant chunks (out of {len(chunks)} total)")
        
        # Log which chunks we're actually using
        for i, chunk in enumerate(chunks_to_use, 1):
            similarity = chunk.get('similarity', 0)
            if source_type == "document":
                filename = chunk.get('filename', 'Unknown')
                page = chunk.get('page', 'Unknown')
                logger.info(f"   ✅ {i}. Using: {filename} (Page {page}) - Similarity: {similarity:.3f}")
            else:
                url = chunk.get('url', 'Unknown URL')
                logger.info(f"   ✅ {i}. Using: {url} - Similarity: {similarity:.3f}")

        # STEP 3: Build context ONLY from chunks we're actually using
        context_texts = []
        used_sources_tracker = {}  # FIXED: Track ONLY chunks actually used in LLM prompt
        visual_content_found = False
        visual_content_types = []

        for chunk in chunks_to_use:  # ONLY iterate over chunks we're using
            chunk_text = chunk.get("text", "")
            similarity = chunk.get("similarity", 0)

            if source_type == "document":
                # Document source processing
                document_id = chunk.get("document_id")
                # First try to get filename from document_id
                filename = None
                if document_id:
                    try:
                        from supabase_client import supabase
                        doc_query = f"SELECT COALESCE(display_name, file_name, name) as filename FROM documents WHERE id = '{document_id}'"
                        doc_result = supabase.execute_query(doc_query)
                        if doc_result and len(doc_result) > 0:
                            filename = doc_result[0]['filename']
                    except Exception as e:
                        logger.error(f"Error retrieving document name: {str(e)}")
                
                # Fall back to chunk metadata if needed
                if not filename:
                    filename = chunk.get("filename") or chunk.get("title") or "Unknown document"
                
                page = chunk.get("page") or chunk.get("page_number") or 1

                # Check for visual content
                metadata = chunk.get("metadata", {})
                content_type = metadata.get("content_type", "text")
                visual_content_type = metadata.get("visual_content_type")
                storage_url = metadata.get("storage_url")
                display_type = metadata.get("display_type", "text")

                # Track visual content
                if content_type != "text":
                    visual_content_found = True
                    if content_type not in visual_content_types:
                        visual_content_types.append(content_type)

                # Enhanced context for visual content
                if content_type in ["table", "image", "chart_diagram"]:
                    context_texts.append(f"From '{filename}' (page {page}, {content_type}, relevance: {similarity:.2f}):\n{chunk_text}\n")
                else:
                    context_texts.append(f"From '{filename}' (page {page}, relevance: {similarity:.2f}):\n{chunk_text}\n")

                # Create unique source key for deduplication (include content type for visual content)
                if content_type != "text":
                    source_key = f"{filename}_{content_type}_{page}"
                else:
                    source_key = f"{filename}"
                    
                logger.info(f"📝 [FIXED] Chunk {i+1}: Using '{filename}' page {page} (similarity: {similarity:.3f})")
                    
                if source_key not in used_sources_tracker:
                    used_sources_tracker[source_key] = {
                        "source_type": "document",
                        "filename": filename,
                        "name": os.path.basename(filename),
                        "pages": [],
                        "content_type": content_type,
                        "visual_content_type": visual_content_type,
                        "storage_url": storage_url,
                        "display_type": display_type,
                        "visual_content": format_visual_content_for_frontend(metadata, content_type) if content_type != "text" else None
                    }

                # Add page if not already present
                if page not in used_sources_tracker[source_key]["pages"]:
                    used_sources_tracker[source_key]["pages"].append(page)

            elif source_type == "website":
                # Website source processing - extract URL from multiple possible fields
                url = chunk.get("url", "")

                # If no URL field, try to extract from text
                if not url or url == "Unknown":
                    chunk_text_for_url = chunk.get("text", "")
                    # Look for URL patterns in the text
                    import re
                    url_match = re.search(r'URL:\s*(https?://[^\s\n]+)', chunk_text_for_url)
                    if url_match:
                        url = url_match.group(1)
                    else:
                        # Look for website domain patterns
                        domain_match = re.search(r'Website:\s*([^\s,\n]+)', chunk_text_for_url)
                        if domain_match:
                            domain = domain_match.group(1)
                            url = f"https://{domain}" if not domain.startswith('http') else domain
                        # Special case handling for known websites
                        elif "rapidresponseapp" in chunk_text_for_url.lower():
                            url = "https://www.rapidresponseapp.com"
                        elif "indianrailways.gov.in" in chunk_text_for_url.lower():
                            url = "https://indianrailways.gov.in"
                        elif "irctc" in chunk_text_for_url.lower():
                            url = "https://www.irctc.co.in"
                        else:
                            url = "Unknown website"

                # Add to context
                context_texts.append(f"From '{url}' (relevance: {similarity:.2f}):\n{chunk_text}\n")

                # Create unique source key for deduplication
                source_key = url
                if source_key not in used_sources_tracker:
                    used_sources_tracker[source_key] = {
                        "source_type": "website",
                        "url": url,
                        "title": chunk.get("title", "")
                    }

        # STEP 4: Generate answer using ONLY the selected chunks
        if not context_texts:
            logger.info(f"❌ No valid context generated from {source_type} chunks")
            return f"I couldn't extract meaningful information from the {source_type} content to answer your question.", [], visual_content_found, visual_content_types

        # Combine context
        combined_context = "\n".join(context_texts)
        
        logger.info(f"🤖 Generating {source_type} answer with context from {len(chunks_to_use)} chunks")
        logger.info(f"📊 Context length: {len(combined_context)} characters")

        # Generate the answer using only selected chunks
        answer = generate_llm_answer(
            query=query,
            similar_chunks=chunks_to_use,  # Pass only chunks actually being used
            model_id=model_id,
            extract_format=extract_format
        )

        if not answer or answer.strip() == "":
            logger.warning(f"⚠️  Empty answer generated for {source_type} query")
            return f"I couldn't generate a meaningful answer from the {source_type} content.", [], visual_content_found, visual_content_types

        # STEP 5: Build clean sources list from ONLY the chunks we used
        clean_sources = []
        
        for source_data in used_sources_tracker.values():
            if source_type == "document":
                # Calculate relevance score for this document source
                # Find the highest similarity chunk for this document among USED chunks
                max_similarity = 0
                for chunk in chunks_to_use:  # Only check chunks we actually used
                    if chunk.get("filename") == source_data["filename"]:
                        chunk_similarity = chunk.get("similarity", 0)
                        if chunk_similarity > max_similarity:
                            max_similarity = chunk_similarity

                # Sort pages and create page reference
                pages = sorted(source_data["pages"])
                
                # Create source entry
                source = Source(
                    source_type="document",
                    filename=source_data["filename"],
                    name=source_data["filename"],
                    page=pages[0] if pages else None,
                    content_type="text"
                )
                clean_sources.append(source)
                
            else:  # website
                # Create source entry for website
                source = Source(
                    source_type="website",
                    url=source_data["url"],
                    content_type="text"
                )
                clean_sources.append(source)

        logger.info(f"🎯 Final answer generated from {len(clean_sources)} {source_type} sources")
        return answer, clean_sources, visual_content_found, visual_content_types

    except Exception as e:
        logger.error(f"❌ Error in generate_clean_answer_with_sources: {str(e)}")
        return f"Error generating answer: {str(e)}", [], False, []
import re
import logging
import numpy as np
import shutil
import time
import uuid
import json
import random
from typing import List, Dict, Any, Optional, Tuple
import fitz  # PyMuPDF
from fastapi import FastAPI, HTTPException, Depends, Request, BackgroundTasks, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse, RedirectResponse
from pydantic import BaseModel, Field
from dotenv import load_dotenv
from fastapi import Query
try:
    from fastapi import run_in_threadpool
except ImportError:
    # For newer versions of FastAPI, run_in_threadpool is in fastapi.concurrency
    try:
        from fastapi.concurrency import run_in_threadpool
    except ImportError:
        # Fallback for even newer versions or alternative implementations
        import asyncio
        from concurrent.futures import ThreadPoolExecutor
        import functools
        
        # Create our own run_in_threadpool function
        _thread_pool = ThreadPoolExecutor()
        
        async def run_in_threadpool(func, *args, **kwargs):
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(_thread_pool, functools.partial(func, *args, **kwargs))
from uuid import uuid4
from datetime import datetime

# Load environment variables FIRST before importing custom modules
load_dotenv()

# Import custom modules
from website_scraper import extract_website_text
from document_extractor import extract_document, extract_document_with_visual_content
from vector_db import vector_db  # Import the vector database
import llm_router  # Import the new LLM router module
from feedback import FeedbackData, send_feedback_email, get_feedback_emails, update_feedback_emails, FeedbackEmailConfig  # Import feedback module
from supabase_client import supabase  # Import Supabase client
from config import config  # Import secure configuration
import vector_search  # Import standardized vector search
from category_management import router as category_router  # Import category management

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Direct document search functions
def search_documents_by_title(query: str, limit: int = 5):
    """Search for documents by title."""
    logger.info(f"Searching for documents with title containing '{query}'...")

    # Prepare the query
    search_query = f"""
    SELECT
        d.id,
        d.display_name,
        d.file_path,
        d.file_type,
        d.created_at,
        d.updated_at
    FROM
        documents d
    WHERE
        LOWER(d.display_name) LIKE LOWER('%{query}%')
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(search_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching documents by title: {result['error']}")
            return []

        logger.info(f"Found {len(result)} documents matching '{query}'")
        return result
    except Exception as e:
        logger.error(f"Error searching documents by title: {str(e)}")
        return []

def get_document_chunks(document_id: str, limit: int = 10):
    """Get chunks for a specific document."""
    logger.info(f"Getting chunks for document {document_id}...")

    # Prepare the query
    chunks_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        d.display_name as filename,
        d.file_path as url,
        'document' as source_type
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        dc.document_id = '{document_id}'
    ORDER BY
        dc.page_number, dc.chunk_index
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(chunks_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error getting document chunks: {result['error']}")
            return []

        logger.info(f"Found {len(result)} chunks for document {document_id}")
        return result
    except Exception as e:
        logger.error(f"Error getting document chunks: {str(e)}")
        return []

def search_documents_by_content(query: str, limit: int = 10):
    """Search for documents by content using text search."""
    logger.info(f"Searching for documents with content containing '{query}'...")

    # Extract key terms for better search
    import re
    from string import punctuation

    # Check if this is an acronym search (e.g., "full form of ACP")
    acronym_match = re.search(r'(?:full\s+form\s+of|what\s+(?:does|is|are)\s+the\s+full\s+form\s+of|what\s+(?:does|is|are)\s+)\s*([A-Z]{2,})', query, re.IGNORECASE)

    if acronym_match:
        # This is an acronym search, extract the acronym
        acronym = acronym_match.group(1).upper()
        logger.info(f"Detected acronym search for: {acronym}")

        # Special handling for acronym searches
        return search_for_acronym(acronym, limit)

    # Clean the query and extract key terms
    clean_query = re.sub(r'[' + punctuation + ']', ' ', query.lower())
    words = clean_query.split()
    stop_words = {'what', 'is', 'the', 'of', 'in', 'on', 'at', 'and', 'or', 'for', 'to', 'a', 'an', 'by', 'with', 'about'}
    key_terms = [word for word in words if word not in stop_words and len(word) > 2]

    # If no key terms found, use the original query
    if not key_terms:
        key_terms = [query]

    logger.info(f"Extracted key terms for content search: {key_terms}")

    # Create a tsquery string with OR operators between terms for broader matches
    ts_query_terms = " | ".join([term.replace("'", "''") for term in key_terms])

    # Sanitize the full query for logging
    sanitized_full_query = query.replace("'", "''")

    # Prepare the query with both exact phrase matching and key term matching
    # Join with documents table to get proper document information
    search_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        COALESCE(d.display_name, d.file_name, d.name, 'Unknown Document') as filename,
        d.file_path as url,
        'document' as source_type,
        GREATEST(
            ts_rank(to_tsvector('english', dc.text), to_tsquery('english', '{ts_query_terms}')),
            ts_rank(to_tsvector('english', dc.text), plainto_tsquery('english', '{sanitized_full_query}'))
        ) AS similarity
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        to_tsvector('english', dc.text) @@ to_tsquery('english', '{ts_query_terms}')
        OR to_tsvector('english', dc.text) @@ plainto_tsquery('english', '{sanitized_full_query}')
        OR dc.text ILIKE '%{sanitized_full_query}%'
    ORDER BY
        similarity DESC
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(search_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching documents by content: {result['error']}")
            return []

        logger.info(f"Found {len(result)} documents matching '{query}'")
        return result
    except Exception as e:
        logger.error(f"Error searching documents by content: {str(e)}")
        return []

def search_for_acronym(acronym: str, limit: int = 5):
    """Special search function for acronyms."""
    logger.info(f"Performing specialized acronym search for: {acronym}")

    # Sanitize the acronym
    sanitized_acronym = acronym.replace("'", "''")

    # Pattern 1: Look for exact acronym followed by words in parentheses (e.g., "ACP (Alarm Chain Pulling)")
    # Pattern 2: Look for exact acronym followed by "stands for" or "means" or "is"
    # Pattern 3: Look for exact acronym followed by a dash or colon and then words
    # Pattern 4: Look for the words "full form" near the acronym
    search_query = f"""
    SELECT
        dc.id,
        dc.document_id,
        dc.chunk_index,
        dc.page_number,
        dc.text,
        d.display_name as filename,
        d.file_path as url,
        'document' as source_type,
        1.0 AS similarity
    FROM
        document_chunks dc
    JOIN
        documents d ON dc.document_id = d.id
    WHERE
        dc.text ~* '\\b{sanitized_acronym}\\s*\\([^)]+\\)'
        OR dc.text ~* '\\b{sanitized_acronym}\\s+(stands\\s+for|means|is)\\b'
        OR dc.text ~* '\\b{sanitized_acronym}\\s*[-:]'
        OR (dc.text ~* '\\b{sanitized_acronym}\\b' AND dc.text ~* 'full\\s+form')
        OR dc.text ~* '\\b{sanitized_acronym}\\b.{{0,30}}(stands\\s+for|means|refers\\s+to|is)'
    ORDER BY
        CASE
            WHEN dc.text ~* '\\b{sanitized_acronym}\\s*\\([^)]+\\)' THEN 1
            WHEN dc.text ~* 'full\\s+form.{{0,20}}\\b{sanitized_acronym}\\b' THEN 2
            WHEN dc.text ~* '\\b{sanitized_acronym}\\b.{{0,20}}full\\s+form' THEN 3
            ELSE 4
        END
    LIMIT {limit}
    """

    try:
        result = supabase.execute_query(search_query)

        if isinstance(result, dict) and "error" in result:
            logger.error(f"Error searching for acronym: {result['error']}")
            return []

        logger.info(f"Found {len(result)} document chunks matching acronym '{acronym}'")

        # If no results found with the specialized search, try a more general search
        if not result:
            logger.info(f"No specialized matches for acronym '{acronym}', trying general search")

            # Fallback to a more general search
            general_query = f"""
            SELECT
                dc.id,
                dc.document_id,
                dc.chunk_index,
                dc.page_number,
                dc.text,
                d.display_name as filename,
                d.file_path as url,
                'document' as source_type,
                1.0 AS similarity
            FROM
                document_chunks dc
            JOIN
                documents d ON dc.document_id = d.id
            WHERE
                dc.text ~* '\\b{sanitized_acronym}\\b'
            LIMIT {limit}
            """

            result = supabase.execute_query(general_query)

            if isinstance(result, dict) and "error" in result:
                logger.error(f"Error with general acronym search: {result['error']}")
                return []

            logger.info(f"Found {len(result)} document chunks with general acronym search")

        return result
    except Exception as e:
        logger.error(f"Error searching for acronym: {str(e)}")
        return []

def text_based_document_search(query: str, top_k: int = 10):
    """
    Text-based document search using keyword matching with relevance scoring.
    Works with in-memory DOCUMENT_CHUNKS without requiring embeddings.
    """
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Using text-based document search for: '{query}'")

        # Extract key terms from query
        query_lower = query.lower()
        query_words = set(query_lower.split())

        # Railway domain keywords for relevance boost
        railway_keywords = {
            'railway', 'train', 'rail', 'station', 'track', 'locomotive', 'coach',
            'signal', 'platform', 'passenger', 'freight', 'engine', 'diesel',
            'fsds', 'monitoring', 'system', 'safety', 'maintenance', 'inspection'
        }

        scored_chunks = []

        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'document':
                continue

            chunk_text = chunk.get('text', '').lower()
            if not chunk_text:
                continue

            # Calculate relevance score
            score = 0

            # 1. Exact phrase match (highest priority)
            if query_lower in chunk_text:
                score += 2.0

            # 2. Keyword matching (must have multiple matches for general terms)
            chunk_words = set(chunk_text.split())
            common_words = query_words & chunk_words
            if common_words:
                # Require more matches for shorter queries
                min_matches = max(1, len(query_words) // 2)
                if len(common_words) >= min_matches:
                    score += len(common_words) * 0.5

            # 3. Railway domain relevance (strong boost)
            railway_matches = railway_keywords & chunk_words
            if railway_matches:
                score += len(railway_matches) * 0.8

            # 4. Special boost for specific technical terms
            if 'fsds' in chunk_text:
                score += 1.5
            if 'monitoring' in chunk_text and 'system' in chunk_text:
                score += 1.0

            # 5. Penalty for very generic matches
            generic_terms = {'the', 'and', 'or', 'in', 'on', 'at', 'to', 'a', 'an', 'is', 'are'}
            if query_words.issubset(generic_terms):
                score *= 0.1  # Heavy penalty for generic queries

            # Only include chunks with meaningful relevance (increased threshold)
            if score >= 0.8:  # Much higher threshold
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = score
                scored_chunks.append(chunk_copy)

        # Sort by relevance score
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)

        result = scored_chunks[:top_k]
        logger.info(f"Text-based document search found {len(result)} relevant chunks (threshold: 0.8)")

        return result

    except Exception as e:
        logger.error(f"Error in text-based document search: {str(e)}")
        return []

def text_based_website_search(query: str, top_k: int = 10):
    """
    Text-based website search using keyword matching with relevance scoring.
    Works with in-memory DOCUMENT_CHUNKS without requiring embeddings.
    """
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Using text-based website search for: '{query}'")

        # Extract key terms from query
        query_lower = query.lower()
        query_words = set(query_lower.split())

        # Transport/railway domain keywords for relevance boost
        transport_keywords = {
            'railway', 'train', 'rail', 'station', 'transport', 'transportation',
            'public', 'safety', 'travel', 'passenger', 'metro', 'services'
        }

        scored_chunks = []

        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'website':
                continue

            chunk_text = chunk.get('text', '').lower()
            if not chunk_text:
                continue

            # Calculate relevance score
            score = 0

            # 1. Exact phrase match (highest priority)
            if query_lower in chunk_text:
                score += 2.0

            # 2. Keyword matching (must have multiple matches for general terms)
            chunk_words = set(chunk_text.split())
            common_words = query_words & chunk_words
            if common_words:
                # Require more matches for shorter queries
                min_matches = max(1, len(query_words) // 2)
                if len(common_words) >= min_matches:
                    score += len(common_words) * 0.5

            # 3. Transport domain relevance (strong boost)
            transport_matches = transport_keywords & chunk_words
            if transport_matches:
                score += len(transport_matches) * 0.8

            # 4. Special boost for transport safety
            if 'transportation' in chunk_text and 'safety' in chunk_text:
                score += 1.0
            if 'public' in chunk_text and ('transport' in chunk_text or 'railway' in chunk_text):
                score += 1.0

            # 5. Penalty for very generic matches
            generic_terms = {'the', 'and', 'or', 'in', 'on', 'at', 'to', 'a', 'an', 'is', 'are'}
            if query_words.issubset(generic_terms):
                score *= 0.1  # Heavy penalty for generic queries

            # Only include chunks with meaningful relevance (increased threshold)
            if score >= 0.8:  # Much higher threshold
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = score
                scored_chunks.append(chunk_copy)

        # Sort by relevance score
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)

        result = scored_chunks[:top_k]
        logger.info(f"Text-based website search found {len(result)} relevant chunks (threshold: 0.8)")

        return result

    except Exception as e:
        logger.error(f"Error in text-based website search: {str(e)}")
        return []


def search_documents_in_supabase(query: str, limit: int = 10) -> List[Dict[str, Any]]:
    """
    Simple wrapper function for document search that combines multiple search strategies.
    This function is used by the query endpoint for document search.
    """
    try:
        logger.info(f"Searching documents in Supabase for: '{query}' (limit: {limit})")

        # Try multiple search strategies in order of preference

        # 1. First try content-based search (works well for specific queries)
        content_results = search_documents_by_content(query, limit)
        if content_results:
            logger.info(f"Content search found {len(content_results)} results")
            return content_results

        # 2. Try vector search if available
        try:
            from llm_router import generate_embedding
            query_embedding = generate_embedding(query)
            if query_embedding:
                vector_results = search_supabase_document_chunks_enhanced(
                    query_embedding=query_embedding,
                    query_text=query,
                    use_hybrid_search=True,
                    top_k=limit,
                    min_threshold=0.1
                )
                if vector_results:
                    logger.info(f"Vector search found {len(vector_results)} results")
                    return vector_results
        except Exception as e:
            logger.warning(f"Vector search failed: {str(e)}")

        # 3. Try text-based search as fallback
        text_results = text_based_document_search(query, limit)
        if text_results:
            logger.info(f"Text-based search found {len(text_results)} results")
            return text_results

        logger.info("No document results found with any search method")
        return []

    except Exception as e:
        logger.error(f"Error in search_documents_in_supabase: {str(e)}")
        return []


# Configure LLM models through the router
available_models = llm_router.get_available_models()
logger.info(f"Available LLM models: {[model['id'] for model in available_models]}")

# For backward compatibility
api_key = os.getenv("GEMINI_API_KEY")
if not api_key:
    logger.warning("GEMINI_API_KEY not found in environment variables. Using mock embeddings.")

# Global variable to store document chunks with embeddings
DOCUMENT_CHUNKS = []
# Priority weights for different source types
DOCUMENT_PRIORITY_WEIGHT = 2.5  # Higher priority for documents
WEBSITE_PRIORITY_WEIGHT = 1.5   # Medium priority for websites
# Threshold for considering document chunks relevant
RELEVANCE_THRESHOLD = 0.05  # Very low threshold to prioritize documents

# Create FastAPI app
app = FastAPI(title="Document Management System")

# Import and include category management router
try:
    from category_management import router as category_router
    app.include_router(category_router)
    logger.info("Category management router included successfully")
except ImportError as e:
    logger.warning(f"Could not import category management router: {e}")
except Exception as e:
    logger.warning(f"Error including category management router: {e}")

# Import and include enhanced category management router
try:
    from enhanced_category_management import router as enhanced_category_router
    app.include_router(enhanced_category_router, prefix="/api/enhanced")
    logger.info("Enhanced category management router included successfully")
except ImportError as e:
    logger.warning(f"Could not import enhanced category management router: {e}")
except Exception as e:
    logger.warning(f"Error including enhanced category management router: {e}")

# Configure CORS for local development and production
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # React development server
        "http://127.0.0.1:3000",  # Alternative localhost
        "https://railchatbot-cb555.web.app",  # Production domain
        "*"  # Allow all origins for development
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Custom middleware to increase file upload size limit
@app.middleware("http")
async def add_custom_upload_limit(request: Request, call_next):
    # Set a large upload limit (200MB)
    # This is handled at the application level, not the server level
    # For production, you should also configure your ASGI server (Uvicorn) with appropriate limits
    request.scope.setdefault("_body_size_limit", 200 * 1024 * 1024)  # 200MB
    response = await call_next(request)
    return response

# Define API request and response models
class QueryRequest(BaseModel):
    query: str
    model: Optional[str] = "gemini-2.0-flash"  # Changed to gemini-1.5-flash to fix timeout issues
    fallback_enabled: Optional[bool] = True  # Enable LLM fallback by default
    extract_format: Optional[str] = "paragraph"  # paragraph, bullet, or table
    use_hybrid_search: Optional[bool] = True  # Enable hybrid search by default
    retry_on_timeout: Optional[bool] = True  # Try fallback model on timeout
    context_mode: Optional[str] = "flexible"  # strict, flexible, or none (no context)

class WebsiteScrapeRequest(BaseModel):
    url: str

class WebsiteAddRequest(BaseModel):
    url: str
    submitted_by: Optional[str] = None
    role: Optional[str] = None
    follow_links: Optional[bool] = False
    extraction_depth: Optional[int] = 1
    extract_images: Optional[bool] = False
    extract_tables: Optional[bool] = True
    max_pages: Optional[int] = 10
    extractor_type: Optional[str] = "trafilatura"
    domain_category: Optional[str] = "general"
    embedding_model: Optional[str] = "gemini-2.0-flash"  # Model for embeddings
    # 4-level hierarchical category fields (UUIDs)
    main_category_id: Optional[str] = None
    category_id: Optional[str] = None
    sub_category_id: Optional[str] = None
    minor_category_id: Optional[str] = None
    # Legacy fields for backward compatibility
    main_category: Optional[str] = None
    category: Optional[str] = None
    sub_category: Optional[str] = None
    minor_category: Optional[str] = None

class Source(BaseModel):
    source_type: str  # "document" or "website"
    # For documents
    filename: Optional[str] = None
    name: Optional[str] = None  # Display name for the document
    page: Optional[int] = None
    # For websites
    url: Optional[str] = None
    link: Optional[str] = None  # For document viewer links
    # For visual content
    content_type: Optional[str] = None  # "text", "table", "image", "chart_diagram"
    visual_content: Optional[Dict[str, Any]] = None  # Visual content metadata
    storage_url: Optional[str] = None  # URL for stored visual content
    display_type: Optional[str] = None  # "text", "html_table", "image", "base64_image"

class QueryResponse(BaseModel):
    answer: str  # Combined answer from all sources
    document_answer: Optional[str] = None  # Answer only from document sources
    website_answer: Optional[str] = None  # Answer only from website sources
    sources: List[Source]  # All sources
    document_sources: Optional[List[Source]] = None  # Only document sources
    website_sources: Optional[List[Source]] = None  # Only website sources
    llm_model: Optional[str] = None  # The LLM model used for generating the answer
    llm_fallback: Optional[bool] = False  # Whether the answer was generated using LLM fallback
    visual_content_found: Optional[bool] = False  # Whether visual content was found
    visual_content_types: Optional[List[str]] = None  # Types of visual content found

class ChunkData(BaseModel):
    filename: str
    page: int
    chunk_id: str
    text: str
    embedding: Optional[List[float]] = None

# Document processing functions
def clean_text(text: str) -> str:
    """Clean text for processing."""
    return " ".join(text.split()) if text else ""

def detect_visual_query(query: str) -> Dict[str, Any]:
    """
    Detect if query is looking for visual content and what types.
    Enhanced to detect specific logos, brands, and companies.
    
    Returns:
        Dict with flags for different visual content types and specific entities
    """
    query_lower = query.lower()
    
    # Visual-related keywords
    table_keywords = ["table", "chart", "data", "specifications", "schedule", "list", "grid", "column", "row", "quotation"]
    image_keywords = ["image", "picture", "diagram", "figure", "illustration", "photo", "schematic", "project", "logo", "emblem", "symbol", "icon", "graphic", "drawing", "sketch", "map", "layout", "design", "blueprint", "plan", "screenshot", "snapshot", "visual", "artwork"]
    chart_keywords = ["chart", "graph", "diagram", "flowchart", "flow chart", "technical drawing", "blueprint"]
    
    # Logo and brand related keywords
    logo_keywords = ["logo", "emblem", "symbol", "icon", "brand", "company logo", "trademark", "wordmark", "letterhead"]
    
    # General visual keywords
    visual_keywords = ["visual", "show me", "display", "view", "picture", "graphic"]
    
    is_table_query = any(keyword in query_lower for keyword in table_keywords)
    is_image_query = any(keyword in query_lower for keyword in image_keywords)
    is_chart_query = any(keyword in query_lower for keyword in chart_keywords)
    is_logo_query = any(keyword in query_lower for keyword in logo_keywords)
    is_visual_query = any(keyword in query_lower for keyword in visual_keywords) or is_table_query or is_image_query or is_chart_query or is_logo_query
    
    # Extract specific entities (like "Project 1", "Quotation 2", etc.)
    specific_entities = []
    company_entities = []
    import re
    
    # Look for patterns like "Project 1", "Quotation 1", "Table 2", etc.
    entity_patterns = [
        r'project\s*(\d+)',
        r'quotation\s*(\d+)',
        r'table\s*(\d+)',
        r'image\s*(\d+)',
        r'figure\s*(\d+)',
    ]
    
    for pattern in entity_patterns:
        matches = re.findall(pattern, query_lower)
        if matches:
            entity_type = pattern.split('\\')[0]  # Get the entity type (project, quotation, etc.)
            for match in matches:
                specific_entities.append({
                    "type": entity_type,
                    "number": match,
                    "query_text": f"{entity_type} {match}"
                })
    
    # Extract company/brand names for logo queries
    if is_logo_query or "logo" in query_lower:
        # Common patterns for company names in logo queries
        company_patterns = [
            r'(?:logo\s+(?:of\s+)?|show\s+me\s+(?:the\s+)?logo\s+(?:of\s+)?)([A-Z][A-Za-z\s&]+?)(?:\s+logo|\s+enterprises|\s+company|\s+corp|\s+ltd|\s+inc|\s*$)',
            r'([A-Z][A-Za-z\s&]+?)\s+(?:enterprises|company|corp|ltd|inc)\s+logo',
            r'([A-Z][A-Za-z\s&]+?)\s+logo',
        ]
        
        for pattern in company_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            for match in matches:
                company_name = match.strip()
                if len(company_name) > 1:  # Avoid single characters
                    company_entities.append({
                        "type": "company",
                        "name": company_name,
                        "query_text": f"{company_name} logo"
                    })
    
    # Priority content types based on query analysis
    priority_content_types = []
    if is_logo_query:
        priority_content_types.append("logo")
    if is_image_query:
        priority_content_types.append("image")
    if is_table_query:
        priority_content_types.append("table")
    if is_chart_query:
        priority_content_types.append("chart_diagram")
    
    return {
        "is_visual": is_visual_query,
        "wants_tables": is_table_query,
        "wants_images": is_image_query,
        "wants_charts": is_chart_query,
        "wants_logos": is_logo_query,
        "specific_entities": specific_entities,
        "company_entities": company_entities,
        "priority_content_types": priority_content_types,
        "search_terms": [entity["query_text"] for entity in specific_entities + company_entities]
    }

def search_supabase_document_chunks_enhanced(query_embedding, query_text=None, use_hybrid_search=True, 
                                           top_k=30, min_threshold=0.4, document_filter=None,
                                           visual_query_info=None):
    """
    Enhanced document search that prioritizes visual content when needed.
    Simplified to prevent timeouts while maintaining logo detection functionality.
    """
    try:
        logger.info(f"Enhanced search - Visual query info: {visual_query_info}")
        
        # Quick check for logo queries - simplify the logic to prevent timeouts
        if visual_query_info and visual_query_info.get("wants_logos"):
            logger.info("🎯 Logo query detected - searching for visual content")
            
            # For logo queries, use a simple approach that doesn't timeout
            # Search all chunks with a focus on image content and company entities
            company_entities = visual_query_info.get("company_entities", [])
            
            if company_entities:
                # Extract company names for searching
                company_names = [entity.get("name", "").lower() for entity in company_entities]
                logger.info(f"Searching for company logos: {company_names}")
                
                # Use regular search but filter for visual content afterwards
                regular_chunks = search_supabase_document_chunks(
                    query_embedding=query_embedding,
                    query_text=query_text,
                    use_hybrid_search=use_hybrid_search,
                    top_k=top_k,
                    min_threshold=min_threshold
                )
                
                # Filter and boost chunks that contain company names or are images
                enhanced_chunks = []
                for chunk in regular_chunks:
                    chunk_text = chunk.get("text", "").lower()
                    metadata = chunk.get("metadata", {})
                    content_type = metadata.get("content_type", "text")
                    
                    # Check for company name matches
                    score_boost = 0.0
                    for company_name in company_names:
                        if company_name in chunk_text:
                            score_boost += 0.3
                            logger.info(f"Found company '{company_name}' in chunk")
                    
                    # Boost image content for logo queries
                    if content_type == "image":
                        score_boost += 0.4
                        logger.info(f"Found image content - boosting for logo query")
                    
                    # Apply boost and add to results
                    if score_boost > 0:
                        chunk["similarity"] = chunk.get("similarity", 0) + score_boost
                        enhanced_chunks.append(chunk)
                    else:
                        enhanced_chunks.append(chunk)
                
                # Sort by boosted similarity
                enhanced_chunks.sort(key=lambda x: x.get("similarity", 0), reverse=True)
                logger.info(f"Enhanced search returned {len(enhanced_chunks)} chunks with logo boosting")
                return enhanced_chunks
        
        # For non-visual queries or if visual search fails, use regular search
        logger.info("Using regular search (non-visual query or fallback)")
        return search_supabase_document_chunks(
            query_embedding=query_embedding,
            query_text=query_text,
            use_hybrid_search=use_hybrid_search,
            top_k=top_k,
            min_threshold=min_threshold
        )
            
    except Exception as e:
        logger.error(f"Error in enhanced document search: {str(e)}")
        # Fallback to regular search
        return search_supabase_document_chunks(
            query_embedding=query_embedding,
            query_text=query_text,
            use_hybrid_search=use_hybrid_search,
            top_k=top_k,
            min_threshold=min_threshold
        )

def generate_embedding(text: str, model_id: str = "gemini-2.0-flash") -> List[float]:
    """Generate embedding vector for text using the LLM router."""
    try:
        # Use the LLM router to generate embeddings
        return llm_router.generate_embedding(text, model_id)
    except Exception as e:
        logger.error(f"Error generating embedding with {model_id}: {str(e)}")
        # Try with a different model before falling back to random
        try:
            logger.info(f"Retrying embedding generation with default model")
            return llm_router.generate_embedding(text, llm_router.DEFAULT_MODEL)
        except Exception as fallback_error:
            logger.error(f"Fallback embedding generation also failed: {str(fallback_error)}")
            # Fallback to a consistent embedding rather than random
            # This ensures that if we use this fallback, at least all fallbacks will be similar
            # Using a seed ensures consistency
            np.random.seed(hash(text) % 2**32)
            return list(np.random.rand(768))

def cosine_similarity(embedding1, embedding2):
    """
    Calculate cosine similarity between two embeddings, handling string conversions.
    """
    # Convert embeddings to numpy arrays if they are not already
    try:
        # Handle string embeddings (from JSON)
        if isinstance(embedding1, str):
            try:
                embedding1 = json.loads(embedding1)
            except:
                logger.error("Failed to parse string embedding1")
                return 0.0

        if isinstance(embedding2, str):
            try:
                embedding2 = json.loads(embedding2)
            except:
                logger.error("Failed to parse string embedding2")
                return 0.0

        # Ensure embeddings are numpy arrays of float32
        embedding1 = np.array(embedding1, dtype=np.float32)
        embedding2 = np.array(embedding2, dtype=np.float32)

        # Compute cosine similarity
        dot_product = np.dot(embedding1, embedding2)
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)
    except Exception as e:
        logger.error(f"Error calculating cosine similarity: {str(e)}")
        return 0.0  # Return 0 similarity on error

def search_supabase_document_chunks(query_embedding, query_text=None, use_hybrid_search=True, top_k=30, min_threshold=0.4, document_filter=None):
    """
    Fixed professional vector search for document chunks using proper pgvector syntax.
    """
    try:
        logger.info(f"FIXED vector search for documents: threshold={min_threshold}, top_k={top_k}")

        # Strategy 1: Try direct content search first (most reliable)
        if query_text:
            try:
                direct_results = search_documents_by_content(query_text, limit=top_k)
                if direct_results:
                    logger.info(f"Direct content search found {len(direct_results)} document chunks")
                    
                    # Convert to expected format
                    result_chunks = []
                    for chunk in direct_results:
                        chunk_copy = dict(chunk)
                        chunk_copy['similarity'] = 0.9  # High similarity for direct matches
                        chunk_copy['source_type'] = 'document'
                        
                        # Ensure required fields are present
                        if 'page' not in chunk_copy and 'page_number' in chunk_copy:
                            chunk_copy['page'] = chunk_copy['page_number']
                        
                        # Extract or set filename
                        if not chunk_copy.get('filename'):
                            text = chunk_copy.get('text', '')
                            import re
                            match = re.search(r'Document:\s*([^,]+)', text)
                            if match:
                                filename = match.group(1).strip()
                                if not filename.endswith(('.pdf', '.docx', '.txt')):
                                    filename += '.pdf'
                                chunk_copy['filename'] = filename
                            else:
                                chunk_copy['filename'] = 'Unknown document'
                        
                        result_chunks.append(chunk_copy)
                    
                    return result_chunks[:top_k]
                        
            except Exception as e:
                logger.warning(f"Direct content search failed: {str(e)}")

        # Strategy 2: Try RPC function (if it works)
        try:
            from supabase_client import supabase
            # Use Supabase RPC function for vector search (skip if known to fail)
            result = supabase.rpc(
                'search_document_chunks',
                {
                    'query_embedding': query_embedding,
                    'similarity_threshold': min_threshold,
                    'match_count': top_k
                }
            ).execute()

            if result.data and len(result.data) > 0:
                for chunk in result.data:
                    chunk["source_type"] = "document"
                logger.info(f"RPC search found {len(result.data)} document chunks")
                return result.data
        except Exception as e:
            logger.info(f"RPC search failed, trying local approach: {str(e)}")

        # Fallback to local search with proper embeddings
        logger.info("Using local vector search with cosine similarity")
        global DOCUMENT_CHUNKS

        scored_chunks = []
        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'document':
                continue

            chunk_embedding = chunk.get('embedding')
            if not chunk_embedding:
                continue

            # Calculate cosine similarity
            similarity = cosine_similarity(query_embedding, chunk_embedding)

            if similarity >= min_threshold:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = similarity
                scored_chunks.append(chunk_copy)

        # Sort by similarity (highest first)
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)
        result = scored_chunks[:top_k]
        
        # Ensure document metadata is properly attached to all chunks
        # This prevents "Unknown document" issues in source attribution
        try:
            # Get document IDs from chunks
            doc_ids = [chunk.get('document_id') for chunk in result if chunk.get('document_id')]
            if doc_ids:
                # Query documents table to get filenames and other metadata
                from supabase_client import supabase
                query = f"""
                SELECT id, COALESCE(display_name, file_name, name) as filename, file_path, name
                FROM documents
                WHERE id IN ({','.join([f"'{id}'" for id in doc_ids])})
                """
                doc_results = supabase.execute_query(query)
                if doc_results and len(doc_results) > 0:
                    # Create lookup dictionary
                    doc_lookup = {doc['id']: doc for doc in doc_results}
                    # Update all chunks with their document metadata
                    for chunk in result:
                        doc_id = chunk.get('document_id')
                        if doc_id and doc_id in doc_lookup:
                            chunk['filename'] = doc_lookup[doc_id]['filename']
                            chunk['url'] = doc_lookup[doc_id]['file_path'] if 'file_path' in doc_lookup[doc_id] else None
                            logger.info(f"Enhanced chunk metadata with filename: {chunk['filename']}")
        except Exception as e:
            logger.error(f"Error enhancing chunks with document metadata: {str(e)}")

        logger.info(f"Local vector search found {len(result)} document chunks with similarity >= {min_threshold}")
        if result:
            similarities = [f"{c.get('similarity', 0):.3f}" for c in result[:3]]
            logger.info(f"Top similarities: {similarities}")

        return result

    except Exception as e:
        logger.error(f"Error in fixed vector document search: {str(e)}")
        return []

def search_supabase_website_chunks(query_embedding, query_text=None, use_hybrid_search=True, top_k=30, min_threshold=0.2, website_filter=None):
    """
    Professional vector search for website chunks using Supabase pgvector.
    Scalable solution for 100+ websites using semantic similarity.
    """
    try:
        logger.info(f"Professional vector search for websites: threshold={min_threshold}, top_k={top_k}")

        # Try to use Supabase RPC function first, then fallback to direct SQL
        try:
            from supabase_client import supabase
            # Use Supabase RPC function for vector search if available
            result = supabase.rpc(
                'search_website_chunks',
                {
                    'query_embedding': query_embedding,
                    'similarity_threshold': min_threshold,
                    'match_count': top_k
                }
            ).execute()

            if result.data and len(result.data) > 0:
                for chunk in result.data:
                    chunk["source_type"] = "website"
                logger.info(f"RPC website search found {len(result.data)} chunks")
                return result.data
        except Exception as rpc_error:
            logger.info(f"RPC website search failed: {str(rpc_error)}, trying direct SQL")

        # Fallback to direct SQL query
        try:
            # Convert embedding to proper format for pgvector
            embedding_str = str(query_embedding).replace(' ', '')

            # Use direct SQL with pgvector cosine similarity
            query = f"""
            SELECT
                wc.id,
                wc.website_id,
                wc.chunk_index,
                wc.text,
                w.url,
                w.title as website_name,
                wc.metadata,
                1 - (wc.embedding <=> '{embedding_str}') as similarity
            FROM website_chunks wc
            JOIN websites w ON wc.website_id = w.id
            WHERE 1 - (wc.embedding <=> '{embedding_str}') > {min_threshold}
            ORDER BY wc.embedding <=> '{embedding_str}'
            LIMIT {top_k};
            """

            result = supabase.execute_query(query)
        except Exception as sql_error:
            logger.error(f"Direct SQL website search failed: {str(sql_error)}")
            return []

        if not result or not isinstance(result, list):
            logger.info("No website chunks found with vector search")
            return []

        # Add source_type to each chunk
        for chunk in result:
            chunk["source_type"] = "website"

        logger.info(f"Found {len(result)} relevant website chunks with vector similarity")
        return result

    except Exception as e:
        logger.error(f"Error in professional vector website search: {str(e)}")
        return []

def group_chunks_by_source(chunks):
    """
    Group chunks by source type into document and website categories.

    Args:
        chunks: List of chunks with source_type information

    Returns:
        Tuple of (document_chunks, website_chunks)
    """
    document_chunks = []
    website_chunks = []

    for chunk in chunks:
        source_type = chunk.get("source_type", "").lower()
        if source_type == "document":
            document_chunks.append(chunk)
        elif source_type == "website":
            website_chunks.append(chunk)

    return document_chunks, website_chunks

def generate_llm_answer(query: str, similar_chunks: List[Dict[str, Any]], system_prompt: str = None, model_id: str = "gemini-2.0-flash", extract_format: str = "paragraph"):
    """
    Generate a coherent answer using the LLM router based on similar chunks.

    Args:
        query: The user's question
        similar_chunks: List of context chunks to use for generating the answer
        system_prompt: Custom system prompt to use (optional)
        model_id: LLM model to use (default: gemini-2.0-flash)
        extract_format: Preferred format for the extraction (paragraph, bullet, table)

    Returns:
        Tuple of (answer_text, source_data, document_sources, website_sources)
    """
    try:
        # Prepare context from similar chunks
        context_texts = []
        sources = []
        document_sources = []
        website_sources = []

        # Ensure all chunks have valid text
        valid_chunks = []
        for chunk in similar_chunks:
            # Ensure text field exists and is a string
            if "text" not in chunk or not isinstance(chunk["text"], str) or not chunk["text"].strip():
                # Log the issue and try to fix it
                logger.warning(f"Found chunk with missing or invalid text: {chunk.get('id', 'unknown')}")
                # Try to fix it with a default value
                chunk["text"] = chunk.get("text", "") or "No content available"

            # Ensure source_type is set - use a more aggressive approach to reduce warnings
            if "source_type" not in chunk or not chunk["source_type"]:
                if "filename" in chunk or "page" in chunk or "document_id" in chunk:
                    chunk["source_type"] = "document"
                    # Use less verbose logging to avoid flooding logs
                    if random.random() < 0.01:  # Only log 1% of these to reduce noise
                        logger.debug(f"Set source_type to 'document' for chunk {chunk.get('id', 'unknown')}")
                elif "url" in chunk or "website_id" in chunk:
                    chunk["source_type"] = "website"
                    if random.random() < 0.01:  # Only log 1% of these
                        logger.debug(f"Set source_type to 'website' for chunk {chunk.get('id', 'unknown')}")
                else:
                    chunk["source_type"] = "unknown"
                    if random.random() < 0.01:  # Only log 1% of these
                        logger.debug(f"Set source_type to 'unknown' for chunk {chunk.get('id', 'unknown')}")

            valid_chunks.append(chunk)

        # Use only valid chunks
        similar_chunks = valid_chunks

        # If no valid chunks, return early
        if not similar_chunks:
            logger.warning("No valid chunks found for LLM answer generation")
            return "I couldn't find any valid information to answer your question.", [], [], []

        # Log the content of chunks to help diagnose issues
        logger.info(f"Processing {len(similar_chunks)} chunks for answer generation")
        for i, chunk in enumerate(similar_chunks[:3]):  # Log first 3 chunks for debugging
            logger.info(f"Chunk {i}: source_type={chunk.get('source_type', 'unknown')}, similarity={chunk.get('similarity', 0):.3f}, text_length={len(chunk.get('text', ''))}, text_preview={chunk.get('text', '')[:100]}...")

        for chunk in similar_chunks:
            chunk_text = chunk.get("text", "")
            similarity = chunk.get("similarity", 0)

            # Add context with citation information
            if chunk.get("source_type") == "document":
                # Document source
                filename = chunk.get("filename") or chunk.get("title") or "Unknown document"
                page = chunk.get("page", 1)
                context_texts.append(f"From '{filename}' (page {page}, relevance: {similarity:.2f}):\n{chunk_text}\n")

                # Add to sources list
                source = {
                    "source_type": "document",
                    "filename": filename,
                    "page": page,
                    # For UI display
                    "name": os.path.basename(filename),
                    "link": f"/viewer?file={filename}&page={page}"
                }
                if source not in sources:
                    sources.append(source)
                if source not in document_sources:
                    document_sources.append(source)
            elif chunk.get("source_type") == "website":
                # Website source - extract URL from multiple possible fields
                url = chunk.get("url", "")

                # If no URL field, try to extract from text
                if not url or url == "Unknown":
                    chunk_text_for_url = chunk.get("text", "")
                    # Look for URL patterns in the text
                    import re
                    url_match = re.search(r'URL:\s*(https?://[^\s\n]+)', chunk_text_for_url)
                    if url_match:
                        url = url_match.group(1)
                    else:
                        # Look for website domain patterns
                        domain_match = re.search(r'Website:\s*([^\s,\n]+)', chunk_text_for_url)
                        if domain_match:
                            domain = domain_match.group(1)
                            url = f"https://{domain}" if not domain.startswith('http') else domain
                        # Special case handling for known websites
                        elif "rapidresponseapp" in chunk_text_for_url.lower():
                            url = "https://www.rapidresponseapp.com"
                        elif "indianrailways.gov.in" in chunk_text_for_url.lower():
                            url = "https://indianrailways.gov.in"
                        elif "irctc" in chunk_text_for_url.lower():
                            url = "https://www.irctc.co.in"
                        else:
                            url = "Unknown website"

                context_texts.append(f"From '{url}' (relevance: {similarity:.2f}):\n{chunk_text}\n")

                # Add to sources list
                source = {
                    "source_type": "website",
                    "url": url
                }
                if source not in sources:
                    sources.append(source)
                if source not in website_sources:
                    website_sources.append(source)
            else:
                # Unknown source type
                logger.warning(f"Unknown source type for chunk: {chunk.get('id', 'unknown')}")
                context_texts.append(f"From unknown source (relevance: {similarity:.2f}):\n{chunk_text}\n")

        # Combine all context texts
        context = "\n\n".join(context_texts)
        logger.info(f"Created context with {len(context_texts)} context segments, total length: {len(context)} characters")

        # Token limit handling - approximately 4 characters per token for English text
        # 1M tokens is the absolute limit for Gemini, so we'll stay well under that
        MAX_MODEL_TOKENS = 1000000  # Gemini's maximum token limit
        SAFETY_FACTOR = 0.25  # Use only 75% of the maximum to leave room for response
        CHARS_PER_TOKEN = 4  # Approximate chars per token in English text

        max_context_chars = int((MAX_MODEL_TOKENS * SAFETY_FACTOR) * CHARS_PER_TOKEN)
        logger.info(f"Maximum context size: {max_context_chars} chars (~{int(MAX_MODEL_TOKENS * SAFETY_FACTOR)} tokens)")

        if len(context) > max_context_chars:
            logger.warning(f"Context too large ({len(context)} chars / ~{int(len(context)/CHARS_PER_TOKEN)} tokens), truncating to ~{max_context_chars} chars")

            # Sort chunks by similarity before truncating
            sorted_chunks = sorted(similar_chunks, key=lambda x: x.get('similarity', 0), reverse=True)

            # Start with highest similarity chunks
            new_context_texts = []
            new_context_length = 0
            preserved_sources = []
            preserved_doc_sources = []
            preserved_web_sources = []

            # First include top 5 highest similarity chunks regardless of size (to ensure best matches are included)
            top_chunks = sorted_chunks[:5]
            remaining_chunks = sorted_chunks[5:]

            # Add top chunks first
            for chunk in top_chunks:
                if "text" not in chunk or not chunk["text"]:  # Skip chunks with no text
                    continue

                # Truncate extremely large chunks to a reasonable size
                if len(chunk.get("text", "")) > 10000:  # If chunk is > 10K chars
                    chunk["text"] = chunk["text"][:10000] + "...[truncated due to length]"
                    logger.info(f"Truncated very large chunk of {len(chunk.get('text', ''))} chars")

                chunk_text = f"From '{chunk.get('filename', chunk.get('url', 'Unknown'))}' (relevance: {chunk.get('similarity', 0):.2f}):\n{chunk.get('text', '')}\n"
                new_context_texts.append(chunk_text)
                new_context_length += len(chunk_text)

                # Track which sources we're keeping
                if chunk.get("source_type") == "document":
                    source = {
                        "source_type": "document",
                        "filename": chunk.get("filename", "Unknown document"),
                        "page": chunk.get("page", 1),
                        "name": os.path.basename(chunk.get("filename", "Unknown")),
                        "link": f"/viewer?file={chunk.get('filename', 'Unknown')}&page={chunk.get('page', 1)}"
                    }
                    if source not in preserved_sources:
                        preserved_sources.append(source)
                    if source not in preserved_doc_sources:
                        preserved_doc_sources.append(source)
                elif chunk.get("source_type") == "website":
                    source = {
                        "source_type": "website",
                        "url": chunk.get("url", "Unknown website")
                    }
                    if source not in preserved_sources:
                        preserved_sources.append(source)
                    if source not in preserved_web_sources:
                        preserved_web_sources.append(source)

            # Now add remaining chunks up to the limit
            for chunk in remaining_chunks:
                if "text" not in chunk or not chunk["text"]:  # Skip chunks with no text
                    continue

                # Truncate very large chunks
                if len(chunk.get("text", "")) > 5000:  # More aggressive truncation for non-top chunks
                    chunk["text"] = chunk["text"][:5000] + "...[truncated due to length]"

                chunk_text = f"From '{chunk.get('filename', chunk.get('url', 'Unknown'))}' (relevance: {chunk.get('similarity', 0):.2f}):\n{chunk.get('text', '')}\n"

                # Check if adding this chunk would exceed our limit
                if new_context_length + len(chunk_text) < max_context_chars:
                    new_context_texts.append(chunk_text)
                    new_context_length += len(chunk_text)

                    # Track which sources we're keeping
                    if chunk.get("source_type") == "document":
                        source = {
                            "source_type": "document",
                            "filename": chunk.get("filename", "Unknown document"),
                            "page": chunk.get("page", 1),
                            "name": os.path.basename(chunk.get("filename", "Unknown")),
                            "link": f"/viewer?file={chunk.get('filename', 'Unknown')}&page={chunk.get('page', 1)}"
                        }
                        if source not in preserved_sources:
                            preserved_sources.append(source)
                        if source not in preserved_doc_sources:
                            preserved_doc_sources.append(source)
                    elif chunk.get("source_type") == "website":
                        source = {
                            "source_type": "website",
                            "url": chunk.get("url", "Unknown website")
                        }
                        if source not in preserved_sources:
                            preserved_sources.append(source)
                        if source not in preserved_web_sources:
                            preserved_web_sources.append(source)
                else:
                    # We've reached our limit
                    break

            # Replace the original collections with our trimmed versions
            context = "\n\n".join(new_context_texts)
            sources = preserved_sources
            document_sources = preserved_doc_sources
            website_sources = preserved_web_sources

            logger.info(f"Truncated context to {len(context)} chars (~{int(len(context)/CHARS_PER_TOKEN)} tokens), preserving {len(new_context_texts)} highest relevance chunks")

        if not context.strip():
            logger.warning("Combined context is empty after processing chunks")
            return "I couldn't find any valid information to answer your question.", [], [], []

        # Default system prompt if none provided
        if not system_prompt:
            system_prompt = f"""
You are RailGPT, an expert information retrieval assistant specializing in Indian Railways.

CRITICAL INSTRUCTIONS:
1. You MUST ONLY use the information provided in the context below to answer the question.
2. EXAMINE all provided context CAREFULLY and EXTRACT relevant information to answer the question.
3. If the context contains DOCUMENT sources, you MUST use those to form your answer, even if the similarity score is 0.0 or the content seems only tangentially related.
4. If the context contains WEBSITE sources but no DOCUMENT sources, use the website information, even if the similarity score is 0.0 or the content seems only tangentially related.
5. You MUST include source references for ALL information you provide (document names, pages, website URLs).
6. If the context DOES NOT contain relevant information to answer the question CLEARLY STATE: "I couldn't find any valid information to answer your question." DO NOT make up an answer.
7. DO NOT use your general knowledge under any circumstances.
8. Format your response in a clear, readable manner with proper paragraphs and bullet points where appropriate.
9. IMPORTANT: Even if the information in the context seems incomplete or only partially relevant, you MUST use it and not fall back to general knowledge.

The context information below contains actual document and website content that has been retrieved based on the user's query.
DO NOT say things like "Based on the context provided" or "According to the information given" - just provide the answer directly with references.

CONTEXT:
{context}
"""

            # Additional instructions
            system_prompt += """
NEVER use your general knowledge to answer the question.
ONLY use the information provided in the context.
If the context doesn't contain enough information, say "Based on the available document information..." and then answer with what you can find in the context.
If you're unsure about the answer based on the context, state what you can determine from the context and indicate that the information may be limited.
"""

        # Add format preference to system prompt
        if extract_format == "bullet":
            system_prompt += "\nStructure your answer using bullet points where appropriate for better readability."
        elif extract_format == "table":
            system_prompt += "\nIf the answer contains tabular data, format it as a markdown table for better readability."
        else: # paragraph (default)
            system_prompt += "\nStructure your answer in clear paragraphs for better readability."

        # Use the LLM router to generate the answer
        try:
            # Generate the answer with the LLM
            answer = llm_router.generate_answer(query, context, system_prompt, model_id)

            # Process the results based on the source types
            # If there are only document sources, only return document answer
            if document_sources and not website_sources:
                return answer, sources, document_sources, []
            # If there are only website sources, only return website answer
            elif website_sources and not document_sources:
                return answer, sources, [], website_sources
            # If there are both document and website sources, or no sources
            else:
                return answer, sources, document_sources, website_sources

        except Exception as e:
            logger.error(f"Error generating LLM answer: {str(e)}")
            # Fallback to a simpler prompt
            try:
                fallback_answer = llm_router.generate_answer(
                    query=query,
                    context="",  # Empty context for fallback
                    system_prompt="You are an AI assistant. Answer this question to the best of your ability: " + query,
                    model_id=model_id
                )
                return fallback_answer, [], [], []
            except Exception as fallback_err:
                logger.error(f"Fallback also failed: {str(fallback_err)}")
                return "I'm sorry, I couldn't process your question. Please try again.", [], [], []

    except Exception as e:
        # Catch-all for any other errors
        logger.error(f"Unexpected error in generate_llm_answer with {model_id}: {str(e)}")
        return f"I encountered an error while trying to generate an answer: {str(e)}", [], [], []

def load_documents(data_dir: str = './data'):
    """Load and process all supported documents from a directory."""
    global DOCUMENT_CHUNKS
    DOCUMENT_CHUNKS = []

    logger.info(f"Loading documents on startup")

    # Load document chunks from Supabase first using the correct method
    try:
        from supabase_client import supabase
        logger.info("Loading all document chunks from Supabase")

        # Use the table() method for better compatibility
        chunks_query = "SELECT * FROM document_chunks"
        chunks_result = supabase.execute_query(chunks_query)
        
        logger.info(f"Loaded {len(chunks_result) if chunks_result else 0} document chunks from Supabase")
        
        # Load document chunks
        if chunks_result and len(chunks_result) > 0:
            for chunk in chunks_result:
                # Get document info for each chunk
                try:
                    doc_query = f"SELECT display_name, file_path FROM documents WHERE id = '{chunk.get('document_id')}'"
                    doc_result = supabase.execute_query(doc_query)
                    
                    if doc_result and len(doc_result) > 0:
                        doc_info = doc_result[0]
                        chunk['filename'] = doc_info.get('display_name', 'Unknown')
                        chunk['url'] = doc_info.get('file_path', '')
                    else:
                        chunk['filename'] = 'Unknown Document'
                        chunk['url'] = ''
                    
                    chunk['source_type'] = 'document'
                    chunk['similarity'] = 0.85
                    
                    # Add embedding if needed
                    if "embedding" not in chunk or not chunk["embedding"]:
                        chunk_text = chunk.get("text", "")
                        if chunk_text:
                            try:
                                chunk["embedding"] = generate_embedding(chunk_text)
                            except Exception as e:
                                logger.warning(f"Failed to generate embedding for chunk {chunk.get('id', 'unknown')}: {str(e)}")
                                chunk["embedding"] = [0.01] * 768
                        else:
                            chunk["embedding"] = [0.01] * 768
                    
                    DOCUMENT_CHUNKS.append(chunk)
                except Exception as e:
                    logger.warning(f"Error processing document chunk {chunk.get('id', 'unknown')}: {str(e)}")
                    
        logger.info(f"Loaded {len([c for c in DOCUMENT_CHUNKS if c.get('source_type') == 'document'])} document chunks from Supabase")
    
    except Exception as e:
        logger.error(f"Error loading document chunks from Supabase: {str(e)}")

    # Also load website chunks from Supabase
    try:
        from supabase_client import supabase
        logger.info("Loading all website chunks from Supabase")

        # Use the table() method for better compatibility
        chunks_query = "SELECT * FROM website_chunks"
        chunks_result = supabase.execute_query(chunks_query)
        
        logger.info(f"Loaded {len(chunks_result) if chunks_result else 0} website chunks from Supabase")
        
        # Load website chunks
        if chunks_result and len(chunks_result) > 0:
            for chunk in chunks_result:
                # Get website info for each chunk
                try:
                    website_query = f"SELECT url, domain FROM websites WHERE id = '{chunk.get('website_id')}'"
                    website_result = supabase.execute_query(website_query)
                    
                    if website_result and len(website_result) > 0:
                        website_info = website_result[0]
                        chunk['url'] = website_info.get('url', 'Unknown URL')
                        chunk['domain'] = website_info.get('domain', 'Unknown Domain')
                    else:
                        chunk['url'] = 'Unknown Website'
                        chunk['domain'] = 'Unknown Domain'
                    
                    chunk['source_type'] = 'website'
                    chunk['similarity'] = 0.70
                    
                    # Add embedding if needed
                    if "embedding" not in chunk or not chunk["embedding"]:
                        chunk_text = chunk.get("text", "")
                        if chunk_text:
                            try:
                                chunk["embedding"] = generate_embedding(chunk_text)
                            except Exception as e:
                                logger.warning(f"Failed to generate embedding for website chunk {chunk.get('id', 'unknown')}: {str(e)}")
                                chunk["embedding"] = [0.01] * 768
                        else:
                            chunk["embedding"] = [0.01] * 768
                    
                    DOCUMENT_CHUNKS.append(chunk)
                except Exception as e:
                    logger.warning(f"Error processing website chunk {chunk.get('id', 'unknown')}: {str(e)}")
                    
        logger.info(f"Loaded {len([c for c in DOCUMENT_CHUNKS if c.get('source_type') == 'website'])} website chunks from Supabase")
    
    except Exception as e:
        logger.error(f"Error loading website chunks from Supabase: {str(e)}")

    # Ensure data directory exists
    if not os.path.exists(data_dir):
        logger.warning(f"No supported document files found in {data_dir}")
        return DOCUMENT_CHUNKS

    # Get all supported document files in the directory
    supported_extensions = ['.pdf', '.docx', '.doc', '.txt']
    document_files = []

    for ext in supported_extensions:
        files = [f for f in os.listdir(data_dir) if f.lower().endswith(ext)]
        document_files.extend(files)

    if not document_files:
        logger.warning(f"No supported document files found in {data_dir}")

    return DOCUMENT_CHUNKS

@app.on_event("startup")
async def startup_event():
    """Load documents on server startup and check Supabase configuration."""
    logger.info("Loading documents on startup")
    load_documents()

    # Log how many chunks were loaded
    logger.info(f"Loaded {len(DOCUMENT_CHUNKS)} document chunks on startup")

    # CLEAR and reload chunks properly from Supabase database
    try:
        logger.info("Loading additional chunks from Supabase database...")
        
        # Clear existing chunks first to avoid duplicates
        DOCUMENT_CHUNKS.clear()
        
        # Load document chunks from database
        doc_chunks = supabase.table("document_chunks").select("*").execute()
        if doc_chunks.data:
            for chunk in doc_chunks.data:
                chunk['source_type'] = 'document'
                # Get filename from documents table if not present
                if not chunk.get('filename') and chunk.get('document_id'):
                    try:
                        doc_result = supabase.table("documents").select("display_name, file_path").eq("id", chunk['document_id']).execute()
                        if doc_result.data:
                            chunk['filename'] = doc_result.data[0].get('display_name', 'Unknown')
                            chunk['url'] = doc_result.data[0].get('file_path', '')
                        else:
                            chunk['filename'] = 'Unknown document'
                            chunk['url'] = ''
                    except:
                        chunk['filename'] = 'Unknown document'
                        chunk['url'] = ''
                        
            DOCUMENT_CHUNKS.extend(doc_chunks.data)
            logger.info(f"✅ Loaded {len(doc_chunks.data)} document chunks from database")
        else:
            logger.warning("No document chunks found in database")
        
        # Load website chunks from database
        web_chunks = supabase.table("website_chunks").select("*").execute()
        if web_chunks.data:
            for chunk in web_chunks.data:
                chunk['source_type'] = 'website'
                # Get website info
                if chunk.get('website_id'):
                    try:
                        website_result = supabase.table("websites").select("url, domain").eq("id", chunk['website_id']).execute()
                        if website_result.data:
                            chunk['url'] = website_result.data[0].get('url', 'Unknown URL')
                            chunk['domain'] = website_result.data[0].get('domain', 'Unknown Domain')
                    except:
                        chunk['url'] = 'Unknown URL'
                        chunk['domain'] = 'Unknown Domain'
                        
            DOCUMENT_CHUNKS.extend(web_chunks.data)
            logger.info(f"✅ Loaded {len(web_chunks.data)} website chunks from database")
        else:
            logger.warning("No website chunks found in database")
        
        logger.info(f"🎉 Total chunks now in memory: {len(DOCUMENT_CHUNKS)}")
        
    except Exception as e:
        logger.error(f"Error loading chunks from database: {str(e)}")

    # Check Supabase configuration
    try:
        # Check if Supabase is configured
        if os.getenv("USE_SUPABASE", "true").lower() == "true":
            # Test Supabase connection
            result = supabase.execute_query("SELECT 1 as test")
            if "error" in result:
                logger.error(f"Supabase connection test failed: {result['error']}")
            else:
                logger.info("Supabase connection test successful")

                # Skip schema checks and directly try to create/access buckets
                try:
                    logger.info("Checking Supabase storage setup...")

                    # Directly try to create the documents bucket
                    # This will either succeed or fail gracefully with a mock response
                    bucket_result = supabase.create_bucket_if_not_exists("documents")

                    if "error" in bucket_result:
                        logger.info(f"Using mock storage bucket for 'documents': {bucket_result.get('id')}")
                    else:
                        logger.info(f"Successfully accessed/created bucket: {bucket_result.get('id')}")

                    # Create additional buckets if needed
                    supabase.create_bucket_if_not_exists("websites", is_public=True)
                    supabase.create_bucket_if_not_exists("images", is_public=True)
                except Exception as e:
                    logger.warning(f"Error setting up Supabase storage: {str(e)}")
                    logger.info("Will use mock storage functionality when needed.")
    except Exception as e:
        logger.error(f"Error checking Supabase configuration: {str(e)}")

# Root endpoint
@app.get("/")
async def read_root():
    return {"message": "Document Management System API is running"}

# Health check endpoint
@app.get("/api/health")
async def health_check():
    return {"status": "ok", "message": "API is running"}

# Environment variables check endpoint
@app.get("/api/env-check")
async def env_check():
    """Check environment variables for debugging"""
    import os
    return {
        "SUPABASE_URL": os.getenv("SUPABASE_URL", "NOT_SET"),
        "SUPABASE_KEY_SET": "YES" if os.getenv("SUPABASE_KEY") else "NO",
        "SUPABASE_ANON_KEY_SET": "YES" if os.getenv("SUPABASE_ANON_KEY") else "NO",
        "supabase_client_status": "initialized" if hasattr(supabase, 'supabase') and supabase.supabase else "not_initialized"
    }

# Alternative health check endpoint (for compatibility)
@app.get("/health")
async def health_check_alt():
    return {"status": "ok", "message": "API is running"}

@app.get("/api/debug/paths")
async def debug_paths():
    """Debug endpoint to check server paths"""
    import os

    cwd = os.getcwd()
    script_dir = os.path.dirname(os.path.abspath(__file__))

    test_files = ["SampleRailwayDoc.pdf", "ACP 110V.docx"]
    file_checks = {}

    for filename in test_files:
        possible_paths = [
            os.path.join(script_dir, "data", "uploads", filename),
            os.path.join(script_dir, "data", filename),
            f"./backend/data/uploads/{filename}",
            f"./data/uploads/{filename}",
            f"./uploads/{filename}",
        ]

        file_checks[filename] = []
        for path in possible_paths:
            file_checks[filename].append({
                "path": path,
                "exists": os.path.exists(path)
            })

    return {
        "current_working_directory": cwd,
        "script_directory": script_dir,
        "file_checks": file_checks
    }

@app.get("/api/test/view/{filename}")
async def test_view_document(filename: str):
    """Test endpoint to check if route matching works"""
    return {
        "message": f"Test endpoint reached with filename: {filename}",
        "decoded_filename": filename
    }

# Clear database endpoint (for testing only)
@app.post("/api/clear-database")
async def clear_database():
    """Clear all data from the database (for testing purposes)"""
    try:
        result = supabase.clear_all_data()
        return {"message": "Database cleared successfully", "result": result}
    except Exception as e:
        logger.error(f"Error clearing database: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error clearing database: {str(e)}")

# Get all documents endpoint
@app.get("/api/documents")
async def get_documents():
    """Get all documents from Supabase database"""
    try:
        logger.info("Fetching documents from Supabase database")

        # First, let's see what columns are actually available in the documents table
        try:
            # Get table schema first
            schema_query = "SELECT * FROM documents LIMIT 1"
            schema_result = supabase.execute_query(schema_query)
            if schema_result:
                logger.info(f"Sample document record: {schema_result[0] if schema_result else 'No records'}")
        except Exception as e:
            logger.error(f"Error checking table schema: {str(e)}")

        # Query documents from Supabase with proper error handling
        documents_query = """
        SELECT * FROM documents
        ORDER BY created_at DESC
        """

        result = supabase.execute_query(documents_query)

        if result and len(result) > 0:
            # Process the results to match frontend expectations
            processed_documents = []
            for doc in result:
                logger.info(f"Processing document: {doc}")

                # Map Supabase fields to frontend expected fields
                processed_doc = {
                    "id": doc.get("id", "unknown"),
                    "name": doc.get("display_name") or doc.get("file_name") or doc.get("name") or "Unknown Document",
                    "uploadedAt": doc.get("created_at", ""),
                    "fileType": doc.get("file_type", "unknown"),
                    "fileSize": doc.get("file_size", 0),
                    "filePath": doc.get("file_path", ""),
                    "uploadedBy": doc.get("uploaded_by", "unknown"),
                    "status": doc.get("status", "processed"),
                    "qualityScore": doc.get("extraction_quality", 85),
                    "mainCategory": "Documents",
                    "category": "Uploaded",
                    # Additional fields for debugging
                    "originalData": doc  # Keep original data for debugging
                }
                processed_documents.append(processed_doc)

            logger.info(f"Found {len(processed_documents)} documents in database")
            return processed_documents
        else:
            logger.info("No documents found in database")
            return []

    except Exception as e:
        logger.error(f"Error fetching documents from Supabase: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        # Return empty list on error instead of mock data
        return []

# Get all websites endpoint
@app.get("/api/websites")
async def get_websites():
    """Get all websites from Supabase database"""
    try:
        logger.info("Fetching websites from Supabase database")

        # First, let's see what columns are actually available in the websites table
        try:
            # Get table schema first
            schema_query = "SELECT * FROM websites LIMIT 1"
            schema_result = supabase.execute_query(schema_query)
            if schema_result:
                logger.info(f"Sample website record: {schema_result[0] if schema_result else 'No records'}")
        except Exception as e:
            logger.error(f"Error checking websites table schema: {str(e)}")

        # Query websites from Supabase with proper error handling
        websites_query = """
        SELECT * FROM websites
        ORDER BY created_at DESC
        """

        result = supabase.execute_query(websites_query)

        if result and len(result) > 0:
            # Process the results to match frontend expectations
            processed_websites = []
            for site in result:
                logger.info(f"Processing website: {site}")

                # Extract domain from URL
                url = site.get("url", "")
                try:
                    from urllib.parse import urlparse
                    domain = urlparse(url).netloc.replace('www.', '')
                except:
                    domain = url

                # Map Supabase fields to frontend expected fields
                processed_site = {
                    "id": site.get("id", "unknown"),
                    "name": site.get("name") or site.get("title") or domain,
                    "url": url,
                    "domain": domain,
                    "extractedAt": site.get("created_at", ""),
                    "status": "Success" if site.get("status", "processed") == "processed" else "Failed",
                    "submittedBy": site.get("submitted_by", "unknown"),
                    "domainCategory": site.get("category", "General"),
                    # Hierarchical category fields
                    "mainCategory": site.get("main_category"),
                    "category": site.get("category_level2"),
                    "subCategory": site.get("sub_category"),
                    "minorCategory": site.get("minor_category"),
                    # Additional fields for debugging
                    "originalData": site  # Keep original data for debugging
                }
                processed_websites.append(processed_site)

            logger.info(f"Found {len(processed_websites)} websites in database")
            return processed_websites
        else:
            logger.info("No websites found in database")
            return []

    except Exception as e:
        logger.error(f"Error fetching websites from Supabase: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        # Return empty list on error instead of mock data
        return []


def format_visual_content_for_frontend(metadata, content_type):
    """Format visual content metadata for frontend consumption."""
    if not metadata or content_type == "text":
        return None
    
    formatted_content = {}
    
    if content_type == "table":
        # For tables, extract the table data and markdown
        if "table_data" in metadata:
            formatted_content["table_data"] = metadata["table_data"]
        if "markdown_table" in metadata:
            formatted_content["markdown_table"] = metadata["markdown_table"]
        if "text_table" in metadata:
            formatted_content["text_table"] = metadata["text_table"]
    
    elif content_type == "image":
        # For images, extract base64 data and metadata from nested structure
        if "visual_content" in metadata and "images" in metadata["visual_content"]:
            images = metadata["visual_content"]["images"]
            if isinstance(images, list) and len(images) > 0:
                # Use the first image for now (could be enhanced to show all)
                first_image = images[0]
                if "base64_data" in first_image:
                    formatted_content["base64_data"] = first_image["base64_data"]
                # Store all images for potential display of multiple images
                formatted_content["images"] = images
        # Fallback: look for direct base64_data in metadata
        elif "base64_data" in metadata:
            formatted_content["base64_data"] = metadata["base64_data"]
        if "image_url" in metadata:
            formatted_content["image_url"] = metadata["image_url"]
    
    # Add common fields
    if "storage_url" in metadata:
        formatted_content["storage_url"] = metadata["storage_url"]
    if "visual_content_type" in metadata:
        formatted_content["visual_content_type"] = metadata["visual_content_type"]
    if "display_type" in metadata:
        formatted_content["display_type"] = metadata["display_type"]
    
    return formatted_content

def generate_clean_answer_with_sources(query: str, chunks: List[Dict[str, Any]], source_type: str, model_id: str = "gemini-2.0-flash", extract_format: str = "paragraph"):
    """
    Generate a clean answer with properly formatted sources for a specific source type.

    Args:
        query: The user's question
        chunks: List of chunks from a single source type (document OR website)
        source_type: Either "document" or "website"
        model_id: LLM model to use
        extract_format: Preferred format for the extraction

    Returns:
        Tuple of (answer_text, clean_sources_list, visual_content_found, visual_content_types)
    """
    try:
        logger.info(f"Generating clean {source_type} answer from {len(chunks)} chunks")

        # Set minimum relevance thresholds
        min_doc_threshold = 0.25  # Increased threshold to find more relevant matches
        min_web_threshold = 0.15  # Increased threshold for websites
        min_threshold = min_doc_threshold if source_type == "document" else min_web_threshold

        # Filter chunks by relevance first
        relevant_chunks = []
        for chunk in chunks:
            similarity = chunk.get('similarity', 0.0)
            chunk_text = chunk.get('text', '')

            # Skip chunks with very low similarity or empty text
            if similarity < min_threshold or not chunk_text.strip():
                logger.info(f"Skipping {source_type} chunk with similarity {similarity:.3f} (below threshold {min_threshold})")
                continue

            # Add minimum text length requirement
            if len(chunk_text) < 20:
                logger.info(f"Skipping {source_type} chunk with text length {len(chunk_text)} (below minimum 20 characters)")
                continue

            relevant_chunks.append(chunk)

        # If no relevant chunks found, return empty result
        if not relevant_chunks:
            logger.info(f"No relevant {source_type} chunks found above threshold {min_threshold} and minimum text length 20 characters")
            return f"I couldn't find relevant information in the {source_type} content to answer your question.", [], False, []

        logger.info(f"Processing {len(relevant_chunks)} relevant {source_type} chunks (out of {len(chunks)} total)")

        # Prepare context from relevant chunks only
        context_texts = []
        sources_dict = {}  # Use dict to avoid duplicates

        for chunk in relevant_chunks:
            chunk_text = chunk.get("text", "")
            similarity = chunk.get("similarity", 0)

            if source_type == "document":
                # Document source processing
                document_id = chunk.get("document_id")
                # First try to get filename from document_id (most reliable)
                filename = None
                if document_id:
                    try:
                        from supabase_client import supabase
                        doc_query = f"SELECT COALESCE(display_name, file_name, name) as filename FROM documents WHERE id = '{document_id}'"
                        doc_result = supabase.execute_query(doc_query)
                        if doc_result and len(doc_result) > 0:
                            filename = doc_result[0]['filename']
                            logger.info(f"Retrieved filename '{filename}' for document_id '{document_id}'")
                    except Exception as e:
                        logger.error(f"Error retrieving document name: {str(e)}")
                
                # Fall back to chunk metadata if needed
                if not filename:
                    filename = chunk.get("filename") or chunk.get("title") or "Unknown document"
                
                page = chunk.get("page") or chunk.get("page_number") or 1

                # Check for visual content
                metadata = chunk.get("metadata", {})
                content_type = metadata.get("content_type", "text")
                visual_content_type = metadata.get("visual_content_type")
                storage_url = metadata.get("storage_url")
                display_type = metadata.get("display_type", "text")

                # Enhanced context for visual content
                if content_type in ["table", "image", "chart_diagram"]:
                    context_texts.append(f"From '{filename}' (page {page}, {content_type}, relevance: {similarity:.2f}):\n{chunk_text}\n")
                else:
                    context_texts.append(f"From '{filename}' (page {page}, relevance: {similarity:.2f}):\n{chunk_text}\n")

                # Create unique source key for deduplication (include content type for visual content)
                if content_type != "text":
                    source_key = f"{filename}_{content_type}_{page}"
                else:
                    # Group by filename for text content to ensure all pages are tracked together
                    source_key = f"{filename}"
                    
                logger.info(f"Processing chunk from document '{filename}', page {page}, content_type={content_type}")
                    
                if source_key not in sources_dict:
                    sources_dict[source_key] = {
                        "source_type": "document",
                        "filename": filename,
                        "name": os.path.basename(filename),
                        "pages": [],
                        "content_type": content_type,
                        "visual_content_type": visual_content_type,
                        "storage_url": storage_url,
                        "display_type": display_type,
                        "visual_content": format_visual_content_for_frontend(metadata, content_type) if content_type != "text" else None
                    }

                # Add page if not already present
                if page not in sources_dict[source_key]["pages"]:
                    sources_dict[source_key]["pages"].append(page)

            elif source_type == "website":
                # Website source processing - extract URL from multiple possible fields
                url = chunk.get("url", "")

                # If no URL field, try to extract from text
                if not url or url == "Unknown":
                    chunk_text_for_url = chunk.get("text", "")
                    # Look for URL patterns in the text
                    import re
                    url_match = re.search(r'URL:\s*(https?://[^\s\n]+)', chunk_text_for_url)
                    if url_match:
                        url = url_match.group(1)
                    else:
                        # Look for website domain patterns
                        domain_match = re.search(r'Website:\s*([^\s,\n]+)', chunk_text_for_url)
                        if domain_match:
                            domain = domain_match.group(1)
                            url = f"https://{domain}" if not domain.startswith('http') else domain
                        # Special case handling for known websites
                        elif "rapidresponseapp" in chunk_text_for_url.lower():
                            url = "https://www.rapidresponseapp.com"
                        elif "indianrailways.gov.in" in chunk_text_for_url.lower():
                            url = "https://indianrailways.gov.in"
                        elif "irctc" in chunk_text_for_url.lower():
                            url = "https://www.irctc.co.in"
                        else:
                            url = "Unknown website"

                # Add to context
                context_texts.append(f"From '{url}' (relevance: {similarity:.2f}):\n{chunk_text}\n")

                # Create unique source key for deduplication
                source_key = url
                if source_key not in sources_dict:
                    sources_dict[source_key] = {
                        "source_type": "website",
                        "url": url
                    }

        # Convert sources dict to clean list with relevance scoring
        source_relevance = []
        for source_data in sources_dict.values():
            if source_type == "document":
                # Calculate relevance score for this document source
                # Find the highest similarity chunk for this document
                max_similarity = 0
                for chunk in relevant_chunks:
                    if chunk.get("filename") == source_data["filename"]:
                        chunk_similarity = chunk.get("similarity", 0)
                        if chunk_similarity > max_similarity:
                            max_similarity = chunk_similarity

                # Sort pages and create page reference
                pages = sorted(source_data["pages"])
                logger.info(f"Document '{source_data['name']}' has pages: {pages}")
                if len(pages) == 1:
                    page_ref = f"Page {pages[0]}"
                else:
                    page_ref = f"Pages {', '.join(map(str, pages))}"
                logger.info(f"Page reference for '{source_data['name']}': {page_ref}")

                # Enhanced source with visual content information
                clean_source = {
                    "source_type": "document",
                    "filename": source_data["filename"],
                    "name": source_data["name"],
                    "page": pages[0],  # First page for link generation
                    "link": f"/viewer?file={source_data['filename']}&page={pages[0]}",
                    "display_text": f"{source_data['name']} – {page_ref}",
                    "relevance_score": max_similarity,
                    "content_type": source_data.get("content_type", "text"),
                    "visual_content": source_data.get("visual_content"),
                    "storage_url": source_data.get("storage_url"),
                    "display_type": source_data.get("display_type", "text")
                }
                source_relevance.append(clean_source)

            elif source_type == "website":
                # Calculate relevance score for this website source
                max_similarity = 0
                for chunk in relevant_chunks:
                    if chunk.get("url") == source_data["url"]:
                        chunk_similarity = chunk.get("similarity", 0)
                        if chunk_similarity > max_similarity:
                            max_similarity = chunk_similarity

                clean_source = {
                    "source_type": "website",
                    "url": source_data["url"],
                    "display_text": source_data["url"],
                    "relevance_score": max_similarity
                }
                source_relevance.append(clean_source)
                logger.info(f"Added website source: {source_data['url']} (relevance: {max_similarity:.3f})")

        # Sort sources by relevance score (highest first), prioritizing visual content
        def source_sort_key(source):
            relevance = source.get("relevance_score", 0)
            content_type = source.get("content_type", "text")
            # Boost visual content slightly in ranking
            visual_boost = 0.1 if content_type != "text" else 0
            return relevance + visual_boost
            
        source_relevance.sort(key=source_sort_key, reverse=True)

        # Include all document sources to ensure all page references are shown properly
        # For websites, still limit to top 2 most relevant sources for better UX
        max_sources = len(source_relevance) if source_type == "document" else 2
        logger.info(f"Including up to {max_sources} {source_type} sources to show all page references")
        clean_sources = []
        visual_content_found = False
        visual_content_types = []
        
        for source in source_relevance[:max_sources]:
            # Track visual content
            content_type = source.get("content_type", "text")
            if content_type != "text":
                visual_content_found = True
                if content_type not in visual_content_types:
                    visual_content_types.append(content_type)
            
            # Remove the relevance_score from the final source object
            final_source = {k: v for k, v in source.items() if k != "relevance_score"}
            clean_sources.append(final_source)

        logger.info(f"Filtered to top {len(clean_sources)} most relevant {source_type} sources (from {len(source_relevance)} total)")

        # Don't create fallback sources - if no clean sources can be created from chunks,
        # it means the chunks are not relevant and we should let the system fall back to LLM
        if not clean_sources and chunks:
            logger.info(f"No clean sources created from {len(chunks)} chunks - chunks appear to be irrelevant")

        # Combine context
        context = "\n\n".join(context_texts)

        # Create specialized system prompt for this source type
        visual_instruction = ""
        if visual_content_found:
            visual_instruction = f"""
VISUAL CONTENT AVAILABLE:
The following document context includes visual content (tables, images, diagrams). 
When referencing visual content:
- For tables: Describe the data structure and key information
- For images: Reference their content and purpose 
- For diagrams: Explain what they illustrate
- Note that visual content is available in the source documents for detailed viewing
"""

        if source_type == "document":
            system_prompt = f"""You are RailGPT, an AI assistant that helps users with questions about Indian Railways and general topics.
You have been provided with relevant document content to answer the user's question.

{visual_instruction}

INSTRUCTIONS:
1. Answer the user's question using ONLY the provided document context
2. Be specific and detailed in your response
3. If the documents contain visual content (tables, images, charts), reference them appropriately
4. If the provided context doesn't contain enough information to fully answer the question, say so clearly
5. Format your answer in {extract_format} format
6. Do not make up information not present in the documents

DOCUMENT CONTEXT:
{context}

USER QUESTION: {query}

Provide a comprehensive answer based on the document content."""

        else:  # website
            system_prompt = f"""You are RailGPT, an AI assistant that helps users with questions about Indian Railways and general topics.
You have been provided with relevant website content to answer the user's question.

INSTRUCTIONS:
1. Answer the user's question using ONLY the provided website context
2. Be specific and detailed in your response
3. If the provided context doesn't contain enough information to fully answer the question, say so clearly
4. Format your answer in {extract_format} format
5. Do not make up information not present in the websites

WEBSITE CONTEXT:
{context}

USER QUESTION: {query}

Provide a comprehensive answer based on the website content."""

        # Generate answer using LLM
        answer = llm_router.generate_answer(
            query=query,
            context=context,
            system_prompt=system_prompt,
            model_id=model_id
        )

        # Check if the AI says the content is not relevant
        # But be less aggressive for visual content queries
        visual_query_keywords = ["image", "picture", "table", "chart", "diagram", "project", "quotation"]
        is_visual_query = any(keyword in query.lower() for keyword in visual_query_keywords)
        
        irrelevant_phrases = [
            "cannot answer",
            "cannot find",
            "does not contain",
            "do not contain",
            "not mentioned",
            "not available",
            "no information",
            "doesn't contain",
            "don't contain",
            "cannot provide",
            "not found in",
            "not present in"
        ]

        answer_lower = answer.lower()
        
        # For visual queries, be much more lenient - only reject if explicitly states irrelevance
        if is_visual_query:
            explicit_irrelevance_phrases = [
                "documents have no information about",
                "documents do not contain any information about",
                "no relevant information in the documents",
                "documents are not relevant to"
            ]
            is_irrelevant = any(phrase in answer_lower for phrase in explicit_irrelevance_phrases)
        else:
            is_irrelevant = any(phrase in answer_lower for phrase in irrelevant_phrases)

        if is_irrelevant:
            logger.info(f"AI indicated {source_type} content is irrelevant - returning empty sources")
            return answer, [], False, []

        logger.info(f"Generated clean {source_type} answer with {len(clean_sources)} sources")
        logger.info(f"Visual content found: {visual_content_found}, types: {visual_content_types}")
        for source in clean_sources:
            if source_type == "website":
                logger.info(f"Website source in final result: {source.get('url', 'Unknown')}")

        return answer, clean_sources, visual_content_found, visual_content_types

    except Exception as e:
        logger.error(f"Error generating clean {source_type} answer: {str(e)}")
        return f"I couldn't generate an answer from the {source_type} information.", [], False, []

# Chunks endpoint
@app.get("/api/chunks")
async def get_chunks():
    # Return document chunks without embeddings for response size efficiency
    chunks_without_embeddings = []
    for chunk in DOCUMENT_CHUNKS:
        chunk_copy = dict(chunk)
        if "embedding" in chunk_copy:
            del chunk_copy["embedding"]
        chunks_without_embeddings.append(chunk_copy)

    return chunks_without_embeddings

@app.post("/api/query", response_model=QueryResponse)
async def query(request: QueryRequest):
    logger.info(f"Processing query: {request.query} using model: {request.model}")

    # Initialize variables
    used_fallback_model = False
    original_model = request.model
    fallback_reason = None
    document_answer = None
    website_answer = None
    combined_answer = ""
    combined_sources = []
    document_sources = []
    website_sources = []
    llm_fallback_used = False
    model_id = request.model
    answer_source = "Unknown"

    try:
        # Validate the requested model and API key
        if not llm_router.is_model_available(request.model):
            logger.warning(f"Model {request.model} not available, falling back to default model")
            model_id = llm_router.DEFAULT_MODEL
            used_fallback_model = True
            fallback_reason = f"Model '{request.model}' is not available"
        else:
            # Check if API key is valid for this model
            model_config = llm_router.get_model_config(model_id)
            key_status = llm_router.check_api_key_validity(model_config["provider"])
            if not key_status.get("valid", False):
                logger.warning(f"Invalid API key for {model_id}, falling back to default model")
                model_id = llm_router.DEFAULT_MODEL
                used_fallback_model = True
                fallback_reason = f"API key for {original_model} is invalid: {key_status.get('message', '')}"

        logger.info("=== IMPLEMENTING STRICT PRIORITY ANSWER DISPLAY LOGIC ===")

        # Generate query embedding for vector search
        logger.info("🔍 Generating query embedding...")
        try:
            query_embedding = generate_embedding(request.query, model_id)
            logger.info(f"✅ Query embedding generated successfully (size: {len(query_embedding) if query_embedding else 0})")
        except Exception as e:
            logger.error(f"❌ Failed to generate query embedding: {str(e)}")
            query_embedding = None

        # STEP 1: Search Document Chunks (HIGHEST PRIORITY)
        logger.info("=== STEP 1: SEARCHING DOCUMENT CHUNKS (HIGHEST PRIORITY) ===")
        document_chunks = None
        document_answer = None
        document_sources = []
        
        # Initialize visual content tracking variables
        doc_visual_found = False
        doc_visual_types = []

        try:
            document_chunks = search_supabase_document_chunks_enhanced(
                query_embedding=query_embedding, 
                query_text=request.query, 
                use_hybrid_search=request.use_hybrid_search,
                visual_query_info=detect_visual_query(request.query)
            )
            if document_chunks:
                logger.info(f"✅ STEP 1: Found {len(document_chunks)} relevant document chunks")
                for i, chunk in enumerate(document_chunks[:3], 1):
                    logger.info(f"   {i}. Similarity: {chunk.get('similarity', 0):.3f} | {chunk.get('filename', 'Unknown')}")
                
                # Generate clean document answer
                document_answer, document_sources, doc_visual_found, doc_visual_types = generate_clean_answer_with_sources(
                    query=request.query,
                    chunks=document_chunks,
                    source_type="document",
                    model_id=model_id,
                    extract_format=request.extract_format
                )
                logger.info(f"✅ Document answer generated with {len(document_sources)} sources")
            else:
                logger.info("⚠️ STEP 1: No relevant document chunks found")
        except Exception as e:
            logger.error(f"Error in document search: {str(e)}")
            document_chunks = []

        # STEP 2: Search Website Chunks  
        logger.info("=== STEP 2: SEARCHING WEBSITE CHUNKS (MEDIUM PRIORITY) ===")
        website_chunks = None
        website_answer = None
        website_sources = []
        
        # Initialize visual content tracking variables
        web_visual_found = False
        web_visual_types = []

        try:
            website_chunks = search_supabase_website_chunks(
                query_embedding=query_embedding,
                query_text=request.query,
                use_hybrid_search=request.use_hybrid_search
            )
            if website_chunks:
                logger.info(f"✅ STEP 2: Found {len(website_chunks)} relevant website chunks")
                for i, chunk in enumerate(website_chunks[:3], 1):
                    url = chunk.get('url', 'Unknown URL')
                    similarity = chunk.get('similarity', 0)
                    logger.info(f"   {i}. Similarity: {similarity:.3f} | {url}")
                    
                    # Log text preview for debugging
                    text_preview = chunk.get('text', '')[:100] + "..." if len(chunk.get('text', '')) > 100 else chunk.get('text', '')
                    logger.info(f"       Text preview: {text_preview}")
                
                # Generate clean website answer
                website_answer, website_sources, web_visual_found, web_visual_types = generate_clean_answer_with_sources(
                    query=request.query,
                    chunks=website_chunks,
                    source_type="website",
                    model_id=model_id,
                    extract_format=request.extract_format
                )
                logger.info(f"✅ Website answer generated with {len(website_sources)} sources")
            else:
                logger.info("⚠️ STEP 2: No relevant website chunks found")
        except Exception as e:
            logger.error(f"Error in website search: {str(e)}")
            website_chunks = []

        # STEP 3: STRICT PRIORITY ANSWER GENERATION
        logger.info("=== STEP 3: STRICT PRIORITY ANSWER GENERATION ===")

        # Clear combined sources for clean handling
        combined_sources = []

        # Generate document answer if document chunks are available
        if document_chunks:
            logger.info(f"📄 Generating document answer from {len(document_chunks)} chunks")
            try:
                document_answer, doc_sources, doc_visual_found, doc_visual_types = generate_clean_answer_with_sources(
                    query=request.query,
                    chunks=document_chunks,
                    source_type="document",
                    model_id=model_id,
                    extract_format=request.extract_format
                )
                        
                # CRITICAL CHECK: Only use if we actually got sources
                if doc_sources and len(doc_sources) > 0:
                    document_sources = doc_sources
                    logger.info(f"✅ FIXED: Document answer generated with {len(doc_sources)} TRUE sources")
                else:
                    logger.info(f"❌ FIXED: Document answer generated but no sources - chunks were not actually relevant")
                    document_answer = None
                    has_relevant_docs = False

                    # If no sources were generated, it means the chunks were irrelevant
                    if not doc_sources:
                        logger.info(f"📄 Document chunks were irrelevant - no sources generated")
                        document_answer = None

            except Exception as e:
                logger.error(f"Error generating document answer: {str(e)}")
                document_answer = None
                document_sources = []
        else:
            document_sources = []

        # Generate website answer if website chunks are available
        if website_chunks:
            logger.info(f"🌐 Generating website answer from {len(website_chunks)} chunks")
            try:
                website_answer, web_sources, web_visual_found, web_visual_types = generate_clean_answer_with_sources(
                    query=request.query,
                    chunks=website_chunks,
                    source_type="website",
                    model_id=model_id,
                    extract_format=request.extract_format
                )
                website_sources = web_sources
                combined_sources.extend(web_sources)
                logger.info(f"✅ Website answer generated with {len(web_sources)} sources")

                # If no sources were generated, it means the chunks were irrelevant
                if not web_sources:
                    logger.info(f"🌐 Website chunks were irrelevant - no sources generated")
                    website_answer = None

            except Exception as e:
                logger.error(f"Error generating website answer: {str(e)}")
                website_answer = None
                website_sources = []
        else:
            website_sources = []

        # STEP 4: DETERMINE FINAL RESPONSE STRATEGY (FIXED LOGIC)
        logger.info("=== STEP 4: APPLYING FIXED PRIORITY LOGIC ===")

        # Check if we actually have RELEVANT content (not just any content)
        relevant_document_chunks = []
        relevant_website_chunks = []

        # FIXED: Filter documents by actual relevance (similarity > 0.35) 
        if document_chunks:
            relevant_document_chunks = document_chunks  # Pass all chunks to answer logic
            logger.info(f"Found {len(relevant_document_chunks)} document chunks to process (no pre-filtering)")
            similarities = [chunk.get('similarity', 0) for chunk in document_chunks[:5]]
            logger.info(f"Document chunk similarities: {similarities}")

        # Filter websites by actual relevance - HANDLE NEGATIVE SIMILARITIES
        if website_chunks:
            # For website chunks, we need to handle negative similarities properly
            # Sort by similarity (highest first) and take top chunks regardless of threshold
            sorted_website_chunks = sorted(website_chunks, key=lambda x: x.get('similarity', -999), reverse=True)

            # Take top 5 website chunks regardless of similarity score
            relevant_website_chunks = sorted_website_chunks[:5]

            logger.info(f"Found {len(relevant_website_chunks)} RELEVANT website chunks out of {len(website_chunks)} total (taking top 5)")

            # Log similarities for debugging
            if website_chunks:
                similarities = [chunk.get('similarity', 0) for chunk in website_chunks[:5]]
                logger.info(f"Website chunk similarities: {similarities}")

                # If all similarities are very negative (< -0.5), try text-based matching
                if all(sim < -0.5 for sim in similarities):
                    logger.info("All website chunks have very negative similarities, trying text-based fallback")
                    text_matched_chunks = []
                    query_words = set(request.query.lower().split())

                    for chunk in website_chunks:
                        chunk_text = chunk.get('text', '').lower()
                        # Check if chunk contains any query words
                        chunk_words = set(chunk_text.split())
                        common_words = query_words & chunk_words

                        if len(common_words) >= 1:  # At least one word match
                            chunk_copy = dict(chunk)
                            chunk_copy['similarity'] = 0.2  # Assign minimal similarity
                            text_matched_chunks.append(chunk_copy)

                    if text_matched_chunks:
                        logger.info(f"Text-based fallback found {len(text_matched_chunks)} website chunks")
                        relevant_website_chunks = text_matched_chunks[:5]  # Limit to 5

        # Check if we found any website chunks with the vector search
        if not relevant_website_chunks and website_chunks:
            logger.info("No relevant website chunks found with vector search, trying text-based fallback")

            # Try direct text matching for website chunks
            text_matched_chunks = []
            query_terms = request.query.lower().split()

            for chunk in website_chunks:
                chunk_text = chunk.get('text', '').lower()
                # Count how many query terms appear in the text
                matched_terms = sum(1 for term in query_terms if term in chunk_text)

                # If at least one term matches, include this chunk
                if matched_terms > 0:
                    # Create a copy with calculated similarity based on term matches
                    chunk_copy = dict(chunk)
                    # Calculate similarity as proportion of matching terms (normalized to 0.1-0.5 range)
                    term_similarity = 0.1 + (0.4 * matched_terms / len(query_terms))
                    chunk_copy['similarity'] = term_similarity
                    text_matched_chunks.append(chunk_copy)

            if text_matched_chunks:
                logger.info(f"Text-based fallback found {len(text_matched_chunks)} website chunks")
                # Sort by calculated similarity
                text_matched_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)
                # Use top 5 results
                relevant_website_chunks = text_matched_chunks[:5]
            else:
                logger.info("Text-based fallback also found no relevant website chunks")
                # Don't force irrelevant chunks - let it fall back to LLM instead
                relevant_website_chunks = []

        # PRIORITY LOGIC: Only use sources that are actually relevant AND have valid answers

        # FIXED: Check if we actually have RELEVANT content based on stricter thresholds
        has_relevant_docs = document_sources and len(document_sources) > 0 and document_answer
        has_relevant_websites = website_sources and len(website_sources) > 0 and website_answer

        # NEW LOGIC: Only show cards for ACTUALLY relevant content
        if has_relevant_docs and has_relevant_websites:
            # Both are relevant - show both cards
            answer_source = "document_and_website"
            llm_fallback_used = False
            combined_answer = document_answer or "Answer generated from document and website sources."
            combined_sources = document_sources + website_sources  # Combine all sources
            logger.info(f"🟡 BOTH RELEVANT: Document and website answers available - {len(document_sources)} document sources, {len(website_sources)} website sources")

        elif has_relevant_docs and not has_relevant_websites:
            # Only documents are relevant - show only document card
            answer_source = "document_only"
            llm_fallback_used = False
            combined_answer = document_answer or "Answer generated from document sources."
            combined_sources = document_sources  # Use only document sources
            logger.info(f"🔵 DOCUMENTS ONLY: Document answer - {len(document_sources)} sources")

            # Clear irrelevant website data
            website_answer = None
            website_sources = []

        elif has_relevant_websites and not has_relevant_docs:
            # Only websites are relevant - show only website card
            answer_source = "website_only"
            llm_fallback_used = False
            combined_answer = website_answer or "Answer generated from website sources."
            combined_sources = website_sources  # Use only website sources
            logger.info(f"🟢 WEBSITES ONLY: Website answer - {len(website_sources)} sources")

            # Clear irrelevant document data
            document_answer = None
            document_sources = []

        elif request.fallback_enabled:
            # PRIORITY 3: LLM FALLBACK - Only when no relevant chunks found
            answer_source = "llm_fallback"
            llm_fallback_used = True

            logger.info(f"🟣 PRIORITY 3: Using LLM fallback - no relevant content found")

            # Generate LLM-only answer
            fallback_prompt = f"""You are RailGPT, an AI assistant that specializes in Indian Railways.
            The user's question could not be answered from the uploaded documents or extracted websites.
            Please provide a helpful answer using your general knowledge.
            If the question is about Indian Railways, provide detailed technical information.
            Format your answer clearly with paragraphs and bullet points where appropriate.

            USER QUESTION: {request.query}

            Provide a comprehensive answer."""

            try:
                combined_answer = llm_router.generate_answer(
                    query=request.query,
                    context="",  # No context for pure LLM fallback
                    system_prompt=fallback_prompt,
                    model_id=model_id
                )
            except Exception as e:
                logger.error(f"LLM fallback failed: {str(e)}")
                combined_answer = "I'm sorry, I couldn't process your question. Please try again with a different model or rephrase your question."

            # Clear all sources for LLM fallback
            combined_sources = []
            document_sources = []
            website_sources = []
            document_answer = None
            website_answer = None

        else:
            # PRIORITY 5: FALLBACK DISABLED - No results found
            answer_source = "no_results"
            llm_fallback_used = False

            logger.info("❌ PRIORITY: Fallback disabled and no relevant chunks found")
            combined_answer = "No relevant information was found in the uploaded documents or extracted websites. Please try enabling the LLM fallback option or uploading more relevant documents."

            combined_sources = []
            document_sources = []
            website_sources = []
            document_answer = None
            website_answer = None

        # Add model fallback information if needed
        if used_fallback_model and fallback_reason:
            model_info = f"Note: Using {model_id} instead of {original_model}. {fallback_reason}."
            if combined_answer:
                combined_answer = f"{model_info}\n\n{combined_answer}"

        # Log final results for debugging
        logger.info(f"=== SEARCH COMPLETE ===")
        logger.info(f"Answer source: {answer_source}")
        logger.info(f"Document chunks: {len(document_chunks) if document_chunks else 0}")
        logger.info(f"Website chunks: {len(website_chunks) if website_chunks else 0}")
        logger.info(f"LLM fallback used: {llm_fallback_used}")
        logger.info(f"Document sources: {len(document_sources)}")
        logger.info(f"Website sources: {len(website_sources)}")

        # Track visual content from all sources
        overall_visual_found = False
        overall_visual_types = []
        
        # Track visual content from document sources
        if 'doc_visual_found' in locals() and doc_visual_found:
            overall_visual_found = True
            overall_visual_types.extend(doc_visual_types or [])
            
        # Track visual content from website sources  
        if 'web_visual_found' in locals() and web_visual_found:
            overall_visual_found = True
            overall_visual_types.extend(web_visual_types or [])
            
        # Remove duplicates from visual types
        overall_visual_types = list(set(overall_visual_types))
        
        logger.info(f"Overall visual content found: {overall_visual_found}, types: {overall_visual_types}")

        return QueryResponse(
            answer=combined_answer,
            document_answer=document_answer,
            website_answer=website_answer,
            sources=combined_sources,
            document_sources=document_sources,
            website_sources=website_sources,
            llm_model=model_id,
            llm_fallback=llm_fallback_used,
            visual_content_found=overall_visual_found,
            visual_content_types=overall_visual_types if overall_visual_types else None
        )

    except Exception as e:
        logger.error(f"Error processing query: {str(e)}")
        return QueryResponse(
            answer=f"Sorry, an error occurred while processing your query: {str(e)}",
            sources=[],
            document_sources=[],
            website_sources=[],
            llm_model=llm_router.DEFAULT_MODEL,
            llm_fallback=True,
            visual_content_found=False,
            visual_content_types=None
        )

# Document upload endpoint
@app.post("/api/upload-document")
async def upload_document(
    file: UploadFile = File(...),
    uploaded_by: Optional[str] = Form(None),
    role: Optional[str] = Form(None),
    supabase_file_path: Optional[str] = Form(None),
    supabase_file_url: Optional[str] = Form(None),
    extract_tables: Optional[bool] = Form(True),
    extract_images: Optional[bool] = Form(True),
    extract_charts: Optional[bool] = Form(True),
    # 4-level category hierarchy
    main_category_id: Optional[str] = Form(None),
    category_id: Optional[str] = Form(None),
    sub_category_id: Optional[str] = Form(None),
    minor_category_id: Optional[str] = Form(None)
):
    # Enhanced logging for troubleshooting upload issues
    logger.info(f"===== DOCUMENT UPLOAD STARTED =====")
    logger.info(f"Received document: {file.filename}, size: {file.size if hasattr(file, 'size') else 'unknown'} bytes")
    logger.info(f"Uploaded by: {uploaded_by}, role: {role}")
    logger.info(f"Supabase file path: {supabase_file_path}")
    logger.info(f"Received document upload request: {file.filename}")

    # Check if the file has content
    if not file.filename:
        raise HTTPException(status_code=400, detail="File has no filename")

    try:
        # Create uploads directory if it doesn't exist
        uploads_dir = os.path.join("data", "uploads")
        os.makedirs(uploads_dir, exist_ok=True)
        logger.info(f"Using uploads directory: {os.path.abspath(uploads_dir)}")
        
        # Check if a document with the same name already exists and delete its chunks
        try:
            # Use proper SQL escaping for filename
            escaped_filename = file.filename.replace("'", "''")
            logger.info(f"Checking if document '{escaped_filename}' already exists")
            
            # Query to find documents with matching filename or display_name
            existing_doc_query = f"""
            SELECT id, display_name 
            FROM documents 
            WHERE display_name = '{escaped_filename}' 
               OR file_path LIKE '%{escaped_filename}%'
            """
            
            existing_docs = supabase.execute_query(existing_doc_query)
            
            if existing_docs and len(existing_docs) > 0:
                for doc in existing_docs:
                    doc_id = doc.get('id')
                    doc_name = doc.get('display_name')
                    logger.info(f"Found existing document: {doc_name} (id: {doc_id}). Deleting old chunks...")
                    
                    # Delete existing chunks for this document
                    delete_chunks_query = f"DELETE FROM document_chunks WHERE document_id = '{doc_id}'"
                    supabase.execute_query(delete_chunks_query)
                    logger.info(f"Deleted old chunks for document {doc_id}")
        except Exception as cleanup_error:
            logger.warning(f"Error while cleaning up existing document chunks: {str(cleanup_error)}")
            # Continue with upload even if cleanup fails
        
        # Save the file to the uploads directory
        file_path = os.path.join(uploads_dir, file.filename)
        logger.info(f"Saving file to: {os.path.abspath(file_path)}")

        # Read the content before saving to get the actual file size
        content = await file.read()
        file_size = len(content)
        logger.info(f"Received file content size: {file_size} bytes")

        # Reset file position and save
        await file.seek(0)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        logger.info(f"File saved to {file_path}")

        # Process the document to extract content including visual elements
        # Pass Supabase file path if available
        try:
            logger.info(f"Starting document extraction from {file_path}")
            logger.info(f"Visual extraction options - Tables: {extract_tables}, Images: {extract_images}, Charts: {extract_charts}")

            # Use visual extraction for PDFs, regular extraction for other files
            _, ext = os.path.splitext(file_path)
            if ext.lower() == '.pdf' and (extract_tables or extract_images or extract_charts):
                document_chunks = extract_document_with_visual_content(
                    file_path=file_path,
                    supabase_file_path=supabase_file_path,
                    uploaded_by=uploaded_by,
                    extract_tables=extract_tables,
                    extract_images=extract_images,
                    extract_charts=extract_charts,
                    main_category_id=main_category_id,
                    category_id=category_id,
                    sub_category_id=sub_category_id,
                    minor_category_id=minor_category_id
                                )
            else:
                document_chunks = extract_document(
                    file_path=file_path,
                    supabase_file_path=supabase_file_path,
                    uploaded_by=uploaded_by,
                    main_category_id=main_category_id,
                    category_id=category_id,
                    sub_category_id=sub_category_id,
                    minor_category_id=minor_category_id
                )
            logger.info(f"Document extraction completed with {len(document_chunks)} chunks")
        except Exception as extract_error:
            logger.error(f"Error during document extraction: {str(extract_error)}")
            import traceback
            logger.error(f"Extraction error traceback: {traceback.format_exc()}")

            # Simple fallback extraction
            logger.info("Attempting fallback extraction...")
            try:
                # Simple text extraction fallback
                with open(file_path, 'rb') as f:
                    # Try to read the file as text
                    sample_content = f.read(4096)  # Read first 4KB to detect text

                is_binary = False
                try:
                    sample_content.decode('utf-8')
                except UnicodeDecodeError:
                    is_binary = True

                if not is_binary:
                    # Simple text extraction for text files
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        text = f.read()

                    # Create a minimal chunk
                    document_chunks = [{
                        "filename": os.path.basename(file_path),
                        "text": text,
                        "source_type": "document",
                        "page": 1,
                        "chunk_id": f"{os.path.splitext(os.path.basename(file_path))[0]}_1_0"
                    }]
                    logger.info(f"Fallback extraction created {len(document_chunks)} simple chunks")
                else:
                    # Binary file we can't parse
                    logger.error("Cannot extract text from binary file using fallback method")
                    document_chunks = []
            except Exception as fallback_error:
                logger.error(f"Fallback extraction also failed: {str(fallback_error)}")
                document_chunks = []

        if not document_chunks:
            raise HTTPException(
                status_code=422,
                detail=f"Failed to extract content from {file.filename}"
            )

        # Get document_id from the first chunk if available (set by extract_document when using Supabase)
        document_id = None
        if document_chunks and "document_id" in document_chunks[0]:
            document_id = document_chunks[0]["document_id"]

        # Generate embeddings for each chunk
        for chunk in document_chunks:
            # Add additional metadata
            if uploaded_by:
                chunk["uploaded_by"] = uploaded_by
            if role:
                chunk["role"] = role
            if supabase_file_path:
                chunk["supabase_file_path"] = supabase_file_path
            if supabase_file_url:
                chunk["supabase_file_url"] = supabase_file_url

            # Add embedding
            chunk_text = chunk.get("text", "")
            embedding = generate_embedding(chunk_text)
            chunk["embedding"] = embedding

        # Store each chunk in Supabase database if we have a document_id
        if document_id:
            successful_chunks = 0
            for i, chunk in enumerate(document_chunks):
                # Store chunk in Supabase
                chunk_data = supabase.store_document_chunk(
                    document_id=document_id,
                    chunk_index=chunk.get("chunk_index", i),
                    page_number=chunk.get("page_number", chunk.get("page", 1)),
                    text=chunk["text"],
                    embedding=chunk["embedding"],
                    metadata=chunk.get("metadata", {}),
                    source_type=chunk.get("source_type", "document"),
                    chunk_type=chunk.get("chunk_type", "text"),
                    content_type=chunk.get("content_type", "text")
                )

                if "error" in chunk_data:
                    logger.error(f"Error storing document chunk {i}: {chunk_data['error']}")
                else:
                    successful_chunks += 1
                    logger.info(f"Successfully stored document chunk {i} with ID: {chunk_data.get('id')}")

            logger.info(f"Successfully stored {successful_chunks} out of {len(document_chunks)} document chunks in Supabase")

        # Add to global document chunks (legacy in-memory storage)
        DOCUMENT_CHUNKS.extend(document_chunks)

        # Add to vector database for efficient search
        vector_db.add_chunks(document_chunks)

        logger.info(f"Added {len(document_chunks)} document chunks to knowledge base and vector database")

        # Return chunks without embeddings
        chunks_without_embedding = []
        for chunk in document_chunks:
            chunk_copy = dict(chunk)
            if "embedding" in chunk_copy:
                del chunk_copy["embedding"]
            chunks_without_embedding.append(chunk_copy)

        result = {
            "message": f"Successfully processed {file.filename}",
            "chunks_extracted": len(chunks_without_embedding),
            "chunks": chunks_without_embedding,
            "document_id": document_id  # Return the document ID if available
        }
        logger.info(f"Document processing successful: {len(chunks_without_embedding)} chunks extracted, document_id: {document_id}")
        logger.info(f"===== DOCUMENT UPLOAD COMPLETED =====")
        return result

    except Exception as e:
        logger.error(f"Error processing uploaded document {file.filename}: {str(e)}")
        # Print more detailed exception information for debugging
        import traceback
        logger.error(f"Exception traceback: {traceback.format_exc()}")
        logger.info(f"===== DOCUMENT UPLOAD FAILED =====")
        raise HTTPException(status_code=500, detail=f"Error processing document: {str(e)}")
    finally:
        file.file.close()

# Website add endpoint
@app.post("/api/add-website")
async def add_website(request: WebsiteAddRequest):
    url = request.url
    submitted_by = request.submitted_by
    role = request.role

    logger.info(f"Received request to add website: {url}")

    if not url or not url.strip():
        raise HTTPException(status_code=400, detail="URL cannot be empty")

    try:
        # First, store the website metadata in Supabase
        from urllib.parse import urlparse
        parsed_url = urlparse(url)
        domain = parsed_url.netloc

        # Store website in Supabase with new category structure
        website_data = supabase.store_website(
            url=url,
            domain=domain,
            title=f"{domain} Website",
            description=f"Content extracted from {url}",
            submitted_by=submitted_by or "system",
            # Use new category IDs if provided, fallback to legacy names
            main_category=request.main_category_id or request.main_category,
            category_level2=request.category_id or request.category,
            sub_category=request.sub_category_id or request.sub_category,
            minor_category=request.minor_category_id or request.minor_category
        )

        if "error" in website_data:
            logger.error(f"Error storing website metadata: {website_data['error']}")
            raise HTTPException(status_code=500, detail=f"Error storing website metadata: {website_data['error']}")

        # Get the website ID
        website_id = website_data.get("id")
        if not website_id:
            logger.error("No website ID returned from Supabase")
            raise HTTPException(status_code=500, detail="No website ID returned from Supabase")

        logger.info(f"Successfully stored website with ID: {website_id}")

        # Extract text from website using fallback extraction methods
        website_chunks = extract_website_text(url)

        if not website_chunks:
            raise HTTPException(status_code=422, detail=f"Failed to extract content from {url}")

        # Generate embeddings for each chunk and add website_id
        for i, chunk in enumerate(website_chunks):
            # Add additional metadata
            if submitted_by:
                chunk["submitted_by"] = submitted_by
            if role:
                chunk["role"] = role

            # Add website_id to each chunk (required for vector_db.add_chunks)
            chunk["website_id"] = website_id
            chunk["chunk_index"] = i

            # Add source_type if not present
            if "source_type" not in chunk:
                chunk["source_type"] = "website"

            # Add embedding
            chunk_text = chunk.get("text", "")
            embedding = generate_embedding(chunk_text)
            chunk["embedding"] = embedding

            # Add metadata field if not present
            if "metadata" not in chunk:
                chunk["metadata"] = {
                    "url": url,
                    "domain": domain,
                    "extraction_method": chunk.get("extraction_method", "unknown")
                }

        # Add to global document chunks (legacy in-memory storage)
        DOCUMENT_CHUNKS.extend(website_chunks)

        # Store each chunk in Supabase directly
        successful_chunks = 0
        for i, chunk in enumerate(website_chunks):
            # Store chunk in Supabase
            chunk_data = supabase.store_website_chunk(
                website_id=website_id,
                chunk_index=i,
                text=chunk["text"],
                embedding=chunk["embedding"],
                metadata=chunk.get("metadata", {})
            )

            if "error" in chunk_data:
                logger.error(f"Error storing chunk {i}: {chunk_data['error']}")
            else:
                successful_chunks += 1
                logger.info(f"Successfully stored chunk {i} with ID: {chunk_data.get('id')}")

        logger.info(f"Successfully stored {successful_chunks} out of {len(website_chunks)} chunks in Supabase")

        # Add to vector database for efficient search
        vector_db.add_chunks(website_chunks, source_type="website")

        logger.info(f"Added {len(website_chunks)} website chunks to knowledge base and vector database")

        # Log vector database stats
        logger.info(f"Vector database stats: {vector_db.get_stats()}")

        # Return chunks without embeddings
        chunks_without_embedding = []
        for chunk in website_chunks:
            chunk_copy = dict(chunk)
            if "embedding" in chunk_copy:
                del chunk_copy["embedding"]
            chunks_without_embedding.append(chunk_copy)

        return {
            "message": f"Successfully added website {url}",
            "chunks_extracted": len(chunks_without_embedding),
            "chunks": chunks_without_embedding,
            "website_id": website_id
        }

    except Exception as e:
        logger.error(f"Error adding website {url}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error adding website: {str(e)}")
# Document extraction details endpoint
@app.get("/api/documents/{document_id}/extraction-details")
async def get_document_extraction_details(document_id: str):
    logger.info(f"Fetching extraction details for document ID: {document_id}")

    try:
        # First try to get the document info from the database
        document_query = """
        SELECT id, display_name, extracted_content, file_type, created_at, metadata
        FROM documents 
        WHERE id = %s
        """
        
        document_result = supabase.execute_query(document_query, [document_id])
        
        if not document_result or len(document_result) == 0:
            raise HTTPException(status_code=404, detail="Document not found")
        
        document = document_result[0]
        display_name = document.get('display_name', 'Unknown Document')
        extracted_content = document.get('extracted_content')
        file_type = document.get('file_type', 'unknown')
        metadata = document.get('metadata', {})
        
        # If we have extracted content in the document record, use it for preview
        if extracted_content and extracted_content.strip():
            # Show a preview of the extracted content
            content_preview = extracted_content[:1000] + ("..." if len(extracted_content) > 1000 else "")
        else:
            # Otherwise, get content preview from document chunks
            chunks_query = """
            SELECT text, page_number, chunk_index
            FROM document_chunks 
            WHERE document_id = %s 
            ORDER BY page_number, chunk_index
            LIMIT 3
            """
            
            chunks_result = supabase.execute_query(chunks_query, [document_id])
            
            if chunks_result and len(chunks_result) > 0:
                # Combine first few chunks as sample content for extraction details
                content_preview = f"# {display_name}\n\n"
                
                for i, chunk in enumerate(chunks_result[:3]):
                    chunk_text = chunk.get('text', '')
                    page_num = chunk.get('page_number')
                    if page_num:
                        content_preview += f"**Page {page_num}:**\n"
                    content_preview += chunk_text[:300] + ("..." if len(chunk_text) > 300 else "") + "\n\n"
                
                content_preview = content_preview.strip()
            else:
                content_preview = f"# {display_name}\n\nNo extracted content available for this document."
        
        # Get chunks count and metadata
        chunks_count_query = """
        SELECT COUNT(*) as count
        FROM document_chunks 
        WHERE document_id = %s
        """
        
        chunks_count_result = supabase.execute_query(chunks_count_query, [document_id])
        chunks_count = 0
        try:
            if chunks_count_result and len(chunks_count_result) > 0:
                if isinstance(chunks_count_result[0], dict) and 'count' in chunks_count_result[0]:
                    chunks_count = chunks_count_result[0]['count']
                else:
                    chunks_count = len(chunks_count_result)
        except Exception as e:
            logger.error(f"Error parsing count result: {str(e)}")
            chunks_count = 0
        
        # Calculate quality score based on available data
        quality_score = 85
        if extracted_content and len(extracted_content) > 1000:
            quality_score = 95
        elif chunks_count > 5:
            quality_score = 90
        
        extraction_method = "Document Processing Pipeline"
        if file_type.lower() == 'pdf':
            extraction_method = "PyMuPDF + NLP Processing"
        elif file_type.lower() in ['doc', 'docx']:
            extraction_method = "Python-docx + NLP Processing"
        
        return {
            "extractedContent": content_preview,
            "extractionMethod": extraction_method,
            "qualityScore": quality_score,
            "processingTime": metadata.get('processing_time', 0),
            "chunks": chunks_count,
            "warnings": metadata.get('warnings', []),
            "fallbackReason": metadata.get('fallback_reason', ""),
            "document_name": display_name,
            "file_type": file_type
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching document extraction details: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Error fetching document extraction details: {str(e)}"
        )

# Document content endpoint
@app.get("/api/documents/{document_id}/content")
async def get_document_content(document_id: str):
    logger.info(f"Fetching content for document ID: {document_id}")

    try:
        # First try to get the document info from the database
        document_query = """
        SELECT id, display_name, extracted_content, file_type, created_at
        FROM documents 
        WHERE id = %s
        """
        
        document_result = supabase.execute_query(document_query, [document_id])
        
        if not document_result or len(document_result) == 0:
            raise HTTPException(status_code=404, detail="Document not found")
        
        document = document_result[0]
        display_name = document.get('display_name', 'Unknown Document')
        extracted_content = document.get('extracted_content')
        
        # If we have extracted content in the document record, use it
        if extracted_content and extracted_content.strip():
            content = extracted_content
        else:
            # Otherwise, get content from document chunks
            chunks_query = """
            SELECT text, page_number, chunk_index
            FROM document_chunks 
            WHERE document_id = %s 
            ORDER BY page_number, chunk_index
            """
            
            chunks_result = supabase.execute_query(chunks_query, [document_id])
            
            if chunks_result and len(chunks_result) > 0:
                # Combine all chunks into a single content string
                content = f"# {display_name}\n\n"
                current_page = None
                
                for chunk in chunks_result:
                    page_num = chunk.get('page_number')
                    chunk_text = chunk.get('text', '')
                    
                    # Add page separator if page changes
                    if page_num != current_page and page_num is not None:
                        if current_page is not None:
                            content += f"\n\n---\n\n"
                        content += f"## Page {page_num}\n\n"
                        current_page = page_num
                    
                    content += chunk_text + "\n\n"
                
                # Remove excessive newlines
                content = content.strip()
            else:
                content = f"# {display_name}\n\nNo extracted content available for this document."
        
        # Get additional metadata
        chunks_count_query = """
        SELECT COUNT(*) as count
        FROM document_chunks 
        WHERE document_id = %s
        """
        
        chunks_count_result = supabase.execute_query(chunks_count_query, [document_id])
        try:
            if chunks_count_result and len(chunks_count_result) > 0:
                if isinstance(chunks_count_result[0], dict) and 'count' in chunks_count_result[0]:
                    chunks_count = chunks_count_result[0]['count']
                else:
                    chunks_count = len(chunks_count_result)
            else:
                chunks_count = 0
        except Exception as e:
            logger.error(f"Error parsing count result: {str(e)}")
            chunks_count = 0
        
        return {
            "content": content,
            "extraction_method": "Document Processing Pipeline",
            "quality_score": 95,  # You can calculate this based on content quality
            "processing_time": 0,  # This would be stored during processing
            "chunks_count": chunks_count,
            "document_name": display_name
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching document content: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Error fetching document content: {str(e)}"
        )

# Website extraction details endpoint
@app.get("/api/websites/{website_id}/extraction-details")
async def get_website_extraction_details(website_id: str):
    logger.info(f"Fetching extraction details for website ID: {website_id}")

    try:
        # First try to get the website info from the database
        website_query = """
        SELECT id, url, domain, extracted_content, created_at, metadata
        FROM websites 
        WHERE id = %s
        """
        
        website_result = supabase.execute_query(website_query, [website_id])
        
        if not website_result or len(website_result) == 0:
            raise HTTPException(status_code=404, detail="Website not found")
        
        website = website_result[0]
        url = website.get('url', 'Unknown URL')
        domain = website.get('domain', 'Unknown Domain')
        extracted_content = website.get('extracted_content')
        metadata = website.get('metadata', {})
        
        # If we have extracted content in the website record, use it
        if extracted_content and extracted_content.strip():
            content = extracted_content
        else:
            # Otherwise, get content from website chunks
            chunks_query = """
            SELECT text, chunk_index, metadata
            FROM website_chunks 
            WHERE website_id = %s 
            ORDER BY chunk_index
            LIMIT 3
            """
            
            chunks_result = supabase.execute_query(chunks_query, [website_id])
            
            if chunks_result and len(chunks_result) > 0:
                # Combine first few chunks as sample content for extraction details
                content = f"# Website Content: {domain}\n"
                content += f"**URL:** {url}\n\n"
                
                for i, chunk in enumerate(chunks_result[:3]):
                    chunk_text = chunk.get('text', '')
                    content += chunk_text[:500] + ("..." if len(chunk_text) > 500 else "") + "\n\n"
                
                # Remove excessive newlines
                content = content.strip()
            else:
                content = f"# Website Content: {domain}\n**URL:** {url}\n\nNo extracted content available for this website."
        
        # Get chunks count
        chunks_count_query = """
        SELECT COUNT(*) as count
        FROM website_chunks 
        WHERE website_id = %s
        """
        
        chunks_count_result = supabase.execute_query(chunks_count_query, [website_id])
        try:
            if chunks_count_result and len(chunks_count_result) > 0:
                if isinstance(chunks_count_result[0], dict) and 'count' in chunks_count_result[0]:
                    chunks_count = chunks_count_result[0]['count']
                else:
                    chunks_count = len(chunks_count_result)
            else:
                chunks_count = 0
        except Exception as e:
            logger.error(f"Error parsing count result: {str(e)}")
            chunks_count = 0
        
        return {
            "extractedContent": content,
            "extractionMethod": "Web Scraping (Trafilatura)",
            "fallbackHistory": ["Trafilatura"],
            "contentQuality": 90,  # You can calculate this based on content quality
            "warnings": [],
            "processingTime": 0,  # This would be stored during processing
            "chunks": chunks_count,
            "website_url": url,
            "website_domain": domain
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching website extraction details: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Error fetching website extraction details: {str(e)}"
        )

# Website content endpoint
@app.get("/api/websites/{website_id}/content")
async def get_website_content(website_id: str):
    logger.info(f"Fetching content for website ID: {website_id}")

    try:
        # First try to get the website info from the database
        website_query = """
        SELECT id, url, domain, extracted_content, created_at
        FROM websites 
        WHERE id = %s
        """
        
        website_result = supabase.execute_query(website_query, [website_id])
        
        if not website_result or len(website_result) == 0:
            raise HTTPException(status_code=404, detail="Website not found")
        
        website = website_result[0]
        url = website.get('url', 'Unknown URL')
        domain = website.get('domain', 'Unknown Domain')
        extracted_content = website.get('extracted_content')
        
        # If we have extracted content in the website record, use it
        if extracted_content and extracted_content.strip():
            content = extracted_content
        else:
            # Otherwise, get content from website chunks
            chunks_query = """
            SELECT text, chunk_index, metadata
            FROM website_chunks 
            WHERE website_id = %s 
            ORDER BY chunk_index
            """
            
            chunks_result = supabase.execute_query(chunks_query, [website_id])
            
            if chunks_result and len(chunks_result) > 0:
                # Combine all chunks into a single content string
                content = f"# Website Content: {domain}\n"
                content += f"**URL:** {url}\n\n"
                
                for chunk in chunks_result:
                    chunk_text = chunk.get('text', '')
                    content += chunk_text + "\n\n"
                
                # Remove excessive newlines
                content = content.strip()
            else:
                content = f"# Website Content: {domain}\n**URL:** {url}\n\nNo extracted content available for this website."
        
        # Get additional metadata
        chunks_count_query = """
        SELECT COUNT(*) as count
        FROM website_chunks 
        WHERE website_id = %s
        """
        
        chunks_count_result = supabase.execute_query(chunks_count_query, [website_id])
        try:
            if chunks_count_result and len(chunks_count_result) > 0:
                if isinstance(chunks_count_result[0], dict) and 'count' in chunks_count_result[0]:
                    chunks_count = chunks_count_result[0]['count']
                else:
                    chunks_count = len(chunks_count_result)
            else:
                chunks_count = 0
        except Exception as e:
            logger.error(f"Error parsing count result: {str(e)}")
            chunks_count = 0
        
        return {
            "content": content,
            "extraction_method": "Website Scraping Pipeline",
            "quality_score": 95,  # You can calculate this based on content quality
            "processing_time": 0,  # This would be stored during processing
            "pages_processed": 1,
            "total_links": chunks_count,
            "website_url": url,
            "website_domain": domain
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching website content: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Error fetching website content: {str(e)}"
        )

# Feedback endpoints
@app.post("/api/feedback")
async def submit_feedback(feedback: FeedbackData):
    logger.info(f"Received feedback with issue type: {feedback.issue_type}")
    result = send_feedback_email(feedback)
    return result

@app.get("/api/feedback/emails")
async def get_feedback_notification_emails():
    emails = get_feedback_emails()
    logger.info(f"Retrieved feedback emails: {emails}")
    return {"emails": emails}

@app.post("/api/feedback/emails")
async def update_feedback_notification_emails(config: FeedbackEmailConfig):
    success = update_feedback_emails(config.emails)
    if success:
        return {"success": True, "message": "Feedback emails updated successfully"}
    else:
        raise HTTPException(status_code=400, message="Failed to update feedback emails")

# Debug endpoint to check document chunks
@app.get("/api/debug/document-chunks")
async def debug_document_chunks():
    """Debug endpoint to check document chunks in the database."""
    try:
        # Query to get all document chunks
        chunks_query = """
        SELECT
            dc.id,
            dc.document_id,
            dc.chunk_index,
            dc.page_number,
            dc.text,
            d.display_name as filename,
            d.file_path as url,
            'document' as source_type
        FROM
            document_chunks dc
        JOIN
            documents d ON dc.document_id = d.id
        LIMIT 10
        """

        result = supabase.execute_query(chunks_query)

        if isinstance(result, dict) and "error" in result:
            return {"error": result.get("error")}
        else:
            return {"document_chunks": result, "count": len(result)}
    except Exception as e:
        return {"error": str(e)}

# Debug endpoint to check website chunks
@app.get("/api/debug/website-chunks")
async def debug_website_chunks():
    """Debug endpoint to check website chunks in the database."""
    try:
        # Query to get all website chunks
        chunks_query = """
        SELECT
            wc.id,
            wc.website_id,
            wc.chunk_index,
            wc.text,
            wc.metadata,
            w.url,
            w.domain,
            'website' as source_type
        FROM
            website_chunks wc
        JOIN
            websites w ON wc.website_id = w.id
        LIMIT 20
        """

        result = supabase.execute_query(chunks_query)

        if isinstance(result, dict) and "error" in result:
            return {"error": result.get("error")}
        else:
            return {"website_chunks": result, "count": len(result)}
    except Exception as e:
        return {"error": str(e)}

# Debug endpoint to test PDF extraction from URL
@app.get("/api/debug/extract-pdf")
async def debug_extract_pdf(url: str):
    """Debug endpoint to test PDF extraction from a URL."""
    try:
        from website_scraper import extract_pdf_from_url, extract_website_text

        # First try direct PDF extraction
        pdf_text = extract_pdf_from_url(url)

        # Then try the full website extraction
        website_chunks = extract_website_text(url)

        # Remove embeddings for response size
        for chunk in website_chunks:
            if "embedding" in chunk:
                del chunk["embedding"]

        return {
            "url": url,
            "pdf_text_length": len(pdf_text) if pdf_text else 0,
            "pdf_text_sample": pdf_text[:500] + "..." if pdf_text and len(pdf_text) > 500 else pdf_text,
            "website_chunks_count": len(website_chunks),
            "website_chunks_sample": website_chunks[:2] if website_chunks else []
        }
    except Exception as e:
        return {"error": str(e), "url": url}

# Debug endpoint to test search with a specific query
@app.get("/api/debug/search")
async def debug_search(query: str):
    """Debug endpoint to test search with a specific query."""
    try:
        # Generate embedding for the query
        query_embedding = generate_embedding(query)

        # Extract key terms for better search
        import re
        from string import punctuation

        # Clean the query and extract key terms
        clean_query = re.sub(r'[' + punctuation + ']', ' ', query.lower())
        words = clean_query.split()
        stop_words = {'what', 'is', 'the', 'of', 'in', 'on', 'at', 'and', 'or', 'for', 'to', 'a', 'an', 'by', 'with', 'about'}
        key_terms = [word for word in words if word not in stop_words and len(word) > 2]

        # If no key terms found, use the original query
        if not key_terms:
            key_terms = [query]

        # Direct document search results
        title_results = []
        for term in key_terms:
            results = search_documents_by_title(term)
            title_results.extend(results)

        # Remove duplicates
        unique_ids = set()
        unique_title_results = []
        for doc in title_results:
            doc_id = doc.get("id")
            if doc_id and doc_id not in unique_ids:
                unique_ids.add(doc_id)
                unique_title_results.append(doc)

        title_results = unique_title_results

        # Get chunks for each document found by title
        direct_document_chunks = []
        for doc in title_results:
            doc_id = doc.get("id")
            if doc_id:
                chunks = get_document_chunks(doc_id)
                direct_document_chunks.extend(chunks)

        # Search for documents by content
        content_results = search_documents_by_content(query)

        # Vector search results
        vector_document_chunks = search_supabase_document_chunks(
            query_embedding=query_embedding,
            query_text=query,
            use_hybrid_search=True,
            top_k=5,
            min_threshold=0.0001
        )

        # Direct website search results
        direct_website_chunks = []
        for term in key_terms:
            # Sanitize the term
            sanitized_term = term.replace("'", "''")

            # Query to search website chunks by content
            website_query = f"""
            SELECT
                wc.id,
                wc.website_id,
                wc.chunk_index,
                wc.text,
                wc.url,
                w.name as website_name,
                'website' as source_type,
                ts_rank(to_tsvector('english', wc.text), plainto_tsquery('english', '{sanitized_term}')) AS similarity
            FROM
                website_chunks wc
            JOIN
                websites w ON wc.website_id = w.id
            WHERE
                to_tsvector('english', wc.text) @@ plainto_tsquery('english', '{sanitized_term}')
                OR wc.text ILIKE '%{sanitized_term}%'
            ORDER BY
                similarity DESC
            LIMIT 5
            """

            result = supabase.execute_query(website_query)

            if not isinstance(result, dict) or "error" not in result:
                direct_website_chunks.extend(result)

        # Vector search for website chunks
        vector_website_chunks = search_supabase_website_chunks(
            query_embedding=query_embedding,
            query_text=query,
            use_hybrid_search=True,
            top_k=5,
            min_threshold=0.0001
        )

        # Remove embeddings for response size
        for chunk in vector_document_chunks:
            if "embedding" in chunk:
                del chunk["embedding"]

        for chunk in vector_website_chunks:
            if "embedding" in chunk:
                del chunk["embedding"]

        for chunk in direct_document_chunks:
            if "embedding" in chunk:
                del chunk["embedding"]

        for chunk in content_results:
            if "embedding" in chunk:
                del chunk["embedding"]

        for chunk in direct_website_chunks:
            if "embedding" in chunk:
                del chunk["embedding"]

        return {
            "query": query,
            "key_terms": key_terms,
            "direct_document_search": {
                "title_results": title_results,
                "title_count": len(title_results),
                "content_results": content_results,
                "content_count": len(content_results),
                "direct_document_chunks": direct_document_chunks,
                "direct_document_count": len(direct_document_chunks)
            },
            "vector_document_search": {
                "document_chunks": vector_document_chunks,
                "document_count": len(vector_document_chunks)
            },
            "direct_website_search": {
                "website_chunks": direct_website_chunks,
                "website_count": len(direct_website_chunks)
            },
            "vector_website_search": {
                "website_chunks": vector_website_chunks,
                "website_count": len(vector_website_chunks)
            }
        }
    except Exception as e:
        return {"error": str(e)}

def local_document_search(query_embedding, query_text=None, top_k=20, min_threshold=0.05):
    """
    Local document search using in-memory DOCUMENT_CHUNKS when Supabase is unavailable.
    Uses cosine similarity for vector search.
    """
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Using local document search with {len(DOCUMENT_CHUNKS)} chunks")

        if not DOCUMENT_CHUNKS:
            logger.warning("No document chunks available for local search")
            return []

        scored_chunks = []

        for chunk in DOCUMENT_CHUNKS:
            # Only process document chunks
            if chunk.get('source_type') != 'document':
                continue

            chunk_embedding = chunk.get('embedding')
            if not chunk_embedding:
                continue

            # Calculate similarity
            similarity = cosine_similarity(query_embedding, chunk_embedding)

            if similarity >= min_threshold:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = similarity
                chunk_copy['source_type'] = 'document'
                scored_chunks.append(chunk_copy)

        # Sort by similarity (highest first)
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)

        # Return top_k results
        result = scored_chunks[:top_k]
        logger.info(f"Local document search found {len(result)} results (threshold: {min_threshold})")

        return result

    except Exception as e:
        logger.error(f"Error in local document search: {str(e)}")
        return []

def local_website_search(query_embedding, query_text=None, top_k=20, min_threshold=0.01):
    """
    Local website search using in-memory DOCUMENT_CHUNKS when Supabase is unavailable.
    Uses cosine similarity for vector search.
    """
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Using local website search with {len(DOCUMENT_CHUNKS)} chunks")

        if not DOCUMENT_CHUNKS:
            logger.warning("No document chunks available for local search")
            return []

        scored_chunks = []

        for chunk in DOCUMENT_CHUNKS:
            # Only process website chunks
            if chunk.get('source_type') != 'website':
                continue

            chunk_embedding = chunk.get('embedding')
            if not chunk_embedding:
                continue

            # Calculate similarity
            similarity = cosine_similarity(query_embedding, chunk_embedding)

            if similarity >= min_threshold:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = similarity
                chunk_copy['source_type'] = 'website'
                scored_chunks.append(chunk_copy)

        # Sort by similarity (highest first)
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)

        # Return top_k results
        result = scored_chunks[:top_k]
        logger.info(f"Local website search found {len(result)} results (threshold: {min_threshold})")

        return result

    except Exception as e:
        logger.error(f"Error in local website search: {str(e)}")
        return []

def search_with_fallback(search_func, fallback_func, *args, **kwargs):
    """
    Attempt online search first, fallback to local search if it fails.
    """
    try:
        # Try online search first
        results = search_func(*args, **kwargs)
        if results and len(results) > 0:
            logger.info(f"Online search successful: {len(results)} results")
            return results
        else:
            logger.info("Online search returned no results, trying local fallback")
            return fallback_func(*args, **kwargs)
    except Exception as e:
        logger.warning(f"Online search failed: {str(e)}, trying local fallback")
        return fallback_func(*args, **kwargs)

# Start the server if this is the main module
if __name__ == "__main__":
    import uvicorn
    port = int(os.getenv("PORT", 8000))
    uvicorn.run(app, host="0.0.0.0", port=port)

# Debug endpoint to generate embeddings for all chunks
@app.post("/api/debug/generate-embeddings")
async def debug_generate_embeddings():
    """Debug endpoint to generate embeddings for all chunks in memory."""
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Starting embedding generation for {len(DOCUMENT_CHUNKS)} chunks")

        generated_count = 0
        failed_count = 0

        for i, chunk in enumerate(DOCUMENT_CHUNKS):
            chunk_id = chunk.get('id', f'chunk_{i}')

            # Check if embedding already exists
            if "embedding" in chunk and chunk["embedding"] and len(chunk["embedding"]) > 10:
                continue  # Skip if already has embedding

            # Generate embedding for chunk text
            chunk_text = chunk.get("text", "")
            if chunk_text:
                try:
                    chunk["embedding"] = generate_embedding(chunk_text)
                    generated_count += 1
                    logger.info(f"Generated embedding for chunk {chunk_id} ({generated_count}/{len(DOCUMENT_CHUNKS)})")
                except Exception as e:
                    logger.warning(f"Failed to generate embedding for chunk {chunk_id}: {str(e)}")
                    # Fallback to mock embedding
                    chunk["embedding"] = [0.01] * 768
                    failed_count += 1
            else:
                # No text, use mock embedding
                chunk["embedding"] = [0.01] * 768
                failed_count += 1

        logger.info(f"Embedding generation complete: {generated_count} generated, {failed_count} failed")

        return {
            "message": "Embedding generation completed",
            "total_chunks": len(DOCUMENT_CHUNKS),
            "generated": generated_count,
            "failed": failed_count
        }

    except Exception as e:
        logger.error(f"Error generating embeddings: {str(e)}")
        return {"error": str(e)}

def improved_text_based_document_search(query: str, top_k: int = 10, min_threshold: float = 0.7):
    """
    Improved text-based document search with VERY STRICT relevance requirements.
    Only matches queries that are actually related to railway content.
    """
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Using STRICT text-based document search for: '{query}' (threshold: {min_threshold})")

        # Extract key terms from query
        query_lower = query.lower()
        query_words = set(query_lower.split())

        # Railway domain keywords - MUST have at least one for relevance
        railway_keywords = {
            'railway', 'train', 'rail', 'station', 'track', 'locomotive', 'coach',
            'signal', 'platform', 'passenger', 'freight', 'engine', 'diesel',
            'fsds', 'monitoring', 'system', 'safety', 'maintenance', 'inspection',
            'indian', 'railways', 'irctc', 'booking', 'ticket', 'metro', 'suburban'
        }

        # Non-railway indicators - immediate disqualification
        non_railway_keywords = {
            'weather', 'temperature', 'climate', 'forecast', 'rain', 'sunny', 'cloudy',
            'movie', 'film', 'actor', 'music', 'song', 'sports', 'football', 'cricket',
            'restaurant', 'food', 'recipe', 'cooking', 'shopping', 'price', 'buy',
            'health', 'medicine', 'doctor', 'hospital', 'disease', 'symptoms',
            'politics', 'government', 'election', 'vote', 'party', 'minister'
        }

        # Check if query contains non-railway keywords - immediate rejection
        non_railway_in_query = non_railway_keywords & query_words
        if non_railway_in_query:
            logger.info(f"Query rejected: contains non-railway keywords {non_railway_in_query}")
            return []

        # Check if query has any railway context
        railway_in_query = railway_keywords & query_words
        if not railway_in_query:
            logger.info(f"Query rejected: no railway keywords found in query")
            return []

        scored_chunks = []

        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'document':
                continue

            chunk_text = chunk.get('text', '').lower()
            if not chunk_text:
                continue

            # Calculate relevance score with VERY STRICT requirements
            score = 0

            # 1. Exact phrase match (highest priority)
            if query_lower in chunk_text:
                score += 2.0

            # 2. Keyword matching - require MULTIPLE specific matches
            chunk_words = set(chunk_text.split())
            common_words = query_words & chunk_words
            if len(common_words) >= 2:  # Must have at least 2 words in common
                score += len(common_words) * 0.5

            # 3. Railway domain bonus - but document must also contain railway terms
            chunk_railway_matches = railway_keywords & chunk_words
            if chunk_railway_matches:
                score += len(chunk_railway_matches) * 0.3
            else:
                continue  # Skip if chunk has no railway content

            # 4. Special technical terms
            if 'fsds' in chunk_text:
                score += 1.0
            if 'monitoring' in chunk_text and 'system' in chunk_text:
                score += 0.5

            # 5. Only include if score meets threshold
            if score >= min_threshold:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = score
                scored_chunks.append(chunk_copy)

        # Sort by relevance score
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)

        result = scored_chunks[:top_k]
        logger.info(f"STRICT text-based document search found {len(result)} relevant chunks (threshold: {min_threshold})")

        # If no results found, log it clearly
        if not result:
            logger.info("No relevant document chunks found - query is not railway-related")

        return result

    except Exception as e:
        logger.error(f"Error in strict text-based document search: {str(e)}")
        return []

def improved_text_based_website_search(query: str, top_k: int = 10, min_threshold: float = 0.7):
    """
    Improved text-based website search with STRICT relevance requirements.
    Only matches queries related to transport/railway content.
    """
    global DOCUMENT_CHUNKS

    try:
        logger.info(f"Using STRICT text-based website search for: '{query}' (threshold: {min_threshold})")

        # Extract key terms from query
        query_lower = query.lower()
        query_words = set(query_lower.split())

        # Transport/railway domain keywords - MUST have at least one
        transport_keywords = {
            'railway', 'train', 'rail', 'station', 'transport', 'transportation',
            'public', 'safety', 'travel', 'passenger', 'metro', 'services',
            'ticket', 'booking', 'irctc', 'indian', 'railways'
        }

        # Non-transport indicators - immediate disqualification
        non_transport_keywords = {
            'weather', 'temperature', 'climate', 'forecast', 'rain', 'sunny', 'cloudy',
            'movie', 'film', 'actor', 'music', 'song', 'sports', 'football', 'cricket',
            'restaurant', 'food', 'recipe', 'cooking', 'shopping', 'price', 'buy',
            'health', 'medicine', 'doctor', 'hospital', 'disease', 'symptoms'
        }

        # Check if query contains non-transport keywords - immediate rejection
        non_transport_in_query = non_transport_keywords & query_words
        if non_transport_in_query:
            logger.info(f"Website query rejected: contains non-transport keywords {non_transport_in_query}")
            return []

        # Check if query has any transport context
        transport_in_query = transport_keywords & query_words
        if not transport_in_query:
            logger.info(f"Website query rejected: no transport keywords found in query")
            return []

        scored_chunks = []

        for chunk in DOCUMENT_CHUNKS:
            if chunk.get('source_type') != 'website':
                continue

            chunk_text = chunk.get('text', '').lower()
            if not chunk_text:
                continue

            # Calculate relevance score
            score = 0

            # 1. Exact phrase match (highest priority)
            if query_lower in chunk_text:
                score += 2.0

            # 2. Keyword matching - require multiple matches
            chunk_words = set(chunk_text.split())
            common_words = query_words & chunk_words
            if len(common_words) >= 2:  # Must have at least 2 words in common
                score += len(common_words) * 0.5

            # 3. Transport domain relevance - chunk must also contain transport terms
            chunk_transport_matches = transport_keywords & chunk_words
            if chunk_transport_matches:
                score += len(chunk_transport_matches) * 0.3
            else:
                continue  # Skip if chunk has no transport content

            # 4. Special boost for transport safety
            if 'transportation' in chunk_text and 'safety' in chunk_text:
                score += 0.5
            if 'public' in chunk_text and ('transport' in chunk_text or 'railway' in chunk_text):
                score += 0.5

            # 5. Only include if score meets threshold
            if score >= min_threshold:
                chunk_copy = dict(chunk)
                chunk_copy['similarity'] = score
                scored_chunks.append(chunk_copy)

        # Sort by relevance score
        scored_chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)

        result = scored_chunks[:top_k]
        logger.info(f"STRICT text-based website search found {len(result)} relevant chunks (threshold: {min_threshold})")

        # If no results found, log it clearly
        if not result:
            logger.info("No relevant website chunks found - query is not transport-related")

        return result

    except Exception as e:
        logger.error(f"Error in strict text-based website search: {str(e)}")
        return []


def generate_clean_answer_with_sources(query: str, chunks: List[Dict[str, Any]], source_type: str, model_id: str = "gemini-2.0-flash", extract_format: str = "paragraph"):
    """
    Generate a clean answer with properly formatted sources for a specific source type.

    Args:
        query: The user's question
        chunks: List of chunks from a single source type (document OR website)
        source_type: Either "document" or "website"
        model_id: LLM model to use
        extract_format: Preferred format for the extraction

    Returns:
        Tuple of (answer_text, clean_sources_list)
    """
    try:
        logger.info(f"Generating clean {source_type} answer from {len(chunks)} chunks")

        # Set minimum relevance thresholds
        min_doc_threshold = 0.25  # Increased threshold to find more relevant matches
        min_web_threshold = 0.15  # Increased threshold for websites
        min_threshold = min_doc_threshold if source_type == "document" else min_web_threshold

        # Filter chunks by relevance first
        relevant_chunks = []
        for chunk in chunks:
            similarity = chunk.get('similarity', 0.0)
            chunk_text = chunk.get('text', '')

            # Skip chunks with very low similarity, empty text, or insufficient content
            if similarity < min_threshold or not chunk_text or len(chunk_text) < 20:
                logger.info(f"Skipping {source_type} chunk with similarity {similarity:.3f} (below threshold {min_threshold}) or insufficient text")
                continue

            relevant_chunks.append(chunk)

        # If no relevant chunks found, return empty result
        if not relevant_chunks:
            logger.info(f"No relevant {source_type} chunks found above threshold {min_threshold} and minimum text length 20 characters")
            return f"I couldn't find relevant information in the {source_type} content to answer your question.", [], False, []

        logger.info(f"Processing {len(relevant_chunks)} relevant {source_type} chunks (out of {len(chunks)} total)")

        # Prepare context from relevant chunks only
        context_texts = []
        sources_dict = {}  # Use dict to avoid duplicates

        for chunk in relevant_chunks:
            chunk_text = chunk.get("text", "")
            similarity = chunk.get("similarity", 0)

            if source_type == "document":
                # Document source processing
                document_id = chunk.get("document_id")
                # First try to get filename from document_id (most reliable)
                filename = None
                if document_id:
                    try:
                        from supabase_client import supabase
                        doc_query = f"SELECT COALESCE(display_name, file_name, name) as filename FROM documents WHERE id = '{document_id}'"
                        doc_result = supabase.execute_query(doc_query)
                        if doc_result and len(doc_result) > 0:
                            filename = doc_result[0]['filename']
                            logger.info(f"Retrieved filename '{filename}' for document_id '{document_id}'")
                    except Exception as e:
                        logger.error(f"Error retrieving document name: {str(e)}")
                
                # Fall back to chunk metadata if needed
                if not filename:
                    filename = chunk.get("filename") or chunk.get("title") or "Unknown document"
                
                page = chunk.get("page") or chunk.get("page_number") or 1

                # Check for visual content
                metadata = chunk.get("metadata", {})
                content_type = metadata.get("content_type", "text")
                visual_content_type = metadata.get("visual_content_type")
                storage_url = metadata.get("storage_url")
                display_type = metadata.get("display_type", "text")

                # Enhanced context for visual content
                if content_type in ["table", "image", "chart_diagram"]:
                    context_texts.append(f"From '{filename}' (page {page}, {content_type}, relevance: {similarity:.2f}):\n{chunk_text}\n")
                else:
                    context_texts.append(f"From '{filename}' (page {page}, relevance: {similarity:.2f}):\n{chunk_text}\n")

                # Create unique source key for deduplication (include content type for visual content)
                if content_type != "text":
                    source_key = f"{filename}_{content_type}_{page}"
                else:
                    # Group by filename for text content to ensure all pages are tracked together
                    source_key = f"{filename}"
                    
                logger.info(f"Processing chunk from document '{filename}', page {page}, content_type={content_type}")
                    
                if source_key not in sources_dict:
                    sources_dict[source_key] = {
                        "source_type": "document",
                        "filename": filename,
                        "name": os.path.basename(filename),
                        "pages": [],
                        "content_type": content_type,
                        "visual_content_type": visual_content_type,
                        "storage_url": storage_url,
                        "display_type": display_type,
                        "visual_content": format_visual_content_for_frontend(metadata, content_type) if content_type != "text" else None
                    }

                # Add page if not already present
                if page not in sources_dict[source_key]["pages"]:
                    sources_dict[source_key]["pages"].append(page)

            elif source_type == "website":
                # Website source processing - extract URL from multiple possible fields
                url = chunk.get("url", "")

                # If no URL field, try to extract from text
                if not url or url == "Unknown":
                    chunk_text_for_url = chunk.get("text", "")
                    # Look for URL patterns in the text
                    import re
                    url_match = re.search(r'URL:\s*(https?://[^\s\n]+)', chunk_text_for_url)
                    if url_match:
                        url = url_match.group(1)
                    else:
                        # Look for website domain patterns
                        domain_match = re.search(r'Website:\s*([^\s,\n]+)', chunk_text_for_url)
                        if domain_match:
                            domain = domain_match.group(1)
                            url = f"https://{domain}" if not domain.startswith('http') else domain
                        # Special case handling for known websites
                        elif "rapidresponseapp" in chunk_text_for_url.lower():
                            url = "https://www.rapidresponseapp.com"
                        elif "indianrailways.gov.in" in chunk_text_for_url.lower():
                            url = "https://indianrailways.gov.in"
                        elif "irctc" in chunk_text_for_url.lower():
                            url = "https://www.irctc.co.in"
                        else:
                            url = "Unknown website"

                # Add to context
                context_texts.append(f"From '{url}' (relevance: {similarity:.2f}):\n{chunk_text}\n")

                # Create unique source key for deduplication
                source_key = url
                if source_key not in sources_dict:
                    sources_dict[source_key] = {
                        "source_type": "website",
                        "url": url
                    }

        # Convert sources dict to clean list with relevance scoring
        source_relevance = []
        for source_data in sources_dict.values():
            if source_type == "document":
                # Calculate relevance score for this document source
                # Find the highest similarity chunk for this document
                max_similarity = 0
                for chunk in relevant_chunks:
                    if chunk.get("filename") == source_data["filename"]:
                        chunk_similarity = chunk.get("similarity", 0)
                        if chunk_similarity > max_similarity:
                            max_similarity = chunk_similarity

                # Sort pages and create page reference
                pages = sorted(source_data["pages"])
                logger.info(f"Document '{source_data['name']}' has pages: {pages}")
                if len(pages) == 1:
                    page_ref = f"Page {pages[0]}"
                else:
                    page_ref = f"Pages {', '.join(map(str, pages))}"
                logger.info(f"Page reference for '{source_data['name']}': {page_ref}")

                # Enhanced source with visual content information
                clean_source = {
                    "source_type": "document",
                    "filename": source_data["filename"],
                    "name": source_data["name"],
                    "page": pages[0],  # First page for link generation
                    "link": f"/viewer?file={source_data['filename']}&page={pages[0]}",
                    "display_text": f"{source_data['name']} – {page_ref}",
                    "relevance_score": max_similarity,
                    "content_type": source_data.get("content_type", "text"),
                    "visual_content": source_data.get("visual_content"),
                    "storage_url": source_data.get("storage_url"),
                    "display_type": source_data.get("display_type", "text")
                }
                source_relevance.append(clean_source)

            elif source_type == "website":
                # Calculate relevance score for this website source
                max_similarity = 0
                for chunk in relevant_chunks:
                    if chunk.get("url") == source_data["url"]:
                        chunk_similarity = chunk.get("similarity", 0)
                        if chunk_similarity > max_similarity:
                            max_similarity = chunk_similarity

                clean_source = {
                    "source_type": "website",
                    "url": source_data["url"],
                    "display_text": source_data["url"],
                    "relevance_score": max_similarity
                }
                source_relevance.append(clean_source)
                logger.info(f"Added website source: {source_data['url']} (relevance: {max_similarity:.3f})")

        # Sort sources by relevance score (highest first), prioritizing visual content
        def source_sort_key(source):
            relevance = source.get("relevance_score", 0)
            content_type = source.get("content_type", "text")
            # Boost visual content slightly in ranking
            visual_boost = 0.1 if content_type != "text" else 0
            return relevance + visual_boost
            
        source_relevance.sort(key=source_sort_key, reverse=True)

        # Include all document sources to ensure all page references are shown properly
        # For websites, still limit to top 2 most relevant sources for better UX
        max_sources = len(source_relevance) if source_type == "document" else 2
        logger.info(f"Including up to {max_sources} {source_type} sources to show all page references")
        clean_sources = []
        visual_content_found = False
        visual_content_types = []
        
        for source in source_relevance[:max_sources]:
            # Track visual content
            content_type = source.get("content_type", "text")
            if content_type != "text":
                visual_content_found = True
                if content_type not in visual_content_types:
                    visual_content_types.append(content_type)
            
            # Remove the relevance_score from the final source object
            final_source = {k: v for k, v in source.items() if k != "relevance_score"}
            clean_sources.append(final_source)

        logger.info(f"Filtered to top {len(clean_sources)} most relevant {source_type} sources (from {len(source_relevance)} total)")

        # Don't create fallback sources - if no clean sources can be created from chunks,
        # it means the chunks are not relevant and we should let the system fall back to LLM
        if not clean_sources and chunks:
            logger.info(f"No clean sources created from {len(chunks)} chunks - chunks appear to be irrelevant")

        # Combine context
        context = "\n\n".join(context_texts)

        # Create specialized system prompt for this source type
        visual_instruction = ""
        if visual_content_found:
            visual_instruction = f"""
VISUAL CONTENT AVAILABLE:
The following document context includes visual content (tables, images, diagrams). 
When referencing visual content:
- For tables: Describe the data structure and key information
- For images: Reference their content and purpose 
- For diagrams: Explain what they illustrate
- Note that visual content is available in the source documents for detailed viewing
"""

        if source_type == "document":
            system_prompt = f"""You are RailGPT, an AI assistant that helps users with questions about Indian Railways and general topics.

SPECIAL INSTRUCTIONS FOR VISUAL CONTENT QUERIES:
- If user asks for "image of project X" or "table of quotation X" or similar visual content requests
- Look for ANY mention of projects, quotations, tables, or technical data in the documents
- Even if you can't see the actual image, if documents mention projects or contain relevant data, that IS what the user needs
- For project-related queries, ANY document mentioning projects, quotations, or technical specifications is highly relevant

ANSWERING STRATEGY:
1. For visual content requests (images, tables, charts): If documents mention the requested content type or related data, describe what's available
2. For project queries: If documents contain project information, quotations, or specifications, use that data
3. Use specific details from documents (project names, numbers, quotations, specifications)
4. If documents contain relevant information, focus entirely on that content
5. Only use general knowledge if documents have absolutely no connection to the query

{visual_instruction}
DOCUMENT CONTEXT:
{context}

USER QUESTION: {query}

Provide a helpful answer using the document information if it's relevant to the question."""

        else:  # website
            system_prompt = f"""You are RailGPT, an AI assistant that helps users with questions about Indian Railways and general topics.

SMART ANSWERING STRATEGY:
1. First, carefully examine the website information below to see if it contains relevant content for the user's question.
2. If the websites contain relevant information about the question, use that information as your primary source and provide a comprehensive answer.
3. If the websites do NOT contain relevant information for this specific question, use your general knowledge to provide a helpful and accurate answer.
4. Be intelligent about relevance - if the user asks about "LLM meaning" and the websites only talk about trains, the websites are not relevant.
5. Always be helpful and provide the best possible answer, whether from websites or your knowledge.

WEBSITE CONTEXT:
{context}

USER QUESTION: {query}

Provide a helpful and comprehensive answer. Use the website information if it's relevant to the question, otherwise use your general knowledge to give the best possible answer."""

        # Add format preference
        if extract_format == "bullet":
            system_prompt += "\nStructure your answer using bullet points where appropriate for better readability."
        elif extract_format == "table":
            system_prompt += "\nIf the answer contains tabular data, format it as a markdown table for better readability."

        # Generate the answer
        answer = llm_router.generate_answer(
            query=query,
            context=context,
            system_prompt=system_prompt,
            model_id=model_id
        )

        # Check if the AI says the content is not relevant
        # But be less aggressive for visual content queries
        visual_query_keywords = ["image", "picture", "table", "chart", "diagram", "project", "quotation"]
        is_visual_query = any(keyword in query.lower() for keyword in visual_query_keywords)
        
        irrelevant_phrases = [
            "cannot answer",
            "cannot find",
            "does not contain",
            "do not contain",
            "not mentioned",
            "not available",
            "no information",
            "doesn't contain",
            "don't contain",
            "cannot provide",
            "not found in",
            "not present in"
        ]

        answer_lower = answer.lower()
        
        # For visual queries, be much more lenient - only reject if explicitly states irrelevance
        if is_visual_query:
            explicit_irrelevance_phrases = [
                "documents have no information about",
                "documents do not contain any information about",
                "no relevant information in the documents",
                "documents are not relevant to"
            ]
            is_irrelevant = any(phrase in answer_lower for phrase in explicit_irrelevance_phrases)
        else:
            is_irrelevant = any(phrase in answer_lower for phrase in irrelevant_phrases)

        if is_irrelevant:
            logger.info(f"AI indicated {source_type} content is irrelevant - returning empty sources")
            return answer, [], False, []

        logger.info(f"Generated clean {source_type} answer with {len(clean_sources)} sources")
        logger.info(f"Visual content found: {visual_content_found}, types: {visual_content_types}")
        for source in clean_sources:
            if source_type == "website":
                logger.info(f"Website source in final result: {source.get('url', 'Unknown')}")

        return answer, clean_sources, visual_content_found, visual_content_types

    except Exception as e:
        logger.error(f"Error generating clean {source_type} answer: {str(e)}")
        return f"I couldn't generate an answer from the {source_type} information.", [], False, []

# Debug endpoint to test website search specifically
@app.get("/api/debug/website-search")
async def debug_website_search(query: str):
    """Debug endpoint to test website search with a specific query."""
    try:
        logger.info(f"=== DEBUG WEBSITE SEARCH FOR: {query} ===")

        # Get website chunks from memory (no embedding lookup)
        global DOCUMENT_CHUNKS
        all_website_chunks = [c for c in DOCUMENT_CHUNKS if c.get('source_type') == 'website']

        # Create a simple text search to find matching chunks
        matching_chunks = []
        for chunk in all_website_chunks:
            chunk_text = chunk.get("text", "").lower()
            if query.lower() in chunk_text:
                # Extract URL from text if not present in chunk directly
                url = chunk.get("url", "Unknown")
                if url == "Unknown":
                    # Try to extract URL from text
                    import re
                    url_match = re.search(r'URL:\s*(https?://[^\s\n]+)', chunk_text)
                    if url_match:
                        url = url_match.group(1)
                    elif "rapidresponseapp" in chunk_text:
                        url = "https://www.rapidresponseapp.com"
                    elif "indianrailways.gov.in" in chunk_text:
                        url = "https://indianrailways.gov.in"

                # Create a clean copy with only essential fields
                clean_chunk = {
                    "id": str(chunk.get("id", "unknown")),
                    "text": chunk.get("text", "")[:200] + "..." if len(chunk.get("text", "")) > 200 else chunk.get("text", ""),
                    "url": url,
                    "source_type": "website"
                }
                matching_chunks.append(clean_chunk)

        # Return a simple response
        return {
            "query": query,
            "total_website_chunks": len(all_website_chunks),
            "matching_chunks_found": len(matching_chunks),
            "matching_chunks": matching_chunks[:5]  # First 5 for debugging
        }
    except Exception as e:
        import traceback
        logger.error(f"Error in debug website search: {str(e)}")
        logger.error(f"Debug website search error traceback: {traceback.format_exc()}")
        return {"error": str(e), "query": query}

# Simple debug endpoint for website search
@app.get("/api/debug/website-search-simple")
async def debug_website_search_simple(query: str):
    """Simple debug endpoint for website search."""
    try:
        logger.info(f"Simple website search debug for: {query}")

        # Get total website chunks count
        global DOCUMENT_CHUNKS
        website_chunks = [c for c in DOCUMENT_CHUNKS if c.get('source_type') == 'website']

        # Return minimal information
        website_info = []
        for chunk in website_chunks[:5]:  # First 5 for debugging
            # Create a minimal representation
            chunk_info = {
                "id": str(chunk.get("id", "unknown")),
                "text_preview": chunk.get("text", "")[:100] if chunk.get("text") else "No text",
                "url": str(chunk.get("url", "Unknown")),
                "source_type": str(chunk.get("source_type", "website"))
            }
            website_info.append(chunk_info)

        return {
            "query": query,
            "total_website_chunks": len(website_chunks),
            "sample_chunks": website_info
        }
    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        logger.error(f"Error in simple website search debug: {str(e)}\n{error_trace}")
        return {"error": str(e)}

# Document viewer endpoint to serve files
@app.get("/api/documents/view/{filename}")
async def view_document(filename: str):
    """Serve a document file for viewing in the browser."""
    try:
        import os
        from fastapi.responses import FileResponse

        # Decode the filename
        decoded_filename = filename
        logger.info(f"Attempting to serve document: {decoded_filename}")

        # First, try to find the document in Supabase database
        try:
            # Use proper SQL escaping
            escaped_filename = decoded_filename.replace("'", "''")
            documents_query = f"""
            SELECT file_path, display_name, file_type, supabase_file_path, supabase_file_url
            FROM documents
            WHERE display_name = '{escaped_filename}' OR file_path LIKE '%{escaped_filename}%'
            """
            result = supabase.execute_query(documents_query)

            if result and len(result) > 0:
                doc_info = result[0]
                supabase_file_path = doc_info.get('supabase_file_path')
                supabase_file_url = doc_info.get('supabase_file_url')
                file_type = doc_info.get('file_type', '').lower()

                logger.info(f"Found document in database: {doc_info}")

                # If we have a Supabase file URL, redirect to it
                if supabase_file_url:
                    logger.info(f"Redirecting to Supabase file URL: {supabase_file_url}")
                    from fastapi.responses import RedirectResponse
                    return RedirectResponse(url=supabase_file_url)

                # If we have a Supabase file path, try to get the public URL
                if supabase_file_path:
                    try:
                        # Get public URL from Supabase storage
                        public_url_response = supabase.client.storage.from_('documents').get_public_url(supabase_file_path)
                        if public_url_response:
                            logger.info(f"Generated Supabase public URL: {public_url_response}")
                            from fastapi.responses import RedirectResponse
                            return RedirectResponse(url=public_url_response)
                    except Exception as e:
                        logger.error(f"Error generating Supabase public URL: {str(e)}")

        except Exception as e:
            logger.error(f"Error querying Supabase for document: {str(e)}")

        # Fallback: Look for the file in local directories
        # Get the directory where the server script is located
        import os
        script_dir = os.path.dirname(os.path.abspath(__file__))

        possible_paths = [
            os.path.join(script_dir, "data", "uploads", decoded_filename),
            os.path.join(script_dir, "data", decoded_filename),
            f"./backend/data/uploads/{decoded_filename}",
            f"./backend/data/{decoded_filename}",
            f"./data/uploads/{decoded_filename}",
            f"./data/{decoded_filename}",
            f"./uploads/{decoded_filename}",
            f"./documents/{decoded_filename}",
            f"./{decoded_filename}"
        ]

        file_path = None
        logger.info(f"Checking {len(possible_paths)} possible paths for {decoded_filename}")
        for i, path in enumerate(possible_paths):
            logger.info(f"  Path {i+1}: {path} - {'EXISTS' if os.path.exists(path) else 'NOT FOUND'}")
            if os.path.exists(path):
                file_path = path
                break

        if file_path:
            logger.info(f"Serving document from local path: {file_path}")

            # Determine media type based on file extension
            extension = decoded_filename.split('.')[-1].lower()
            media_type_map = {
                'pdf': 'application/pdf',
                'doc': 'application/msword',
                'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'xls': 'application/vnd.ms-excel',
                'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'ppt': 'application/vnd.ms-powerpoint',
                'txt': 'text/plain'
            }

            media_type = media_type_map.get(extension, 'application/octet-stream')

            return FileResponse(
                path=file_path,
                media_type=media_type,
                filename=decoded_filename
            )
        else:
            logger.error(f"Document not found anywhere: {decoded_filename}")
            raise HTTPException(
                status_code=404,
                detail=f"Document '{decoded_filename}' not found in storage or local files"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error serving document: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error serving document: {str(e)}"
        )

# Import category management
from category_management import router as category_router

# ... existing code ...

# Add category management routes
app.include_router(category_router)

# New category-related endpoint additions
@app.post("/api/create-category")
async def create_category(
    name: str = Form(...),
    parent_id: Optional[str] = Form(None),
    level: int = Form(...),
    category_type: str = Form("document")  # "document" or "website"
):
    """
    Create a new category at any level in the hierarchy.
    Automatically reuses existing categories if they already exist.
    """
    try:
        # Validate level
        if level not in [1, 2, 3, 4]:
            raise HTTPException(status_code=400, detail="Level must be between 1 and 4")
        
        # Map level to type
        level_to_type = {
            1: "main_category",
            2: "category", 
            3: "sub_category",
            4: "minor_category"
        }
        
        category_type_db = level_to_type[level]
        table_name = "document_categories" if category_type == "document" else "website_categories"
        
        # Check if category already exists with same name and parent
        check_query = f"""
        SELECT id FROM {table_name} 
        WHERE name = %s AND 
        {('parent_id = %s' if parent_id else 'parent_id IS NULL')}
        """
        
        params = [name]
        if parent_id:
            params.append(parent_id)
            
        existing = supabase.execute_query(check_query, params)
        
        if isinstance(existing, list) and len(existing) > 0:
            # Category already exists, return its ID
            return {"id": existing[0]["id"], "reused": True, "message": "Category already exists"}
        
        # Create new category
        new_id = str(uuid.uuid4())
        insert_query = f"""
        INSERT INTO {table_name} (id, name, type, parent_id, is_active, sort_order)
        VALUES (%s, %s, %s, %s, true, 0)
        RETURNING id
        """
        
        insert_params = [new_id, name, category_type_db, parent_id]
        result = supabase.execute_query(insert_query, insert_params)
        
        if isinstance(result, list) and len(result) > 0:
            return {"id": result[0]["id"], "reused": False, "message": "Category created successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to create category")
            
    except Exception as e:
        logger.error(f"Error creating category: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create category: {str(e)}")

@app.get("/api/categories/hierarchy/{category_type}")
async def get_category_hierarchy(category_type: str):
    """
    Get the complete category hierarchy for documents or websites.
    Returns a nested structure suitable for frontend dropdowns.
    """
    try:
        if category_type not in ["document", "website"]:
            raise HTTPException(status_code=400, detail="Category type must be 'document' or 'website'")
        
        table_name = "document_categories" if category_type == "document" else "website_categories"
        
        # Get all categories for the specified type
        query = f"""
        SELECT id, name, type, parent_id, sort_order, is_active
        FROM {table_name}
        WHERE is_active = true
        ORDER BY sort_order, name
        """
        
        categories = supabase.execute_query(query)
        
        if not isinstance(categories, list):
            return {"hierarchy": [], "flat": []}
        
        # Build hierarchy structure
        def build_hierarchy(parent_id=None):
            result = []
            for cat in categories:
                if cat["parent_id"] == parent_id:
                    cat_data = {
                        "id": cat["id"],
                        "name": cat["name"],
                        "type": cat["type"],
                        "children": build_hierarchy(cat["id"])
                    }
                    result.append(cat_data)
            return result
        
        hierarchy = build_hierarchy()
        
        # Also return flat list for easier frontend processing
        flat_categories = []
        for cat in categories:
            flat_categories.append({
                "id": cat["id"],
                "name": cat["name"],
                "type": cat["type"],
                "parent_id": cat["parent_id"]
            })
        
        return {
            "hierarchy": hierarchy,
            "flat": flat_categories,
            "total": len(flat_categories)
        }
        
    except Exception as e:
        logger.error(f"Error getting category hierarchy: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get category hierarchy: {str(e)}")

@app.get("/api/categories/")
async def get_all_categories():
    """
    Get all document categories as a flat list for frontend compatibility.
    This endpoint provides backward compatibility for the frontend.
    """
    try:
        table_name = "document_categories"
        
        # Get all categories for documents
        query = f"""
        SELECT id, name, type, parent_id, sort_order, is_active, created_at, updated_at, description
        FROM {table_name}
        WHERE is_active = true
        ORDER BY sort_order, name
        """
        
        categories = supabase.execute_query(query)
        
        if not isinstance(categories, list):
            return []
        
        # Format categories for frontend
        formatted_categories = []
        for cat in categories:
            formatted_cat = {
                "id": str(cat["id"]),
                "name": cat["name"],
                "type": cat["type"],
                "parent_id": str(cat["parent_id"]) if cat["parent_id"] else None,
                "description": cat.get("description", ""),
                "is_active": cat["is_active"],
                "sort_order": cat["sort_order"],
                "created_at": cat["created_at"],
                "updated_at": cat["updated_at"],
                "full_path": cat["name"],  # Will be calculated if needed
                "level": 0,  # Will be calculated if needed
                "children": []
            }
            formatted_categories.append(formatted_cat)
        
        return formatted_categories
        
    except Exception as e:
        logger.error(f"Error getting all categories: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get categories: {str(e)}")

@app.get("/api/categories/by-level/{category_type}/{level}")
async def get_categories_by_level(category_type: str, level: int, parent_id: Optional[str] = None):
    """
    Get categories at a specific level, optionally filtered by parent.
    """
    try:
        if category_type not in ["document", "website"]:
            raise HTTPException(status_code=400, detail="Category type must be 'document' or 'website'")
        
        if level not in [1, 2, 3, 4]:
            raise HTTPException(status_code=400, detail="Level must be between 1 and 4")
        
        # Map level to type
        level_to_type = {
            1: "main_category",
            2: "category",
            3: "sub_category", 
            4: "minor_category"
        }
        
        type_filter = level_to_type[level]
        table_name = "document_categories" if category_type == "document" else "website_categories"
        
        logger.info(f"Getting categories by level: type={category_type}, level={level}, parent_id={parent_id}, type_filter={type_filter}")
        
        # Use Supabase client directly for proper filtering
        if supabase.supabase is None:
            logger.error("Supabase client not available")
            return []
        
        try:
            # Build the query using Supabase client
            query = supabase.supabase.table(table_name).select("*").eq("type", type_filter).eq("is_active", True)
            
            if parent_id:
                query = query.eq("parent_id", parent_id)
            else:
                # For level 1 (main_category), parent_id should be null
                if level == 1:
                    query = query.is_("parent_id", "null")
            
            query = query.order("sort_order").order("name")
            
            result = query.execute()
            
            if hasattr(result, 'data') and result.data:
                categories = result.data
                logger.info(f"Found {len(categories)} categories for level {level}, parent_id={parent_id}")
            else:
                logger.warning(f"No categories found for level {level}, parent_id={parent_id}")
                categories = []
                
        except Exception as query_error:
            logger.error(f"Error executing Supabase query: {query_error}")
            # Fallback to manual filtering
            all_categories = supabase.execute_query(f"SELECT * FROM {table_name}")
            if isinstance(all_categories, list):
                categories = []
                for cat in all_categories:
                    if (cat.get("type") == type_filter and 
                        cat.get("is_active") == True and
                        (parent_id is None or cat.get("parent_id") == parent_id)):
                        categories.append(cat)
                logger.info(f"Fallback filtering found {len(categories)} categories")
            else:
                categories = []
        
        # Format categories for frontend
        formatted_categories = []
        for cat in categories:
            formatted_cat = {
                "id": str(cat["id"]),
                "name": cat["name"],
                "type": cat["type"],
                "parent_id": str(cat["parent_id"]) if cat["parent_id"] else None,
                "description": cat.get("description", ""),
                "is_active": cat["is_active"],
                "sort_order": cat["sort_order"],
                "created_at": cat.get("created_at"),
                "updated_at": cat.get("updated_at")
            }
            formatted_categories.append(formatted_cat)
        
        logger.info(f"Returning {len(formatted_categories)} formatted categories")
        return {"categories": formatted_categories, "count": len(formatted_categories)}
        
    except Exception as e:
        logger.error(f"Error getting categories by level: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get categories by level: {str(e)}")

# Add new endpoints for category reassignment functionality

# Category Change Audit Trail Models
class CategoryChangeLog(BaseModel):
    document_id: Optional[str] = None
    website_id: Optional[str] = None
    old_main_category: Optional[str] = None
    old_category: Optional[str] = None
    old_sub_category: Optional[str] = None
    old_minor_category: Optional[str] = None
    new_main_category: Optional[str] = None
    new_category: Optional[str] = None
    new_sub_category: Optional[str] = None
    new_minor_category: Optional[str] = None
    changed_by: str
    change_reason: Optional[str] = None

class CategoryReassignmentRequest(BaseModel):
    main_category_id: Optional[str] = None
    category_id: Optional[str] = None
    sub_category_id: Optional[str] = None
    minor_category_id: Optional[str] = None
    changed_by: str = "system"
    change_reason: Optional[str] = None

class BulkCategoryReassignmentRequest(BaseModel):
    item_ids: List[str] = Field(..., min_items=1, description="List of document or website IDs")
    main_category_id: Optional[str] = None
    category_id: Optional[str] = None
    sub_category_id: Optional[str] = None
    minor_category_id: Optional[str] = None
    changed_by: str = "system"
    change_reason: Optional[str] = None

@app.post("/api/documents/{document_id}/reassign-category")
async def reassign_document_category(
    document_id: str,
    reassignment_request: CategoryReassignmentRequest
):
    """Reassign category for a single document with audit trail"""
    try:
        logger.info(f"Reassigning category for document: {document_id}")
        
        # Get current document details for audit trail
        query = supabase.supabase.table('documents').select('*').eq('id', document_id).single()
        result = await run_in_threadpool(query.execute)
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Document not found")
        
        current_doc = result.data
        
        # Prepare update data
        update_data = {}
        if reassignment_request.main_category_id:
            update_data['main_category'] = reassignment_request.main_category_id
        if reassignment_request.category_id:
            update_data['category'] = reassignment_request.category_id
        if reassignment_request.sub_category_id:
            update_data['sub_category'] = reassignment_request.sub_category_id
        if reassignment_request.minor_category_id:
            update_data['minor_category'] = reassignment_request.minor_category_id
        
        update_data['updated_at'] = datetime.utcnow().isoformat()
        
        # Update the document
        update_query = supabase.supabase.table('documents').update(update_data).eq('id', document_id)
        update_result = await run_in_threadpool(update_query.execute)
        
        # Create audit trail entry
        audit_data = {
            'id': str(uuid4()),
            'document_id': document_id,
            'old_main_category': current_doc.get('main_category'),
            'old_category': current_doc.get('category'),
            'old_sub_category': current_doc.get('sub_category'),
            'old_minor_category': current_doc.get('minor_category'),
            'new_main_category': reassignment_request.main_category_id,
            'new_category': reassignment_request.category_id,
            'new_sub_category': reassignment_request.sub_category_id,
            'new_minor_category': reassignment_request.minor_category_id,
            'changed_by': reassignment_request.changed_by,
            'change_reason': reassignment_request.change_reason,
            'changed_at': datetime.utcnow().isoformat()
        }
        
        # Try to create audit trail (best effort)
        try:
            audit_query = supabase.supabase.table('category_change_logs').insert(audit_data)
            await run_in_threadpool(audit_query.execute)
            logger.info(f"Created audit trail for document {document_id}")
        except Exception as audit_error:
            logger.warning(f"Failed to create audit trail: {audit_error}")
        
        return {
            "success": True,
            "message": "Document category reassigned successfully",
            "document_id": document_id,
            "updated_data": update_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reassigning document category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/websites/{website_id}/reassign-category")
async def reassign_website_category(
    website_id: str,
    reassignment_request: CategoryReassignmentRequest
):
    """Reassign category for a single website with audit trail"""
    try:
        logger.info(f"Reassigning category for website: {website_id}")
        
        # Get current website details for audit trail
        query = supabase.supabase.table('websites').select('*').eq('id', website_id).single()
        result = await run_in_threadpool(query.execute)
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Website not found")
        
        current_website = result.data
        
        # Prepare update data
        update_data = {}
        if reassignment_request.main_category_id:
            update_data['main_category'] = reassignment_request.main_category_id
        if reassignment_request.category_id:
            update_data['category'] = reassignment_request.category_id
        if reassignment_request.sub_category_id:
            update_data['sub_category'] = reassignment_request.sub_category_id
        if reassignment_request.minor_category_id:
            update_data['minor_category'] = reassignment_request.minor_category_id
        
        update_data['updated_at'] = datetime.utcnow().isoformat()
        
        # Update the website
        update_query = supabase.supabase.table('websites').update(update_data).eq('id', website_id)
        update_result = await run_in_threadpool(update_query.execute)
        
        # Create audit trail entry
        audit_data = {
            'id': str(uuid4()),
            'website_id': website_id,
            'old_main_category': current_website.get('main_category'),
            'old_category': current_website.get('category'),
            'old_sub_category': current_website.get('sub_category'),
            'old_minor_category': current_website.get('minor_category'),
            'new_main_category': reassignment_request.main_category_id,
            'new_category': reassignment_request.category_id,
            'new_sub_category': reassignment_request.sub_category_id,
            'new_minor_category': reassignment_request.minor_category_id,
            'changed_by': reassignment_request.changed_by,
            'change_reason': reassignment_request.change_reason,
            'changed_at': datetime.utcnow().isoformat()
        }
        
        # Try to create audit trail (best effort)
        try:
            audit_query = supabase.supabase.table('category_change_logs').insert(audit_data)
            await run_in_threadpool(audit_query.execute)
            logger.info(f"Created audit trail for website {website_id}")
        except Exception as audit_error:
            logger.warning(f"Failed to create audit trail: {audit_error}")
        
        return {
            "success": True,
            "message": "Website category reassigned successfully",
            "website_id": website_id,
            "updated_data": update_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reassigning website category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/documents/bulk-reassign-category")
async def bulk_reassign_document_categories(
    reassignment_request: BulkCategoryReassignmentRequest
):
    """Bulk reassign categories for multiple documents"""
    try:
        logger.info(f"Bulk reassigning categories for {len(reassignment_request.item_ids)} documents")
        
        successful_updates = []
        failed_updates = []
        
        for document_id in reassignment_request.item_ids:
            try:
                # Create individual reassignment request
                individual_request = CategoryReassignmentRequest(
                    main_category_id=reassignment_request.main_category_id,
                    category_id=reassignment_request.category_id,
                    sub_category_id=reassignment_request.sub_category_id,
                    minor_category_id=reassignment_request.minor_category_id,
                    changed_by=reassignment_request.changed_by,
                    change_reason=reassignment_request.change_reason
                )
                
                # Call individual reassignment function
                result = await reassign_document_category(document_id, individual_request)
                successful_updates.append({
                    "document_id": document_id,
                    "result": result
                })
                
            except Exception as e:
                logger.error(f"Failed to update document {document_id}: {str(e)}")
                failed_updates.append({
                    "document_id": document_id,
                    "error": str(e)
                })
        
        return {
            "success": True,
            "message": f"Bulk reassignment completed: {len(successful_updates)} successful, {len(failed_updates)} failed",
            "successful_updates": successful_updates,
            "failed_updates": failed_updates,
            "total_processed": len(reassignment_request.item_ids)
        }
        
    except Exception as e:
        logger.error(f"Error in bulk document category reassignment: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/websites/bulk-reassign-category")
async def bulk_reassign_website_categories(
    reassignment_request: BulkCategoryReassignmentRequest
):
    """Bulk reassign categories for multiple websites"""
    try:
        logger.info(f"Bulk reassigning categories for {len(reassignment_request.item_ids)} websites")
        
        successful_updates = []
        failed_updates = []
        
        for website_id in reassignment_request.item_ids:
            try:
                # Create individual reassignment request
                individual_request = CategoryReassignmentRequest(
                    main_category_id=reassignment_request.main_category_id,
                    category_id=reassignment_request.category_id,
                    sub_category_id=reassignment_request.sub_category_id,
                    minor_category_id=reassignment_request.minor_category_id,
                    changed_by=reassignment_request.changed_by,
                    change_reason=reassignment_request.change_reason
                )
                
                # Call individual reassignment function
                result = await reassign_website_category(website_id, individual_request)
                successful_updates.append({
                    "website_id": website_id,
                    "result": result
                })
                
            except Exception as e:
                logger.error(f"Failed to update website {website_id}: {str(e)}")
                failed_updates.append({
                    "website_id": website_id,
                    "error": str(e)
                })
        
        return {
            "success": True,
            "message": f"Bulk reassignment completed: {len(successful_updates)} successful, {len(failed_updates)} failed",
            "successful_updates": successful_updates,
            "failed_updates": failed_updates,
            "total_processed": len(reassignment_request.item_ids)
        }
        
    except Exception as e:
        logger.error(f"Error in bulk website category reassignment: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/categories/by-level/{entity_type}/{level}")
async def get_categories_by_level(
    entity_type: str,
    level: int,
    parent_id: Optional[str] = Query(None)
):
    """Get categories filtered by entity type, level, and parent"""
    try:
        logger.info(f"Getting categories by level: type={entity_type}, level={level}, parent_id={parent_id}")
        
        # Map level to category type
        level_to_type = {
            1: 'main_category',
            2: 'category', 
            3: 'sub_category',
            4: 'minor_category'
        }
        
        type_filter = level_to_type.get(level)
        if not type_filter:
            raise HTTPException(status_code=400, detail="Invalid level. Must be 1-4")
        
        # Build query
        query = supabase.supabase.table('document_categories').select('*').eq('type', type_filter).eq('is_active', True)
        
        if parent_id:
            query = query.eq('parent_id', parent_id)
        elif level > 1:
            # If no parent specified for non-root level, return empty
            return {"categories": [], "count": 0}
        else:
            # For level 1 (main_category), parent_id should be null
            query = query.is_('parent_id', 'null')
        
        query = query.order('sort_order').order('name')
        result = await run_in_threadpool(query.execute)
        
        if result.data:
            categories = [
                {
                    "id": cat["id"],
                    "name": cat["name"],
                    "type": cat["type"],
                    "parent_id": cat.get("parent_id"),
                    "description": cat.get("description", ""),
                    "full_path": cat.get("full_path", cat["name"]),
                    "level": level
                }
                for cat in result.data
            ]
            logger.info(f"Found {len(categories)} categories for level {level}, parent_id={parent_id}")
        else:
            categories = []
            logger.warning(f"No categories found for level {level}, parent_id={parent_id}")
        
        logger.info(f"Returning {len(categories)} formatted categories")
        return {"categories": categories, "count": len(categories)}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting categories by level: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/category-change-logs/{entity_type}/{entity_id}")
async def get_category_change_history(
    entity_type: str,
    entity_id: str
):
    """Get category change history for a document or website"""
    try:
        logger.info(f"Getting category change history for {entity_type}: {entity_id}")
        
        # Build query based on entity type
        if entity_type == "document":
            query = supabase.supabase.table('category_change_logs').select('*').eq('document_id', entity_id)
        elif entity_type == "website":
            query = supabase.supabase.table('category_change_logs').select('*').eq('website_id', entity_id)
        else:
            raise HTTPException(status_code=400, detail="Invalid entity type. Must be 'document' or 'website'")
        
        query = query.order('changed_at', desc=True)
        result = await run_in_threadpool(query.execute)
        
        change_logs = result.data if result.data else []
        
        return {
            "change_logs": change_logs,
            "count": len(change_logs),
            "entity_type": entity_type,
            "entity_id": entity_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting category change history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/debug/re-extract-documents")
async def re_extract_documents():
    """Re-extract chunks for all documents that don't have chunks."""
    try:
        from document_extraction import extract_document
        
        # Get all documents
        documents_query = "SELECT * FROM documents"
        documents = supabase.execute_query(documents_query)
        
        if not documents:
            return {"message": "No documents found", "processed": 0}
        
        processed = 0
        for doc in documents:
            doc_id = doc.get('id')
            file_path = doc.get('file_path', '')
            
            # Check if this document has chunks
            chunks_query = f"SELECT COUNT(*) as count FROM document_chunks WHERE document_id = '{doc_id}'"
            chunks_result = supabase.execute_query(chunks_query)
            
            chunk_count = 0
            if chunks_result and len(chunks_result) > 0:
                chunk_count = chunks_result[0].get('count', 0)
            
            if chunk_count == 0:
                logger.info(f"Re-extracting document: {doc.get('display_name', 'Unknown')}")
                try:
                    # Try to extract from the file path
                    full_path = os.path.join("data", "uploads", os.path.basename(file_path))
                    if os.path.exists(full_path):
                        document_chunks = extract_document(
                            file_path=full_path,
                            uploaded_by=doc.get('uploaded_by', 'system')
                        )
                        logger.info(f"Re-extracted {len(document_chunks)} chunks for {doc.get('display_name')}")
                        processed += 1
                    else:
                        logger.warning(f"File not found: {full_path}")
                except Exception as e:
                    logger.error(f"Error re-extracting {doc.get('display_name')}: {str(e)}")
        
        return {
            "message": f"Re-extraction completed",
            "processed": processed,
            "total_documents": len(documents)
        }
        
    except Exception as e:
        logger.error(f"Error in re-extraction: {str(e)}")
        return {"error": str(e), "processed": 0}

@app.post("/api/documents/{document_id}/reprocess")
async def reprocess_document(document_id: str, extraction_tool: Optional[str] = "auto"):
    """Reprocess a specific document with optional extraction tool preference."""
    try:
        logger.info(f"Reprocessing document {document_id} with tool: {extraction_tool}")
        
        # Get document information
        document_query = f"SELECT * FROM documents WHERE id = '{document_id}'"
        documents = supabase.execute_query(document_query)
        
        if not documents or len(documents) == 0:
            raise HTTPException(status_code=404, detail="Document not found")
        
        document = documents[0]
        file_path = document.get('file_path', '')
        display_name = document.get('display_name', 'Unknown')
        
        # Check if file exists
        full_path = os.path.join("data", "uploads", os.path.basename(file_path))
        if not os.path.exists(full_path):
            # Try alternative path locations
            alternative_paths = [
                file_path,
                os.path.join("data", os.path.basename(file_path)),
                os.path.join("backend", "data", "uploads", os.path.basename(file_path))
            ]
            
            full_path = None
            for alt_path in alternative_paths:
                if os.path.exists(alt_path):
                    full_path = alt_path
                    break
            
            if not full_path:
                logger.error(f"File not found for document {display_name}. Searched paths: {alternative_paths}")
                raise HTTPException(status_code=404, detail=f"Document file not found: {display_name}")
        
        # Delete existing chunks
        delete_chunks_query = f"DELETE FROM document_chunks WHERE document_id = '{document_id}'"
        supabase.execute_query(delete_chunks_query)
        logger.info(f"Deleted existing chunks for document {display_name}")
        
        # Update document status to processing
        update_status_query = f"""
        UPDATE documents 
        SET status = 'Processing', updated_at = NOW() 
        WHERE id = '{document_id}'
        """
        supabase.execute_query(update_status_query)
        
        # Re-extract with enhanced visual content support
        try:
            from document_extractor import extract_document_with_visual_content
            
            document_chunks = extract_document_with_visual_content(
                file_path=full_path,
                uploaded_by=document.get('uploaded_by', 'system'),
                extract_tables=True,
                extract_images=True,
                extract_charts=True
            )
            
            if document_chunks and len(document_chunks) > 0:
                # Update document status to extracted
                update_status_query = f"""
                UPDATE documents 
                SET status = 'Extracted', updated_at = NOW() 
                WHERE id = '{document_id}'
                """
                supabase.execute_query(update_status_query)
                
                logger.info(f"Successfully reprocessed {display_name}: {len(document_chunks)} chunks created")
                
                return {
                    "success": True,
                    "message": f"Document reprocessed successfully",
                    "document_name": display_name,
                    "chunks_extracted": len(document_chunks),
                    "extraction_tool": extraction_tool,
                    "status": "Extracted"
                }
            else:
                # Update document status to error
                update_status_query = f"""
                UPDATE documents 
                SET status = 'Error', updated_at = NOW() 
                WHERE id = '{document_id}'
                """
                supabase.execute_query(update_status_query)
                
                logger.warning(f"No chunks extracted during reprocessing of {display_name}")
                return {
                    "success": False,
                    "message": f"No content extracted from document",
                    "document_name": display_name,
                    "chunks_extracted": 0,
                    "extraction_tool": extraction_tool,
                    "status": "Error"
                }
                
        except Exception as extraction_error:
            # Update document status to error
            update_status_query = f"""
            UPDATE documents 
            SET status = 'Error', updated_at = NOW() 
            WHERE id = '{document_id}'
            """
            supabase.execute_query(update_status_query)
            
            logger.error(f"Error during document reprocessing for {display_name}: {str(extraction_error)}")
            raise HTTPException(status_code=500, detail=f"Extraction failed: {str(extraction_error)}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reprocessing document {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/websites/{website_id}/reprocess")
async def reprocess_website(website_id: str, extraction_tool: Optional[str] = "trafilatura"):
    """Reprocess a specific website with optional extraction tool preference."""
    try:
        logger.info(f"Reprocessing website {website_id} with tool: {extraction_tool}")
        
        # Get website information
        website_query = f"SELECT * FROM websites WHERE id = '{website_id}'"
        websites = supabase.execute_query(website_query)
        
        if not websites or len(websites) == 0:
            raise HTTPException(status_code=404, detail="Website not found")
        
        website = websites[0]
        url = website.get('url', '')
        display_name = website.get('url', 'Unknown')
        
        if not url:
            raise HTTPException(status_code=400, detail="Website URL is missing")
        
        # Delete existing chunks
        delete_chunks_query = f"DELETE FROM website_chunks WHERE website_id = '{website_id}'"
        supabase.execute_query(delete_chunks_query)
        logger.info(f"Deleted existing chunks for website {display_name}")
        
        # Update website status to processing
        update_status_query = f"""
        UPDATE websites 
        SET status = 'Processing', updated_at = NOW() 
        WHERE id = '{website_id}'
        """
        supabase.execute_query(update_status_query)
        
        # Re-extract website content
        try:
            from website_scraper import extract_website_text
            
            # Extract with specified tool preference
            extraction_options = {
                'extractor_type': extraction_tool,
                'extract_tables': True,
                'extract_images': False,
                'follow_links': False,
                'max_pages': 1
            }
            
            extracted_text, metadata = extract_website_text(url, **extraction_options)
            
            if extracted_text and len(extracted_text.strip()) > 50:
                # Create website chunks
                chunks = []
                chunk_size = 1000
                text_chunks = [extracted_text[i:i+chunk_size] for i in range(0, len(extracted_text), chunk_size)]
                
                for i, chunk_text in enumerate(text_chunks):
                    if chunk_text.strip():
                        chunk_data = {
                            'website_id': website_id,
                            'chunk_index': i,
                            'text': chunk_text.strip(),
                            'metadata': metadata
                        }
                        chunks.append(chunk_data)
                
                # Insert chunks into database
                if chunks:
                    for chunk in chunks:
                        text_escaped = chunk['text'].replace("'", "''")
                        metadata_json = json.dumps(chunk['metadata']).replace("'", "''")
                        insert_chunk_query = f"""
                        INSERT INTO website_chunks (website_id, chunk_index, text, metadata)
                        VALUES ('{chunk['website_id']}', {chunk['chunk_index']}, '{text_escaped}', '{metadata_json}')
                        """
                        supabase.execute_query(insert_chunk_query)
                
                # Update website status to extracted
                update_status_query = f"""
                UPDATE websites 
                SET status = 'Extracted', updated_at = NOW() 
                WHERE id = '{website_id}'
                """
                supabase.execute_query(update_status_query)
                
                logger.info(f"Successfully reprocessed website {display_name}: {len(chunks)} chunks created")
                
                return {
                    "success": True,
                    "message": f"Website reprocessed successfully",
                    "website_url": url,
                    "chunks_extracted": len(chunks),
                    "extraction_tool": extraction_tool,
                    "status": "Extracted"
                }
            else:
                # Update website status to error
                update_status_query = f"""
                UPDATE websites 
                SET status = 'Error', updated_at = NOW() 
                WHERE id = '{website_id}'
                """
                supabase.execute_query(update_status_query)
                
                logger.warning(f"No content extracted during reprocessing of website {display_name}")
                return {
                    "success": False,
                    "message": f"No content extracted from website",
                    "website_url": url,
                    "chunks_extracted": 0,
                    "extraction_tool": extraction_tool,
                    "status": "Error"
                }
                
        except Exception as extraction_error:
            # Update website status to error
            update_status_query = f"""
            UPDATE websites 
            SET status = 'Error', updated_at = NOW() 
            WHERE id = '{website_id}'
            """
            supabase.execute_query(update_status_query)
            
            logger.error(f"Error during website reprocessing for {display_name}: {str(extraction_error)}")
            raise HTTPException(status_code=500, detail=f"Extraction failed: {str(extraction_error)}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reprocessing website {website_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/documents/{document_id}/reprocess")
async def reprocess_document(document_id: str, extraction_tool: Optional[str] = "auto"):
    """Reprocess a specific document with optional extraction tool preference."""
    try:
        logger.info(f"Reprocessing document {document_id} with tool: {extraction_tool}")
        
        # Get document information
        document_query = f"SELECT * FROM documents WHERE id = '{document_id}'"
        documents = supabase.execute_query(document_query)
        
        if not documents or len(documents) == 0:
            raise HTTPException(status_code=404, detail="Document not found")
        
        document = documents[0]
        file_path = document.get('file_path', '')
        display_name = document.get('display_name', 'Unknown')
        
        # Check if file exists
        full_path = os.path.join("data", "uploads", os.path.basename(file_path))
        if not os.path.exists(full_path):
            # Try alternative path locations
            alternative_paths = [
                file_path,
                os.path.join("data", os.path.basename(file_path)),
                os.path.join("backend", "data", "uploads", os.path.basename(file_path))
            ]
            
            full_path = None
            for alt_path in alternative_paths:
                if os.path.exists(alt_path):
                    full_path = alt_path
                    break
            
            if not full_path:
                logger.error(f"File not found for document {display_name}. Searched paths: {alternative_paths}")
                raise HTTPException(status_code=404, detail=f"Document file not found: {display_name}")
        
        # Delete existing chunks
        delete_chunks_query = f"DELETE FROM document_chunks WHERE document_id = '{document_id}'"
        supabase.execute_query(delete_chunks_query)
        logger.info(f"Deleted existing chunks for document {display_name}")
        
        # Update document status to processing
        update_status_query = f"""
        UPDATE documents 
        SET status = 'Processing', updated_at = NOW() 
        WHERE id = '{document_id}'
        """
        supabase.execute_query(update_status_query)
        
        # Re-extract with enhanced visual content support
        try:
            from document_extractor import extract_document_with_visual_content
            
            document_chunks = extract_document_with_visual_content(
                file_path=full_path,
                uploaded_by=document.get('uploaded_by', 'system'),
                extract_tables=True,
                extract_images=True,
                extract_charts=True
            )
            
            if document_chunks and len(document_chunks) > 0:
                # Update document status to extracted
                update_status_query = f"""
                UPDATE documents 
                SET status = 'Extracted', updated_at = NOW() 
                WHERE id = '{document_id}'
                """
                supabase.execute_query(update_status_query)
                
                logger.info(f"Successfully reprocessed {display_name}: {len(document_chunks)} chunks created")
                
                return {
                    "success": True,
                    "message": f"Document reprocessed successfully",
                    "document_name": display_name,
                    "chunks_extracted": len(document_chunks),
                    "extraction_tool": extraction_tool,
                    "status": "Extracted"
                }
            else:
                # Update document status to error
                update_status_query = f"""
                UPDATE documents 
                SET status = 'Error', updated_at = NOW() 
                WHERE id = '{document_id}'
                """
                supabase.execute_query(update_status_query)
                
                logger.warning(f"No chunks extracted during reprocessing of {display_name}")
                return {
                    "success": False,
                    "message": f"No content extracted from document",
                    "document_name": display_name,
                    "chunks_extracted": 0,
                    "extraction_tool": extraction_tool,
                    "status": "Error"
                }
                
        except Exception as extraction_error:
            # Update document status to error
            update_status_query = f"""
            UPDATE documents 
            SET status = 'Error', updated_at = NOW() 
            WHERE id = '{document_id}'
            """
            supabase.execute_query(update_status_query)
            
            logger.error(f"Error during document reprocessing for {display_name}: {str(extraction_error)}")
            raise HTTPException(status_code=500, detail=f"Extraction failed: {str(extraction_error)}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reprocessing document {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/websites/{website_id}/reprocess")
async def reprocess_website(website_id: str, extraction_tool: Optional[str] = "trafilatura"):
    """Reprocess a specific website with optional extraction tool preference."""
    try:
        logger.info(f"Reprocessing website {website_id} with tool: {extraction_tool}")
        
        # Get website information
        website_query = f"SELECT * FROM websites WHERE id = '{website_id}'"
        websites = supabase.execute_query(website_query)
        
        if not websites or len(websites) == 0:
            raise HTTPException(status_code=404, detail="Website not found")
        
        website = websites[0]
        url = website.get('url', '')
        display_name = website.get('url', 'Unknown')
        
        if not url:
            raise HTTPException(status_code=400, detail="Website URL is missing")
        
        # Delete existing chunks
        delete_chunks_query = f"DELETE FROM website_chunks WHERE website_id = '{website_id}'"
        supabase.execute_query(delete_chunks_query)
        logger.info(f"Deleted existing chunks for website {display_name}")
        
        # Update website status to processing
        update_status_query = f"""
        UPDATE websites 
        SET status = 'Processing', updated_at = NOW() 
        WHERE id = '{website_id}'
        """
        supabase.execute_query(update_status_query)
        
        # Re-extract website content
        try:
            from website_scraper import extract_website_text
            
            # Extract with specified tool preference
            extraction_options = {
                'extractor_type': extraction_tool,
                'extract_tables': True,
                'extract_images': False,
                'follow_links': False,
                'max_pages': 1
            }
            
            extracted_text, metadata = extract_website_text(url, **extraction_options)
            
            if extracted_text and len(extracted_text.strip()) > 50:
                # Create website chunks
                chunks = []
                chunk_size = 1000
                text_chunks = [extracted_text[i:i+chunk_size] for i in range(0, len(extracted_text), chunk_size)]
                
                for i, chunk_text in enumerate(text_chunks):
                    if chunk_text.strip():
                        chunk_data = {
                            'website_id': website_id,
                            'chunk_index': i,
                            'text': chunk_text.strip(),
                            'metadata': metadata
                        }
                        chunks.append(chunk_data)
                
                # Insert chunks into database
                if chunks:
                    for chunk in chunks:
                        text_escaped = chunk['text'].replace("'", "''")
                        metadata_json = json.dumps(chunk['metadata']).replace("'", "''")
                        insert_chunk_query = f"""
                        INSERT INTO website_chunks (website_id, chunk_index, text, metadata)
                        VALUES ('{chunk['website_id']}', {chunk['chunk_index']}, '{text_escaped}', '{metadata_json}')
                        """
                        supabase.execute_query(insert_chunk_query)
                
                # Update website status to extracted
                update_status_query = f"""
                UPDATE websites 
                SET status = 'Extracted', updated_at = NOW() 
                WHERE id = '{website_id}'
                """
                supabase.execute_query(update_status_query)
                
                logger.info(f"Successfully reprocessed website {display_name}: {len(chunks)} chunks created")
                
                return {
                    "success": True,
                    "message": f"Website reprocessed successfully",
                    "website_url": url,
                    "chunks_extracted": len(chunks),
                    "extraction_tool": extraction_tool,
                    "status": "Extracted"
                }
            else:
                # Update website status to error
                update_status_query = f"""
                UPDATE websites 
                SET status = 'Error', updated_at = NOW() 
                WHERE id = '{website_id}'
                """
                supabase.execute_query(update_status_query)
                
                logger.warning(f"No content extracted during reprocessing of website {display_name}")
                return {
                    "success": False,
                    "message": f"No content extracted from website",
                    "website_url": url,
                    "chunks_extracted": 0,
                    "extraction_tool": extraction_tool,
                    "status": "Error"
                }
                
        except Exception as extraction_error:
            # Update website status to error
            update_status_query = f"""
            UPDATE websites 
            SET status = 'Error', updated_at = NOW() 
            WHERE id = '{website_id}'
            """
            supabase.execute_query(update_status_query)
            
            logger.error(f"Error during website reprocessing for {display_name}: {str(extraction_error)}")
            raise HTTPException(status_code=500, detail=f"Extraction failed: {str(extraction_error)}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reprocessing website {website_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# -------------------------------------------------------------
# Bind canonical answer logic implementation (avoids duplicates)
# -------------------------------------------------------------
try:
    from .answer_logic_final import generate_clean_answer_with_sources as _canonical_impl  # type: ignore
    generate_clean_answer_with_sources = _canonical_impl  # override duplicates
    logger.info("✅ Bound canonical generate_clean_answer_with_sources implementation from answer_logic_final.py")
except Exception as _bind_exc:  # pragma: no cover
    logger.error(f"❌ Failed to bind canonical answer logic: {_bind_exc}")

