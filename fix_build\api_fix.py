#!/usr/bin/env python3
"""
API endpoint fix for RailGPT
This script checks and fixes the API routing in the simple_server.py file
"""
import os
import sys
import logging
from fastapi import FastAPI, HTTPException

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Sample API endpoint handler
async def query_handler(request):
    """Sample query handler to verify routing is working"""
    try:
        # Log that we received a request
        logger.info("Received query request")
        
        # Return a diagnostic response
        return {
            "status": "ok",
            "message": "API endpoint is now working",
            "note": "This is a temporary diagnostic response"
        }
    except Exception as e:
        logger.error(f"Error in query handler: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

def check_and_fix_api():
    """Check if the API is properly configured and fix if needed"""
    try:
        # First, check if we're in the application environment
        in_app_env = os.path.exists('/app/simple_server.py')
        logger.info(f"In application environment: {in_app_env}")
        
        if in_app_env:
            # Import the app to verify it's working
            from simple_server import app
            
            # Add the query endpoint if it doesn't exist
            found_query_route = False
            for route in app.routes:
                if route.path == "/api/query":
                    found_query_route = True
                    break
            
            if not found_query_route:
                logger.info("Adding missing /api/query endpoint")
                
                # Import the query handler from server.py if possible
                try:
                    from server import query as real_query_handler
                    app.post("/api/query")(real_query_handler)
                    logger.info("Added real query handler from server.py")
                except ImportError:
                    # If that fails, add a diagnostic handler
                    app.post("/api/query")(query_handler)
                    logger.info("Added diagnostic query handler")
            
            logger.info("API endpoints configured:")
            for route in app.routes:
                logger.info(f"  {route.methods} {route.path}")
            
            return True
        else:
            logger.info("Not in application environment, skipping API check")
            return False
    except Exception as e:
        logger.error(f"Error checking/fixing API: {str(e)}")
        return False

if __name__ == "__main__":
    check_and_fix_api()
