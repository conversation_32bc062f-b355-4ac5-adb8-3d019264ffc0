# Complete Category System Rebuild Script
# Creates the exact hierarchy specified: Main Category -> Category 1 -> Sub Category 1 -> Minor Categories

import logging
import asyncio
import uuid
from datetime import datetime
from typing import Dict, List, Optional

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_supabase_client():
    """Get Supabase client with proper error handling."""
    try:
        from supabase_client import supabase
        if not supabase or not hasattr(supabase, 'supabase') or supabase.supabase is None:
            logger.error("Supabase client not available")
            return None
        return supabase
    except Exception as e:
        logger.error(f"Error getting Supabase client: {str(e)}")
        return None

async def create_category(name: str, category_type: str, parent_id: Optional[str] = None, 
                         description: str = "", sort_order: int = 1, table_name: str = 'document_categories') -> Optional[str]:
    """Create a single category and return its ID."""
    try:
        supabase = get_supabase_client()
        if not supabase:
            logger.error("Cannot create category without Supabase client")
            return None

        category_id = str(uuid.uuid4())
        category_data = {
            "id": category_id,
            "name": name,
            "type": category_type,
            "parent_id": parent_id,
            "description": description,
            "sort_order": sort_order,
            "is_active": True,
            "created_at": datetime.utcnow().isoformat() + "Z",
            "updated_at": datetime.utcnow().isoformat() + "Z"
        }

        logger.info(f"Creating category '{name}' in {table_name} with parent_id: {parent_id}")
        
        response = supabase.supabase.table(table_name).insert(category_data).execute()
        
        if response.data:
            logger.info(f"✓ Successfully created category: {name} (ID: {category_id})")
            return category_id
        else:
            logger.error(f"✗ Failed to create category: {name}")
            return None

    except Exception as e:
        logger.error(f"Error creating category '{name}': {str(e)}")
        return None

async def build_document_category_hierarchy():
    """Build the complete document category hierarchy."""
    logger.info("Building document category hierarchy...")
    
    # Level 1: Main Category
    main_category_id = await create_category(
        name="Main Category",
        category_type="main_category",
        parent_id=None,
        description="Top-level main category for document organization",
        sort_order=1,
        table_name='document_categories'
    )
    
    if not main_category_id:
        logger.error("Failed to create Main Category")
        return False

    # Level 2: Category 1 (child of Main Category)
    category_1_id = await create_category(
        name="Category 1",
        category_type="sub_category",
        parent_id=main_category_id,
        description="First level subcategory under Main Category",
        sort_order=1,
        table_name='document_categories'
    )
    
    if not category_1_id:
        logger.error("Failed to create Category 1")
        return False

    # Level 3: Sub Category 1 (child of Category 1)
    sub_category_1_id = await create_category(
        name="Sub Category 1",
        category_type="sub_category",
        parent_id=category_1_id,
        description="Second level subcategory under Category 1",
        sort_order=1,
        table_name='document_categories'
    )
    
    if not sub_category_1_id:
        logger.error("Failed to create Sub Category 1")
        return False

    # Level 4: Minor Category 1 (child of Sub Category 1)
    minor_category_1_id = await create_category(
        name="Minor Category 1",
        category_type="minor_category",
        parent_id=sub_category_1_id,
        description="Third level minor category under Sub Category 1",
        sort_order=1,
        table_name='document_categories'
    )
    
    if not minor_category_1_id:
        logger.error("Failed to create Minor Category 1")
        return False

    # Level 4: Minor Category 2 (child of Sub Category 1)
    minor_category_2_id = await create_category(
        name="Minor Category 2",
        category_type="minor_category",
        parent_id=sub_category_1_id,
        description="Third level minor category under Sub Category 1",
        sort_order=2,
        table_name='document_categories'
    )
    
    if not minor_category_2_id:
        logger.error("Failed to create Minor Category 2")
        return False

    logger.info("✓ Document category hierarchy created successfully!")
    return True

async def build_website_category_hierarchy():
    """Build the complete website category hierarchy (mirror of document categories)."""
    logger.info("Building website category hierarchy...")
    
    # Level 1: Main Category
    main_category_id = await create_category(
        name="Main Category",
        category_type="main_category",
        parent_id=None,
        description="Top-level main category for website organization",
        sort_order=1,
        table_name='website_categories'
    )
    
    if not main_category_id:
        logger.error("Failed to create Main Category for websites")
        return False

    # Level 2: Category 1 (child of Main Category)
    category_1_id = await create_category(
        name="Category 1",
        category_type="sub_category",
        parent_id=main_category_id,
        description="First level subcategory under Main Category for websites",
        sort_order=1,
        table_name='website_categories'
    )
    
    if not category_1_id:
        logger.error("Failed to create Category 1 for websites")
        return False

    # Level 3: Sub Category 1 (child of Category 1)
    sub_category_1_id = await create_category(
        name="Sub Category 1",
        category_type="sub_category",
        parent_id=category_1_id,
        description="Second level subcategory under Category 1 for websites",
        sort_order=1,
        table_name='website_categories'
    )
    
    if not sub_category_1_id:
        logger.error("Failed to create Sub Category 1 for websites")
        return False

    # Level 4: Minor Category 1 (child of Sub Category 1)
    minor_category_1_id = await create_category(
        name="Minor Category 1",
        category_type="minor_category",
        parent_id=sub_category_1_id,
        description="Third level minor category under Sub Category 1 for websites",
        sort_order=1,
        table_name='website_categories'
    )
    
    if not minor_category_1_id:
        logger.error("Failed to create Minor Category 1 for websites")
        return False

    # Level 4: Minor Category 2 (child of Sub Category 1)
    minor_category_2_id = await create_category(
        name="Minor Category 2",
        category_type="minor_category",
        parent_id=sub_category_1_id,
        description="Third level minor category under Sub Category 1 for websites",
        sort_order=2,
        table_name='website_categories'
    )
    
    if not minor_category_2_id:
        logger.error("Failed to create Minor Category 2 for websites")
        return False

    logger.info("✓ Website category hierarchy created successfully!")
    return True

async def verify_hierarchy():
    """Verify the created hierarchy is correct."""
    try:
        supabase = get_supabase_client()
        if not supabase:
            return False

        logger.info("Verifying document category hierarchy...")
        
        # Get all document categories
        doc_response = supabase.supabase.table('document_categories').select('*').order('sort_order').execute()
        doc_categories = doc_response.data or []
        
        logger.info(f"Document categories created: {len(doc_categories)}")
        for cat in doc_categories:
            parent_name = "None"
            if cat['parent_id']:
                parent = next((c for c in doc_categories if c['id'] == cat['parent_id']), None)
                parent_name = parent['name'] if parent else "Unknown"
            logger.info(f"  - {cat['name']} (type: {cat['type']}, parent: {parent_name})")

        logger.info("Verifying website category hierarchy...")
        
        # Get all website categories
        web_response = supabase.supabase.table('website_categories').select('*').order('sort_order').execute()
        web_categories = web_response.data or []
        
        logger.info(f"Website categories created: {len(web_categories)}")
        for cat in web_categories:
            parent_name = "None"
            if cat['parent_id']:
                parent = next((c for c in web_categories if c['id'] == cat['parent_id']), None)
                parent_name = parent['name'] if parent else "Unknown"
            logger.info(f"  - {cat['name']} (type: {cat['type']}, parent: {parent_name})")

        # Verify we have exactly 5 categories in each table
        if len(doc_categories) == 5 and len(web_categories) == 5:
            logger.info("✓ Hierarchy verification successful!")
            return True
        else:
            logger.error(f"✗ Hierarchy verification failed! Expected 5 categories each, got {len(doc_categories)} doc, {len(web_categories)} web")
            return False

    except Exception as e:
        logger.error(f"Error verifying hierarchy: {str(e)}")
        return False

async def rebuild_category_system():
    """Main function to rebuild the entire category system."""
    logger.info("========================================")
    logger.info("  CATEGORY SYSTEM REBUILD STARTING")
    logger.info("========================================")
    
    # Step 1: Build document category hierarchy
    logger.info("Step 1: Building document category hierarchy...")
    doc_success = await build_document_category_hierarchy()
    
    if not doc_success:
        logger.error("Failed to build document category hierarchy")
        return False
    
    # Step 2: Build website category hierarchy
    logger.info("Step 2: Building website category hierarchy...")
    web_success = await build_website_category_hierarchy()
    
    if not web_success:
        logger.error("Failed to build website category hierarchy")
        return False
    
    # Step 3: Verify the hierarchy
    logger.info("Step 3: Verifying category hierarchy...")
    verification_success = await verify_hierarchy()
    
    if not verification_success:
        logger.error("Hierarchy verification failed")
        return False
    
    logger.info("========================================")
    logger.info("  CATEGORY SYSTEM REBUILD COMPLETED")
    logger.info("========================================")
    logger.info("✓ Document categories: 5 (Main Category -> Category 1 -> Sub Category 1 -> Minor Categories)")
    logger.info("✓ Website categories: 5 (Main Category -> Category 1 -> Sub Category 1 -> Minor Categories)")
    logger.info("✓ Hierarchy verification: PASSED")
    logger.info("✓ System ready for testing")
    
    return True

if __name__ == "__main__":
    success = asyncio.run(rebuild_category_system())
    if success:
        print("\n🎉 CATEGORY SYSTEM REBUILD SUCCESSFUL!")
    else:
        print("\n❌ CATEGORY SYSTEM REBUILD FAILED!")
        exit(1)
