#!/usr/bin/env python3
import sys
import os
sys.path.append('.')

print('🧪 Testing Document Search Fix...')
print('=' * 50)

try:
    # Import modules
    from server import DOCUMENT_CHUNKS
    from supabase_client import supabase
    from server import search_documents_by_content, local_document_search, generate_embedding
    
    print('✅ Modules imported successfully')
    
    # Test 1: Load chunks manually
    print('\n1. Loading chunks from database...')
    doc_chunks = supabase.table('document_chunks').select('*').execute()
    print(f'   Document chunks in DB: {len(doc_chunks.data)}')
    
    web_chunks = supabase.table('website_chunks').select('*').execute()
    print(f'   Website chunks in DB: {len(web_chunks.data)}')
    
    # Add to global chunks for testing
    for chunk in doc_chunks.data:
        chunk['source_type'] = 'document'
        if not chunk.get('filename'):
            chunk['filename'] = 'Test Document'
    DOCUMENT_CHUNKS.extend(doc_chunks.data)
    
    for chunk in web_chunks.data:
        chunk['source_type'] = 'website'
    DOCUMENT_CHUNKS.extend(web_chunks.data)
    
    print(f'   ✅ Total chunks in memory: {len(DOCUMENT_CHUNKS)}')
    
    # Test 2: Direct search
    print('\n2. Testing direct content search...')
    results = search_documents_by_content('Indian Railway', limit=5)
    print(f'   Direct search results: {len(results)}')
    
    if results:
        print('   ✅ Direct content search working!')
        for i, result in enumerate(results[:2]):
            filename = result.get('filename', 'Unknown')
            text_preview = result.get('text', '')[:80] + '...'
            print(f'     {i+1}. {filename}: {text_preview}')
    else:
        print('   ❌ No direct search results found')
        
    # Test 3: Vector search
    print('\n3. Testing vector search...')
    try:
        query_embedding = generate_embedding('Indian Railway')
        vector_results = local_document_search(
            query_embedding=query_embedding,
            query_text='Indian Railway',
            top_k=5,
            min_threshold=0.01
        )
        print(f'   Vector search results: {len(vector_results)}')
        
        if vector_results:
            print('   ✅ Vector search working!')
            for i, result in enumerate(vector_results[:2]):
                similarity = result.get('similarity', 0)
                filename = result.get('filename', 'Unknown')
                print(f'     {i+1}. {filename} (similarity: {similarity:.3f})')
        else:
            print('   ⚠️  No vector search results (may need embeddings)')
            
    except Exception as e:
        print(f'   ❌ Vector search failed: {str(e)}')
    
    print('\n' + '=' * 50)
    print('🎉 Test Results Summary:')
    print(f'   📊 Chunks loaded: {len(DOCUMENT_CHUNKS)}')
    print(f'   🔍 Direct search: {"✅ Working" if results else "❌ Failed"}')
    print(f'   🧠 Vector search: {"✅ Working" if "vector_results" in locals() and vector_results else "⚠️  Needs embeddings"}')
    
    if results:
        print('\n🚀 Document search is working!')
        print('   Restart your server to apply the startup fixes:')
        print('   python server.py')
    else:
        print('\n❌ Document search still has issues')
        print('   Check Supabase connection and data')
        
except Exception as e:
    print(f'❌ Test failed with error: {str(e)}')
    import traceback
    traceback.print_exc() 