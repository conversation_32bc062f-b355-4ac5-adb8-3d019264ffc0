# RailGPT Backend Dependencies
# Core FastAPI and server dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
python-dotenv==1.0.0
pydantic==2.5.0

# Database and storage
supabase==2.0.0
psycopg2-binary==2.9.9
sqlalchemy==2.0.23

# AI and ML dependencies
google-generativeai==0.3.2
openai==1.3.7
groq==0.4.1
numpy==1.24.3
scikit-learn==1.3.2

# Document processing
PyMuPDF==1.23.8
python-docx==1.1.0
pypdf2==3.0.1
pdfplumber==0.10.3
pytesseract==0.3.10
Pillow==10.1.0

# Web scraping and content extraction
requests==2.31.0
beautifulsoup4==4.12.2
trafilatura==1.6.4
selenium==4.15.2
lxml==4.9.3
html2text==2020.1.16

# Text processing and NLP
nltk==3.8.1
spacy==3.7.2
transformers==4.35.2
sentence-transformers==2.2.2

# Vector database and search
pgvector==0.2.4
faiss-cpu==1.7.4
chromadb==0.4.18

# HTTP and API clients
httpx==0.25.2
aiohttp==3.9.1
aiofiles==23.2.1

# Email and notifications
smtplib2==0.2.1
email-validator==2.1.0

# Logging and monitoring
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Security and authentication
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
cryptography==41.0.7

# Data validation and serialization
marshmallow==3.20.1
jsonschema==4.20.0

# Async and concurrency
asyncio==3.4.3
asyncpg==0.29.0
aioredis==2.0.1

# File handling and utilities
pathlib2==2.3.7
mimetypes-extended==1.0.0
magic==0.4.27

# Image processing and OCR
opencv-python==4.8.1.78
easyocr==1.7.0
pdf2image==1.16.3

# Configuration and environment
configparser==6.0.0
toml==0.10.2
pyyaml==6.0.1

# Testing (for development)
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Performance and optimization
cachetools==5.3.2
redis==5.0.1
celery==5.3.4

# Data processing
pandas==2.1.3
openpyxl==3.1.2

# Timezone handling
pytz==2023.3
python-dateutil==2.8.2

# URL and web utilities
validators==0.22.0
urllib3==2.1.0

# JSON and data formats
orjson==3.9.10
msgpack==1.0.7

# Compression and archives
zipfile36==0.1.3
tarfile==0.1.0

# System and OS utilities
psutil==5.9.6
platform==1.0.8

# Development and debugging
rich==13.7.0
click==8.1.7

# Additional AI model support
anthropic==0.7.8
cohere==4.37

# Vector similarity and embeddings
annoy==1.17.3
hnswlib==0.7.0

# Content type detection
python-magic==0.4.27
filetype==1.2.0

# Rate limiting and throttling
slowapi==0.1.9
limits==3.6.0

# Background tasks
rq==1.15.1
dramatiq==1.14.2

# Metrics and health checks
prometheus-client==0.19.0
healthcheck==1.3.3

# CORS and middleware
fastapi-cors==0.0.6
starlette==0.27.0

# WebSocket support (for real-time features)
websockets==12.0

# Additional utilities
tqdm==4.66.1
colorama==0.4.6
tabulate==0.9.0
