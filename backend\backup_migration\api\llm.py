from typing import List, Dict, Any, Optional
import os
import json
import uuid
import requests

# Default dummy response for testing
DEFAULT_RESPONSE = {
    "answer": "This is a response from the RailGPT system. In a real implementation, this would be generated by an LLM.",
    "document_sources": [
        {
            "id": str(uuid.uuid4()),
            "document_id": "f7c13b5e-9c57-4705-8887-a2e9d92e9c9c",
            "filename": "Railway Signaling Manual.pdf",  # This will be replaced with actual document name
            "page_numbers": [12, 13],
            "chunk_indices": [4, 5],
            "relevance_score": 0.92
        },
        {
            "id": str(uuid.uuid4()),
            "document_id": "a1b2c3d4-5e6f-7g8h-9i0j-1k2l3m4n5o6p",
            "filename": "Track Maintenance Guidelines.pdf",  # This will be replaced with actual document name
            "page_numbers": [45],
            "chunk_indices": [18],
            "relevance_score": 0.85
        }
    ],
    "website_sources": []
}

async def generate_response(query: str, history: List[Dict[str, str]] = []) -> Dict[str, Any]:
    """
    Generate a response using an LLM service.

    Args:
        query: The user's query
        history: Chat history as a list of message objects

    Returns:
        Dictionary containing the response and source information
    """
    try:
        # Check if we have an API key for Gemini
        gemini_api_key = os.environ.get("GEMINI_API_KEY")
        if gemini_api_key:
            return await generate_gemini_response(query, history)

        # Check if we have an API key for OpenAI
        openai_api_key = os.environ.get("OPENAI_API_KEY")
        if openai_api_key:
            return await generate_openai_response(query, history)

        # No API keys available, return default response
        return DEFAULT_RESPONSE
    except Exception as e:
        print(f"Error generating response: {e}")
        return {
            "answer": f"I'm sorry, I encountered an error while processing your request. {str(e)}",
            "document_sources": [],
            "website_sources": []
        }

async def generate_gemini_response(query: str, history: List[Dict[str, str]]) -> Dict[str, Any]:
    """
    Generate a response using Google's Gemini API.

    In a real implementation, this would make API calls to Google's Gemini service.
    For this demo, we'll return test data with realistic document sources.
    """
    # In a real implementation, this would make a request to the Gemini API
    # For now, return a sample response with proper document sources

    # Import here to prevent errors if the library is not installed
    try:
        import google.generativeai as genai
        from google.generativeai.types import HarmCategory, HarmBlockThreshold

        # Configure the API
        genai.configure(api_key=os.environ.get("GEMINI_API_KEY"))

        # Format conversation history
        formatted_history = []
        for msg in history:
            role = "user" if msg["role"] == "user" else "model"
            formatted_history.append({"role": role, "parts": [msg["content"]]})

        # Create a chat session
        model = genai.GenerativeModel("gemini-1.0-pro")
        chat = model.start_chat(history=formatted_history)

        # Generate response
        response = chat.send_message(query)
        answer = response.text

        # For demo purposes, add sample document sources with proper information
        # In a real implementation, these would come from a retrieval system
        return {
            "answer": answer,
            "document_sources": [
                {
                    "id": str(uuid.uuid4()),
                    "document_id": "f7c13b5e-9c57-4705-8887-a2e9d92e9c9c",
                    "filename": "Railway Signaling Manual.pdf",
                    "page_numbers": [12, 13],
                    "chunk_indices": [4, 5],
                    "relevance_score": 0.92
                },
                {
                    "id": str(uuid.uuid4()),
                    "document_id": "a1b2c3d4-5e6f-7g8h-9i0j-1k2l3m4n5o6p",
                    "filename": "Track Maintenance Guidelines.pdf",
                    "page_numbers": [45],
                    "chunk_indices": [18],
                    "relevance_score": 0.85
                }
            ],
            "website_sources": []
        }
    except Exception as e:
        print(f"Error with Gemini API: {e}")
        return DEFAULT_RESPONSE

async def generate_openai_response(query: str, history: List[Dict[str, str]]) -> Dict[str, Any]:
    """
    Generate a response using OpenAI's API.

    In a real implementation, this would make API calls to OpenAI's service.
    For this demo, we'll return test data with realistic document sources.
    """
    # Import here to prevent errors if the library is not installed
    try:
        import openai

        # Configure the API
        openai.api_key = os.environ.get("OPENAI_API_KEY")

        # Format conversation history
        formatted_history = []
        for msg in history:
            formatted_history.append({"role": msg["role"], "content": msg["content"]})

        # Add the user's query
        formatted_history.append({"role": "user", "content": query})

        # Generate response
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=formatted_history
        )

        answer = response.choices[0].message.content

        # For demo purposes, add sample document sources with proper information
        # In a real implementation, these would come from a retrieval system
        return {
            "answer": answer,
            "document_sources": [
                {
                    "id": str(uuid.uuid4()),
                    "document_id": "f7c13b5e-9c57-4705-8887-a2e9d92e9c9c",
                    "filename": "Railway Signaling Manual.pdf",
                    "page_numbers": [12, 13],
                    "chunk_indices": [4, 5],
                    "relevance_score": 0.92
                },
                {
                    "id": str(uuid.uuid4()),
                    "document_id": "a1b2c3d4-5e6f-7g8h-9i0j-1k2l3m4n5o6p",
                    "filename": "Track Maintenance Guidelines.pdf",
                    "page_numbers": [45],
                    "chunk_indices": [18],
                    "relevance_score": 0.85
                }
            ],
            "website_sources": []
        }
    except Exception as e:
        print(f"Error with OpenAI API: {e}")
        return DEFAULT_RESPONSE
