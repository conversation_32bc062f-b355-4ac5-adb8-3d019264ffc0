from typing import Dict, List, Any
import os

# NOTE: These are imported from backend.server but imported lazily to avoid circular
# import issues when backend.server imports this module.
from .server import generate_llm_answer, format_visual_content_for_frontend, logger  # type: ignore


def generate_clean_answer_with_sources(query: str,
                                       chunks: List[Dict[str, Any]],
                                       source_type: str,
                                       model_id: str = "gemini-2.0-flash",
                                       extract_format: str = "paragraph"):
    """Canonical implementation ensuring that only chunks actually provided to the LLM become sources.

    Returns
    -------
    answer : str
        The generated answer.
    clean_sources : List[Dict[str, Any]]
        List of source metadata objects actually used.
    visual_content_found : bool
        Whether any non-text (table/image) content was encountered.
    visual_content_types : List[str]
        List of visual content types encountered.
    """

    try:
        logger.info(f"🔍 [FINAL] Processing {len(chunks)} raw {source_type} chunks → query: '{query[:80]}…'")

        # 📏 Heuristic thresholds – tuned for higher recall
        #   • Documents: keep anything ≥ 0.25 similarity
        #   • Websites : keep anything ≥ 0.15 similarity
        threshold = 0.25 if source_type == "document" else 0.15

        # Allow shorter snippets down to 20 chars (some tables/images have concise captions)
        min_len = 20

        filtered: List[Dict[str, Any]] = [
            c for c in chunks
            if c.get("similarity", 0) >= threshold and len(c.get("text", "")) >= min_len
        ]
        if not filtered:
            return (f"I couldn't find relevant information in the {source_type} content.", [], False, [])

        # 2️⃣  Keep top-K by similarity
        filtered.sort(key=lambda c: c.get("similarity", 0), reverse=True)
        k = 4 if source_type == "document" else 3
        selected = filtered[:k]

        # 3️⃣  Build LLM context and gather used sources
        context_parts: List[str] = []
        used_sources: Dict[str, Dict[str, Any]] = {}
        visual_found = False
        visual_types: List[str] = []

        for ch in selected:
            sim = ch.get("similarity", 0)
            text = ch.get("text", "")
            if source_type == "document":
                meta = ch.get("metadata", {})
                filename = ch.get("filename") or ch.get("title") or "Unknown.pdf"
                page = ch.get("page") or ch.get("page_number", 1)
                ctype = meta.get("content_type", "text")
                if ctype != "text":
                    visual_found = True
                    if ctype not in visual_types:
                        visual_types.append(ctype)
                context_parts.append(f"From '{filename}' (page {page}, sim {sim:.2f}):\n{text}\n")
                key = f"{filename}_{ctype}_{page}" if ctype != "text" else filename
                entry = used_sources.setdefault(key, {
                    "source_type": "document",
                    "filename": filename,
                    "name": os.path.basename(filename),
                    "pages": [],
                    "content_type": ctype,
                    "visual_content": format_visual_content_for_frontend(meta, ctype) if ctype != "text" else None,
                })
                if page not in entry["pages"]:
                    entry["pages"].append(page)
            else:
                url = ch.get("url") or "Unknown website"
                context_parts.append(f"From '{url}' (sim {sim:.2f}):\n{text}\n")
                used_sources.setdefault(url, {
                    "source_type": "website",
                    "url": url,
                    "title": ch.get("title", "")
                })

        if not context_parts:
            return (f"I couldn't extract meaningful information from the {source_type} content.", [], visual_found, visual_types)

        # 4️⃣  Generate the answer from selected chunks only
        answer = generate_llm_answer(query=query,
                                      similar_chunks=selected,
                                      model_id=model_id,
                                      extract_format=extract_format)
        if not answer or not answer.strip():
            # If LLM didn't generate proper answer, force use of context
            logger.warning(f"LLM returned empty/invalid answer for {source_type}, forcing context usage")
            
            # Build a summary from the context instead of failing
            if context_parts:
                context_summary = "

".join(context_parts[:2])  # Use first 2 context parts
                
                if source_type == "document":
                    forced_answer = f"Based on the railway documents found, here is the relevant information:

{context_summary}"
                else:
                    forced_answer = f"Based on the railway websites found, here is the relevant information:

{context_summary}"
                
                # Limit length to avoid overwhelming response
                if len(forced_answer) > 1000:
                    forced_answer = forced_answer[:1000] + "...[content truncated for length]"
                
                return forced_answer, clean_sources, visual_found, visual_types
            else:
                return (f"I couldn't generate an answer from the {source_type} content.", [], visual_found, visual_types)

        # 5️⃣  Convert used_sources → clean_sources list
        clean_sources: List[Dict[str, Any]] = []
        for src in used_sources.values():
            if src["source_type"] == "document":
                pages_sorted = sorted(src["pages"])
                page_ref = f"Page {pages_sorted[0]}" if len(pages_sorted) == 1 else f"Pages {', '.join(map(str, pages_sorted))}"
                clean_sources.append({
                    "source_type": "document",
                    "filename": src["filename"],
                    "name": src["name"],
                    "page": pages_sorted[0],
                    "pages": pages_sorted,
                    "link": f"/viewer?file={src['filename']}&page={pages_sorted[0]}",
                    "display_text": f"{src['name']} – {page_ref}",
                    "content_type": src.get("content_type", "text"),
                    "visual_content": src.get("visual_content"),
                })
            else:
                clean_sources.append({
                    "source_type": "website",
                    "url": src["url"],
                    "title": src.get("title", src["url"]),
                    "display_text": src.get("title", src["url"])
                })

        return answer, clean_sources, visual_found, visual_types

    except Exception as exc:
        logger.error(f"❌ [FINAL] generate_clean_answer_with_sources error: {exc}")
        return (f"I encountered an error while processing the {source_type} content.", [], False, []) 