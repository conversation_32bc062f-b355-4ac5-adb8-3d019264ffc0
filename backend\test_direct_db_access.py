#!/usr/bin/env python3
"""
Test direct database access to verify categories are accessible
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from supabase_client import SupabaseClient
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_direct_access():
    """Test direct access to category tables"""
    
    print("🔍 Testing Direct Database Access")
    print("=" * 60)
    
    client = SupabaseClient()
    
    # Test document_categories
    print("\n📋 Testing document_categories access:")
    print("-" * 40)
    try:
        # Test basic select
        result = client.supabase.table("document_categories").select("id,name,type").limit(5).execute()
        print(f"✅ Basic select successful: {len(result.data)} records")
        for record in result.data:
            print(f"   - {record.get('name')} ({record.get('type')})")
        
        # Test with is_active filter
        result2 = client.supabase.table("document_categories").select("*").eq('is_active', True).limit(3).execute()
        print(f"✅ Active filter successful: {len(result2.data)} records")
        
        # Test count
        result3 = client.supabase.table("document_categories").select("*", count="exact").execute()
        print(f"✅ Total count: {result3.count if hasattr(result3, 'count') else len(result3.data)} records")
        
    except Exception as e:
        print(f"❌ Error accessing document_categories: {str(e)}")
    
    # Test website_categories
    print("\n📋 Testing website_categories access:")
    print("-" * 40)
    try:
        # Test basic select
        result = client.supabase.table("website_categories").select("id,name,type").limit(5).execute()
        print(f"✅ Basic select successful: {len(result.data)} records")
        for record in result.data:
            print(f"   - {record.get('name')} ({record.get('type')})")
        
        # Test with is_active filter
        result2 = client.supabase.table("website_categories").select("*").eq('is_active', True).limit(3).execute()
        print(f"✅ Active filter successful: {len(result2.data)} records")
        
        # Test count
        result3 = client.supabase.table("website_categories").select("*", count="exact").execute()
        print(f"✅ Total count: {result3.count if hasattr(result3, 'count') else len(result3.data)} records")
        
    except Exception as e:
        print(f"❌ Error accessing website_categories: {str(e)}")

def test_get_categories_function():
    """Test the actual get_categories_from_db function"""
    
    print("\n🧪 Testing get_categories_from_db Function")
    print("=" * 60)
    
    # Import the function
    try:
        from category_management import get_categories_from_db
        
        # Test document categories
        print("\n📋 Testing document_categories function:")
        print("-" * 40)
        import asyncio
        
        async def test_doc_categories():
            try:
                categories = await get_categories_from_db('document_categories')
                print(f"✅ Function returned: {len(categories) if categories else 0} categories")
                if categories:
                    for i, cat in enumerate(categories[:3]):
                        print(f"   {i+1}. {cat.get('name')} ({cat.get('type')})")
                        print(f"      Full path: {cat.get('full_path', 'N/A')}")
                        print(f"      Level: {cat.get('level', 'N/A')}")
                return categories
            except Exception as e:
                print(f"❌ Function error: {str(e)}")
                return None
        
        # Test website categories
        async def test_web_categories():
            try:
                categories = await get_categories_from_db('website_categories')
                print(f"✅ Function returned: {len(categories) if categories else 0} categories")
                if categories:
                    for i, cat in enumerate(categories[:3]):
                        print(f"   {i+1}. {cat.get('name')} ({cat.get('type')})")
                        print(f"      Full path: {cat.get('full_path', 'N/A')}")
                        print(f"      Level: {cat.get('level', 'N/A')}")
                return categories
            except Exception as e:
                print(f"❌ Function error: {str(e)}")
                return None
        
        # Run tests
        doc_result = asyncio.run(test_doc_categories())
        
        print("\n📋 Testing website_categories function:")
        print("-" * 40)
        web_result = asyncio.run(test_web_categories())
        
        return doc_result, web_result
        
    except Exception as e:
        print(f"❌ Error importing function: {str(e)}")
        return None, None

if __name__ == "__main__":
    test_direct_access()
    test_get_categories_function()
