import {
  truncateText,
  formatPageRange,
  formatDate,
  formatFileSize
} from '../formatUtils';

describe('formatUtils', () => {
  describe('truncateText', () => {
    it('should truncate text longer than maxLength', () => {
      const longText = 'This is a very long text that needs to be truncated';
      expect(truncateText(longText, 10)).toBe('This is a...');
    });

    it('should not truncate text shorter than maxLength', () => {
      const shortText = 'Short text';
      expect(truncateText(shortText, 20)).toBe(shortText);
    });

    it('should handle empty strings', () => {
      expect(truncateText('', 10)).toBe('');
    });

    it('should handle null or undefined', () => {
      expect(truncateText(null as unknown as string, 10)).toBe(null);
      expect(truncateText(undefined as unknown as string, 10)).toBe(undefined);
    });
  });

  describe('formatPageRange', () => {
    it('should format a small number of pages individually', () => {
      expect(formatPageRange([1, 2, 3])).toBe('Pages 1, 2, 3');
    });

    it('should format many pages as a range', () => {
      expect(formatPageRange([1, 2, 3, 4, 5, 6])).toBe('Pages 1-6');
    });

    it('should sort page numbers before formatting', () => {
      expect(formatPageRange([3, 1, 2])).toBe('Pages 1, 2, 3');
    });

    it('should handle empty arrays', () => {
      expect(formatPageRange([])).toBe('');
    });

    it('should handle undefined or null', () => {
      expect(formatPageRange(undefined)).toBe('');
      expect(formatPageRange(null as unknown as number[])).toBe('');
    });
  });

  describe('formatDate', () => {
    it('should format a date string', () => {
      const result = formatDate('2023-01-15T12:30:45Z');
      expect(result).toContain('Jan 15, 2023');
    });

    it('should handle Date objects', () => {
      const date = new Date(2023, 0, 15, 12, 30);
      const result = formatDate(date);
      expect(result).toContain('Jan 15, 2023');
    });

    it('should handle timestamps', () => {
      const timestamp = new Date(2023, 0, 15).getTime();
      const result = formatDate(timestamp);
      expect(result).toContain('Jan 15, 2023');
    });

    it('should handle invalid dates gracefully', () => {
      expect(formatDate('not-a-date')).toBe('not-a-date');
    });

    it('should handle empty input', () => {
      expect(formatDate('')).toBe('');
    });
  });

  describe('formatFileSize', () => {
    it('should format bytes', () => {
      expect(formatFileSize(500)).toBe('500.0 B');
    });

    it('should format kilobytes', () => {
      expect(formatFileSize(1500)).toBe('1.5 KB');
    });

    it('should format megabytes', () => {
      expect(formatFileSize(1500000)).toBe('1.4 MB');
    });

    it('should format gigabytes', () => {
      expect(formatFileSize(1500000000)).toBe('1.4 GB');
    });

    it('should handle zero', () => {
      expect(formatFileSize(0)).toBe('0.0 B');
    });

    it('should handle negative numbers', () => {
      expect(formatFileSize(-100)).toBe('0 B');
    });

    it('should handle NaN', () => {
      expect(formatFileSize(NaN)).toBe('0 B');
    });
  });
});
