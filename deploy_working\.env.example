# RailGPT Environment Configuration
# Copy this file to .env and fill in your actual values

# ===== REQUIRED API KEYS =====
# Get your Gemini API key from: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_actual_gemini_api_key_here

# Get your Supabase service role key from: https://supabase.com/dashboard/project/rkllidjktazafeinezgo/settings/api
SUPABASE_KEY=your_actual_supabase_service_key_here

# ===== OPTIONAL API KEYS =====
# OpenAI API key (optional) - Get from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Groq API key (optional) - Get from: https://console.groq.com/keys
GROQ_API_KEY=your_groq_api_key_here

# ===== SUPABASE CONFIGURATION =====
# Your Supabase project URL
SUPABASE_URL=https://rkllidjktazafeinezgo.supabase.co

# Supabase anon key (for frontend)
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# ===== GCP DEPLOYMENT CONFIGURATION =====
# Your Google Cloud Project ID (will be auto-generated if not set)
PROJECT_ID=railchatbot-cb553

# GCP Region for deployment
REGION=us-central1

# Custom domain (optional)
DOMAIN=

# ===== APPLICATION CONFIGURATION =====
# Backend API URL (for production deployment)
REACT_APP_API_URL=https://your-backend-url.run.app

# Frontend URL (for CORS configuration)
FRONTEND_URL=https://your-frontend-url.web.app

# ===== DEVELOPMENT CONFIGURATION =====
# Local development URLs
REACT_APP_API_URL_DEV=http://localhost:8000
FRONTEND_URL_DEV=http://localhost:3000

# ===== SECURITY CONFIGURATION =====
# JWT Secret for authentication (generate a random string)
JWT_SECRET=your_jwt_secret_here

# Session secret for cookies
SESSION_SECRET=your_session_secret_here

# ===== EMAIL CONFIGURATION (for feedback) =====
# SMTP configuration for sending feedback emails
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password_here

# Feedback notification emails (comma-separated)
FEEDBACK_EMAILS=<EMAIL>,<EMAIL>

# ===== MONITORING CONFIGURATION =====
# Enable application monitoring
ENABLE_MONITORING=true

# Log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# ===== PERFORMANCE CONFIGURATION =====
# Maximum file upload size (in MB)
MAX_UPLOAD_SIZE=200

# Maximum number of concurrent requests
MAX_CONCURRENT_REQUESTS=100

# Request timeout (in seconds)
REQUEST_TIMEOUT=300

# ===== FEATURE FLAGS =====
# Enable visual content extraction
ENABLE_VISUAL_EXTRACTION=true

# Enable website scraping
ENABLE_WEBSITE_SCRAPING=true

# Enable LLM fallback when no sources found
ENABLE_LLM_FALLBACK=true

# Enable hybrid search (vector + text)
ENABLE_HYBRID_SEARCH=true

# ===== CACHE CONFIGURATION =====
# Redis URL for caching (optional)
REDIS_URL=

# Cache TTL in seconds
CACHE_TTL=3600

# ===== DATABASE CONFIGURATION =====
# Database connection pool size
DB_POOL_SIZE=10

# Database connection timeout
DB_TIMEOUT=30

# ===== RATE LIMITING =====
# Requests per minute per IP
RATE_LIMIT_RPM=60

# Burst limit for rate limiting
RATE_LIMIT_BURST=10

# ===== CORS CONFIGURATION =====
# Allowed origins for CORS (comma-separated)
CORS_ORIGINS=http://localhost:3000,https://your-domain.com

# ===== BACKUP CONFIGURATION =====
# Enable automatic backups
ENABLE_BACKUPS=true

# Backup frequency (daily, weekly, monthly)
BACKUP_FREQUENCY=daily

# Backup retention period (in days)
BACKUP_RETENTION=30
