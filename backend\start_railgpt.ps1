# RailGPT Server Startup Script
# PowerShell script to start the fixed RailGPT server

Write-Host "🚀 Starting RailGPT Server with Fixes" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

# Check if we're in the correct directory
if (-not (Test-Path "server.py")) {
    Write-Host "❌ Error: server.py not found. Please run this script from the backend directory." -ForegroundColor Red
    exit 1
}

# Check if Python is installed
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Error: Python not found. Please install Python 3.8 or higher." -ForegroundColor Red
    exit 1
}

# Check if virtual environment exists and activate it
if (Test-Path "venv\Scripts\Activate.ps1") {
    Write-Host "🔧 Activating virtual environment..." -ForegroundColor Yellow
    & venv\Scripts\Activate.ps1
} elseif (Test-Path "venv\bin\activate") {
    Write-Host "🔧 Activating virtual environment (Linux/Mac style)..." -ForegroundColor Yellow
    . venv/bin/activate
} else {
    Write-Host "⚠️ Warning: No virtual environment found. Using system Python." -ForegroundColor Yellow
}

# Check if required packages are installed
Write-Host "🔍 Checking dependencies..." -ForegroundColor Yellow

$requiredPackages = @("fastapi", "uvicorn", "supabase", "openai")
$missingPackages = @()

foreach ($package in $requiredPackages) {
    try {
        python -m pip show $package | Out-Null
        Write-Host "  ✅ $package: installed" -ForegroundColor Green
    } catch {
        Write-Host "  ❌ $package: missing" -ForegroundColor Red
        $missingPackages += $package
    }
}

if ($missingPackages.Count -gt 0) {
    Write-Host "⚠️ Installing missing packages..." -ForegroundColor Yellow
    foreach ($package in $missingPackages) {
        Write-Host "  📦 Installing $package..." -ForegroundColor Yellow
        python -m pip install $package
    }
}

# Set environment variables if they don't exist
if (-not $env:SUPABASE_URL) {
    Write-Host "⚠️ Warning: SUPABASE_URL not set. Please set your environment variables." -ForegroundColor Yellow
}

if (-not $env:SUPABASE_KEY) {
    Write-Host "⚠️ Warning: SUPABASE_KEY not set. Please set your environment variables." -ForegroundColor Yellow
}

# Create logs directory if it doesn't exist
if (-not (Test-Path "logs")) {
    New-Item -ItemType Directory -Path "logs" | Out-Null
    Write-Host "📁 Created logs directory" -ForegroundColor Green
}

# Start the server
Write-Host "`n🚀 Starting RailGPT server..." -ForegroundColor Green
Write-Host "Server will be available at: http://localhost:8000" -ForegroundColor Cyan
Write-Host "API documentation: http://localhost:8000/docs" -ForegroundColor Cyan
Write-Host "Debug endpoints:" -ForegroundColor Cyan
Write-Host "  - Health check: http://localhost:8000/api/health" -ForegroundColor Cyan
Write-Host "  - Search test: http://localhost:8000/api/debug/search-test" -ForegroundColor Cyan
Write-Host "  - Vector status: http://localhost:8000/api/debug/vector-status" -ForegroundColor Cyan
Write-Host "`nPress Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host "====================================`n" -ForegroundColor Green

try {
    # Start the server with uvicorn
    python -m uvicorn server:app --host 0.0.0.0 --port 8000 --reload --log-level info
} catch {
    Write-Host "`n❌ Error starting server: $_" -ForegroundColor Red
    exit 1
} finally {
    Write-Host "`n🛑 Server stopped." -ForegroundColor Yellow
} 