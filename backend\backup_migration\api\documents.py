from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import uuid
import os
from supabase import create_client, Client

from .config import get_supabase_client

router = APIRouter()

class Document(BaseModel):
    id: str
    filename: str
    display_name: Optional[str] = None
    file_path: str
    file_type: Optional[str] = None
    file_size: Optional[int] = None
    category: Optional[str] = None
    sub_category: Optional[str] = None
    created_at: Optional[str] = None

class DocumentChunk(BaseModel):
    id: str
    document_id: str
    chunk_index: int
    page_number: Optional[int] = None
    text: str
    metadata: Optional[Dict[str, Any]] = None

@router.get("/documents", response_model=List[Document])
async def get_documents(supabase: Client = Depends(get_supabase_client)):
    try:
        response = supabase.table("documents").select("*").order("created_at", desc=True).execute()
        return response.data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching documents: {str(e)}")

@router.get("/documents/{document_id}", response_model=Document)
async def get_document(document_id: str, supabase: Client = Depends(get_supabase_client)):
    try:
        response = supabase.table("documents").select("*").eq("id", document_id).execute()

        if not response.data or len(response.data) == 0:
            raise HTTPException(status_code=404, detail="Document not found")

        return response.data[0]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching document: {str(e)}")

@router.get("/documents/{document_id}/chunks", response_model=List[DocumentChunk])
async def get_document_chunks(document_id: str, supabase: Client = Depends(get_supabase_client)):
    try:
        response = supabase.table("document_chunks").select("*").eq("document_id", document_id).order("chunk_index").execute()
        return response.data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching document chunks: {str(e)}")

async def get_document_by_id(document_id: str, supabase: Client) -> Optional[Dict[str, Any]]:
    """Helper function to get document details by ID"""
    try:
        response = supabase.table("documents").select("*").eq("id", document_id).execute()

        if not response.data or len(response.data) == 0:
            return None

        return response.data[0]
    except Exception as e:
        print(f"Error in get_document_by_id: {e}")
        return None

@router.post("/documents/upload")
async def upload_document(
    file: UploadFile = File(...),
    display_name: Optional[str] = Form(None),
    category: Optional[str] = Form(None),
    sub_category: Optional[str] = Form(None),
    supabase: Client = Depends(get_supabase_client)
):
    try:
        # Generate a unique filename to prevent collisions
        original_filename = file.filename
        file_extension = original_filename.split(".")[-1] if "." in original_filename else ""
        unique_filename = f"{uuid.uuid4()}.{file_extension}"

        # Read file content
        file_content = await file.read()
        file_size = len(file_content)

        # Upload to Supabase storage
        file_path = f"documents/{unique_filename}"
        storage_response = supabase.storage.from_("documents").upload(
            path=unique_filename,
            file=file_content,
            file_options={"content-type": file.content_type}
        )

        # Get public URL
        file_url = supabase.storage.from_("documents").get_public_url(unique_filename)

        # Create document record
        document_id = str(uuid.uuid4())
        document = {
            "id": document_id,
            "filename": original_filename,
            "display_name": display_name or original_filename,
            "file_path": file_path,
            "file_type": file.content_type,
            "file_size": file_size,
            "category": category,
            "sub_category": sub_category,
            "status": "active"
        }

        response = supabase.table("documents").insert(document).execute()

        # Process document for chunks (in a real implementation, this would be a background job)
        # For now, just return success
        return {
            "success": True,
            "document_id": document_id,
            "filename": original_filename,
            "file_url": file_url
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error uploading document: {str(e)}")
