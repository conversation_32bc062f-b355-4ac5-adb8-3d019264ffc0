#!/usr/bin/env python3
"""
Check existing data in category tables to understand the schema
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from supabase_client import SupabaseClient
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_existing_data():
    """Check existing data in both tables"""
    
    print("🔍 Checking Existing Data in Category Tables")
    print("=" * 60)
    
    client = SupabaseClient()
    
    # Check document_categories
    print("\n📋 Document Categories (first 5 records):")
    print("-" * 40)
    try:
        result = client.supabase.table("document_categories").select("*").limit(5).execute()
        if result.data:
            for i, record in enumerate(result.data, 1):
                print(f"{i}. {record.get('name', 'N/A')} (type: {record.get('type', 'N/A')})")
                print(f"   ID: {record.get('id', 'N/A')}")
                print(f"   Description: {record.get('description', 'N/A')[:50]}...")
                print()
        else:
            print("No data found")
    except Exception as e:
        print(f"Error: {str(e)}")
    
    # Check website_categories
    print("\n📋 Website Categories (first 5 records):")
    print("-" * 40)
    try:
        result = client.supabase.table("website_categories").select("*").limit(5).execute()
        if result.data:
            for i, record in enumerate(result.data, 1):
                print(f"{i}. {record.get('name', 'N/A')} (type: {record.get('type', 'N/A')})")
                print(f"   ID: {record.get('id', 'N/A')}")
                print(f"   Description: {record.get('description', 'N/A')[:50] if record.get('description') else 'N/A'}...")
                print()
        else:
            print("No data found")
    except Exception as e:
        print(f"Error: {str(e)}")
    
    # Check unique types in website_categories
    print("\n📊 Unique Types in Website Categories:")
    print("-" * 40)
    try:
        result = client.supabase.table("website_categories").select("type").execute()
        if result.data:
            types = set(record.get('type') for record in result.data if record.get('type'))
            for type_val in sorted(types):
                print(f"   - {type_val}")
        else:
            print("No data found")
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    check_existing_data()
