#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_search_functions():
    base_url = "http://localhost:8000"
    
    print("🔍 Testing Search Functions Directly")
    print("=" * 50)
    
    # Test 1: Direct embedding generation
    print("\n1. Testing Embedding Generation")
    try:
        response = requests.post(f"{base_url}/api/generate-embedding", 
                               json={"text": "ACP"}, timeout=30)
        if response.status_code == 200:
            data = response.json()
            embedding = data.get("embedding", [])
            print(f"✅ Embedding generated: {len(embedding)} dimensions")
        else:
            print(f"❌ Embedding generation failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Embedding generation error: {str(e)}")
    
    # Test 2: Test the actual search endpoint with ACP
    print("\n2. Testing Search Endpoint with ACP")
    try:
        # Try the query endpoint but with debug info
        query_data = {
            "query": "ACP Alarm Chain Pulling",
            "model": "gemini-2.0-flash",
            "fallback_enabled": False,  # Disable fallback to see real search results
            "use_hybrid_search": True
        }
        response = requests.post(f"{base_url}/api/query", json=query_data, timeout=60)
        if response.status_code == 200:
            data = response.json()
            
            doc_sources = data.get("document_sources", [])
            print(f"✅ Query completed")
            print(f"   Document sources: {len(doc_sources)}")
            
            if doc_sources:
                for i, source in enumerate(doc_sources[:3]):
                    print(f"\n   📄 Source {i+1}:")
                    print(f"      File: {source.get('filename', 'unknown')}")
                    print(f"      Page: {source.get('page_number', 'N/A')}")
                    print(f"      ID: {source.get('id', 'N/A')}")
                    content = source.get('content', '')
                    print(f"      Content length: {len(content)}")
                    if 'ACP' in content.upper():
                        print(f"      ✅ Contains ACP!")
                        acp_context = content[max(0, content.upper().find('ACP')-50):content.upper().find('ACP')+100]
                        print(f"      ACP context: ...{acp_context}...")
                    else:
                        print(f"      ❌ Does not contain ACP")
                        preview = content[:150] + "..." if len(content) > 150 else content
                        print(f"      Content preview: {preview}")
            else:
                print(f"   ❌ No document sources found!")
                print(f"   LLM fallback: {data.get('llm_fallback', 'unknown')}")
                
        else:
            print(f"❌ Query failed: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Query error: {str(e)}")
    
    # Test 3: Test with exact document ID we know exists
    print(f"\n3. Testing if document ID 1d9a4e11-2ae5-4c4b-9af4-07438c742181 can be found")
    try:
        # Try searching for VASP ENTERPRISES which we know is in the document
        query_data = {
            "query": "VASP ENTERPRISES",
            "model": "gemini-2.0-flash",
            "fallback_enabled": False,
            "use_hybrid_search": True
        }
        response = requests.post(f"{base_url}/api/query", json=query_data, timeout=60)
        if response.status_code == 200:
            data = response.json()
            doc_sources = data.get("document_sources", [])
            
            print(f"✅ VASP ENTERPRISES query completed")
            print(f"   Document sources: {len(doc_sources)}")
            
            acp_doc_found = False
            for source in doc_sources:
                if "ACP 110V" in source.get('filename', ''):
                    acp_doc_found = True
                    print(f"   ✅ Found ACP 110V.pdf in results!")
                    break
            
            if not acp_doc_found:
                print(f"   ❌ ACP 110V.pdf not in VASP ENTERPRISES search results")
                print(f"   Files found:")
                for source in doc_sources:
                    print(f"      - {source.get('filename', 'unknown')}")
        else:
            print(f"❌ VASP query failed: {response.status_code}")
    except Exception as e:
        print(f"❌ VASP query error: {str(e)}")
    
    # Test 4: Check if the chunks were actually stored
    print(f"\n4. Testing chunk storage")
    try:
        query_data = {
            "query": "Alarm Chain Pulling",  # Exact text from the document
            "model": "gemini-2.0-flash",
            "fallback_enabled": False,
            "use_hybrid_search": True
        }
        response = requests.post(f"{base_url}/api/query", json=query_data, timeout=60)
        if response.status_code == 200:
            data = response.json()
            doc_sources = data.get("document_sources", [])
            
            print(f"✅ 'Alarm Chain Pulling' query completed")
            print(f"   Document sources: {len(doc_sources)}")
            
            if doc_sources:
                for source in doc_sources:
                    content = source.get('content', '')
                    if 'Alarm Chain Pulling' in content:
                        print(f"   ✅ Found exact match for 'Alarm Chain Pulling'!")
                        print(f"   File: {source.get('filename', 'unknown')}")
                        break
                else:
                    print(f"   ❌ No exact match for 'Alarm Chain Pulling' found")
            else:
                print(f"   ❌ No results for 'Alarm Chain Pulling'")
        else:
            print(f"❌ Alarm Chain Pulling query failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Alarm Chain Pulling query error: {str(e)}")

if __name__ == "__main__":
    test_search_functions() 