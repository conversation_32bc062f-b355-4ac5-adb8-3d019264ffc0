"""
Visual Content Handler Module for RailGPT
Handles detection and processing of visual content in queries and chunks
"""

import logging
import re
from typing import Dict, List, Any, Tuple, Optional

logger = logging.getLogger(__name__)

class VisualContentHandler:
    """
    Handles visual content detection and processing
    """
    
    def __init__(self):
        # Visual query patterns
        self.visual_patterns = {
            'table': [
                r'\b(table|chart|data|statistics|comparison|breakdown)\b',
                r'\b(show me|display|list)\b.*\b(data|information|details)\b',
                r'\b(compare|versus|vs|difference)\b',
            ],
            'image': [
                r'\b(image|picture|photo|diagram|figure|illustration)\b',
                r'\b(show|display|view)\b.*\b(image|picture|diagram)\b',
                r'\b(visual|graphical)\b',
            ],
            'chart_diagram': [
                r'\b(chart|graph|plot|diagram|flowchart|schematic)\b',
                r'\b(pie chart|bar chart|line graph|flow chart)\b',
                r'\b(visualize|visualization)\b',
            ]
        }
        
        # Content type priorities
        self.content_priorities = {
            'table': 0.9,
            'chart_diagram': 0.8,
            'image': 0.7,
            'text': 1.0
        }
    
    def detect_visual_query(self, query: str) -> Dict[str, Any]:
        """
        Detect if a query is asking for visual content
        """
        query_lower = query.lower()
        visual_intents = []
        
        for content_type, patterns in self.visual_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower, re.IGNORECASE):
                    visual_intents.append(content_type)
                    break
        
        return {
            'has_visual_intent': len(visual_intents) > 0,
            'visual_types': list(set(visual_intents)),
            'priority_type': visual_intents[0] if visual_intents else None
        }
    
    def process_visual_chunks(
        self, 
        chunks: List[Dict[str, Any]], 
        visual_query_info: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Process and boost visual chunks based on query intent
        """
        if not visual_query_info.get('has_visual_intent'):
            return chunks
        
        priority_type = visual_query_info.get('priority_type')
        visual_types = visual_query_info.get('visual_types', [])
        
        for chunk in chunks:
            metadata = chunk.get('metadata', {})
            content_type = metadata.get('content_type', 'text')
            
            # Boost chunks that match visual intent
            if content_type in visual_types:
                boost_factor = 1.3 if content_type == priority_type else 1.1
                original_similarity = chunk.get('similarity', 0)
                chunk['similarity'] = min(original_similarity * boost_factor, 1.0)
        
        # Re-sort chunks by similarity
        chunks.sort(key=lambda x: x.get('similarity', 0), reverse=True)
        return chunks
    
    def process_visual_content_in_chunks(
        self, 
        chunks: List[Dict[str, Any]]
    ) -> Tuple[bool, List[str]]:
        """
        Process visual content in chunks and return metadata
        """
        visual_content_found = False
        visual_content_types = []
        
        for chunk in chunks:
            metadata = chunk.get('metadata', {})
            content_type = metadata.get('content_type', 'text')
            
            if content_type != 'text':
                visual_content_found = True
                if content_type not in visual_content_types:
                    visual_content_types.append(content_type)
        
        return visual_content_found, visual_content_types
    
    def format_visual_content_for_display(
        self, 
        metadata: Dict[str, Any], 
        content_type: str
    ) -> Dict[str, Any]:
        """
        Format visual content for frontend display
        """
        display_content = {
            'type': content_type,
            'display_type': self._get_display_type(content_type)
        }
        
        if content_type == 'table':
            display_content.update({
                'table_html': metadata.get('table_html'),
                'table_data': metadata.get('table_data'),
                'table_caption': metadata.get('table_caption')
            })
        elif content_type == 'image':
            display_content.update({
                'image_url': metadata.get('image_url'),
                'image_base64': metadata.get('image_base64'),
                'image_caption': metadata.get('image_caption'),
                'alt_text': metadata.get('alt_text')
            })
        elif content_type == 'chart_diagram':
            display_content.update({
                'chart_image': metadata.get('chart_image'),
                'chart_type': metadata.get('chart_type'),
                'chart_data': metadata.get('chart_data'),
                'chart_description': metadata.get('chart_description')
            })
        
        return display_content
    
    def _get_display_type(self, content_type: str) -> str:
        """Get display type for frontend"""
        mapping = {
            'text': 'text',
            'table': 'html_table',
            'image': 'image',
            'chart_diagram': 'image'
        }
        return mapping.get(content_type, 'text')
    
    def enhance_visual_chunks(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Enhance chunks with visual content processing
        """
        enhanced_chunks = []
        
        for chunk in chunks:
            enhanced_chunk = chunk.copy()
            metadata = chunk.get('metadata', {})
            content_type = metadata.get('content_type', 'text')
            
            # Add visual content formatting if needed
            if content_type != 'text':
                enhanced_chunk['visual_content'] = self.format_visual_content_for_display(
                    metadata, content_type
                )
                
                # Add content priority score
                enhanced_chunk['content_priority'] = self.content_priorities.get(content_type, 1.0)
            
            enhanced_chunks.append(enhanced_chunk)
        
        return enhanced_chunks

# Global visual handler instance
visual_handler = VisualContentHandler()

# Convenience functions for backward compatibility
def detect_visual_query(query: str) -> Dict[str, Any]:
    """Detect visual query patterns"""
    return visual_handler.detect_visual_query(query)

def process_visual_chunks(
    chunks: List[Dict[str, Any]], 
    visual_query_info: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """Process visual chunks with boosting"""
    return visual_handler.process_visual_chunks(chunks, visual_query_info)

def process_visual_content_in_chunks(
    chunks: List[Dict[str, Any]]
) -> Tuple[bool, List[str]]:
    """Process visual content in chunks"""
    return visual_handler.process_visual_content_in_chunks(chunks)

def format_visual_content_for_frontend(
    metadata: Dict[str, Any], 
    content_type: str
) -> Dict[str, Any]:
    """Format visual content for frontend"""
    return visual_handler.format_visual_content_for_display(metadata, content_type) 