"""
Answer Generator Module for RailGPT
Handles generating clean answers from chunks with accurate source attribution
"""

import logging
import os
from typing import Dict, List, Any, Tuple, Optional
from core.llm_router import generate_llm_answer
from core.source_formatter import format_sources_for_response
from core.visual_content_handler import process_visual_content_in_chunks

logger = logging.getLogger(__name__)

class AnswerGenerator:
    """
    Generates clean answers from chunks with proper source attribution
    """
    
    def __init__(self):
        self.chunk_limits = {
            "document": 6,  # Max chunks for documents
            "website": 4    # Max chunks for websites
        }
        self.similarity_thresholds = {
            "document": 0.3,  # Higher threshold for documents
            "website": 0.2    # Lower threshold for websites
        }
    
    async def generate_answer_from_chunks(
        self,
        query: str,
        chunks: List[Dict[str, Any]],
        source_type: str,
        model_id: str = "gemini-2.0-flash",
        extract_format: str = "paragraph"
    ) -> Tuple[str, List[Dict[str, Any]], bool, List[str]]:
        """
        Generate answer from chunks with clean source attribution
        
        Returns:
            - answer: Generated answer text
            - sources: List of source objects
            - visual_content_found: Whether visual content was found
            - visual_content_types: Types of visual content found
        """
        
        try:
            logger.info(f"🔄 Generating {source_type} answer from {len(chunks)} chunks")
            
            if not chunks:
                return self._empty_response(source_type)
            
            # Filter and rank chunks
            filtered_chunks = self._filter_and_rank_chunks(chunks, source_type)
            
            if not filtered_chunks:
                return self._empty_response(source_type)
            
            # Process visual content
            visual_content_found, visual_content_types = process_visual_content_in_chunks(filtered_chunks)
            
            # Generate answer using LLM
            answer = await generate_llm_answer(
                query=query,
                similar_chunks=filtered_chunks,
                model_id=model_id,
                extract_format=extract_format
            )
            
            if not answer or not answer.strip():
                return self._empty_response(source_type)
            
            # Format sources based on chunks actually used
            sources = format_sources_for_response(filtered_chunks, source_type)
            
            logger.info(f"✅ Generated {source_type} answer with {len(sources)} sources")
            return answer, sources, visual_content_found, visual_content_types
            
        except Exception as e:
            logger.error(f"❌ Answer generation error: {str(e)}")
            return self._error_response(source_type)
    
    def _filter_and_rank_chunks(
        self, 
        chunks: List[Dict[str, Any]], 
        source_type: str
    ) -> List[Dict[str, Any]]:
        """
        Filter and rank chunks based on similarity and relevance
        """
        threshold = self.similarity_thresholds[source_type]
        limit = self.chunk_limits[source_type]
        
        # Filter by similarity threshold
        filtered = [
            chunk for chunk in chunks 
            if chunk.get("similarity", 0) >= threshold and 
               len(chunk.get("text", "")) >= 20  # Minimum text length
        ]
        
        # Sort by similarity score (descending)
        filtered.sort(key=lambda x: x.get("similarity", 0), reverse=True)
        
        # Apply content type boost for documents
        if source_type == "document":
            filtered = self._apply_content_boost(filtered)
        
        # Limit to top chunks
        return filtered[:limit]
    
    def _apply_content_boost(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Apply content type boost for document chunks
        """
        for chunk in chunks:
            metadata = chunk.get("metadata", {})
            content_type = metadata.get("content_type", "text")
            
            # Boost certain content types
            boost_factor = {
                "text": 1.0,
                "table": 1.1,  # Tables often contain structured info
                "image": 0.9,  # Slightly lower for images
                "chart_diagram": 1.05
            }.get(content_type, 1.0)
            
            # Apply boost to similarity score
            original_similarity = chunk.get("similarity", 0)
            boosted_similarity = min(original_similarity * boost_factor, 1.0)
            chunk["similarity"] = boosted_similarity
        
        # Re-sort after boosting
        chunks.sort(key=lambda x: x.get("similarity", 0), reverse=True)
        return chunks
    
    def _empty_response(self, source_type: str) -> Tuple[str, List, bool, List]:
        """Return empty response for given source type"""
        message = f"I couldn't find relevant information in the {source_type} content."
        return message, [], False, []
    
    def _error_response(self, source_type: str) -> Tuple[str, List, bool, List]:
        """Return error response for given source type"""
        message = f"I encountered an error while processing the {source_type} content."
        return message, [], False, []

# Global answer generator instance
answer_generator = AnswerGenerator()

# Convenience function for backward compatibility
async def generate_answer_from_chunks(
    query: str,
    chunks: List[Dict[str, Any]],
    source_type: str,
    model_id: str = "gemini-2.0-flash",
    extract_format: str = "paragraph"
) -> Tuple[str, List[Dict[str, Any]], bool, List[str]]:
    """Generate answer from chunks"""
    return await answer_generator.generate_answer_from_chunks(
        query, chunks, source_type, model_id, extract_format
    ) 