# RailGPT Backend Monitoring Setup Script
# This script sets up comprehensive monitoring for the RailGPT backend

Write-Host "🚂 Setting up RailGPT Backend Monitoring..." -ForegroundColor Green

# Variables
$PROJECT_ID = "railchatbot-cb553"
$SERVICE_NAME = "railgpt-backend"
$REGION = "us-central1"
$BACKEND_URL = "https://railgpt-backend-u5ww2ta3ia-uc.a.run.app"

Write-Host "`n📊 1. Checking Dashboard Creation..." -ForegroundColor Yellow
gcloud monitoring dashboards list --project=$PROJECT_ID --filter="displayName:RailGPT"

Write-Host "`n🔍 2. Testing Backend Health..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-WebRequest -Uri "$BACKEND_URL/health" -Method GET -TimeoutSec 10
    Write-Host "✅ Backend Health Check: Status $($healthResponse.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($healthResponse.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Backend Health Check Failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🧪 3. Testing API Endpoint..." -ForegroundColor Yellow
try {
    $apiBody = @{
        query = "What is Indian Railway?"
        model = "gemini-2.0-flash"
    } | ConvertTo-Json

    $apiResponse = Invoke-WebRequest -Uri "$BACKEND_URL/api/query" -Method POST -Body $apiBody -ContentType "application/json" -TimeoutSec 30
    Write-Host "✅ API Test: Status $($apiResponse.StatusCode)" -ForegroundColor Green
    $responseData = $apiResponse.Content | ConvertFrom-Json
    Write-Host "Response Preview: $($responseData.response.Substring(0, [Math]::Min(100, $responseData.response.Length)))..." -ForegroundColor Cyan
} catch {
    Write-Host "❌ API Test Failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n📈 4. Checking Service Metrics..." -ForegroundColor Yellow
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=$SERVICE_NAME" --limit=3 --project=$PROJECT_ID

Write-Host "`n🔧 5. Service Configuration..." -ForegroundColor Yellow
gcloud run services describe $SERVICE_NAME --region=$REGION --project=$PROJECT_ID --format="table(metadata.name,status.url,spec.template.spec.containers[0].resources.limits.memory,spec.template.spec.containers[0].resources.limits.cpu)"

Write-Host "`n📋 6. Monitoring URLs:" -ForegroundColor Yellow
Write-Host "• Cloud Console: https://console.cloud.google.com/run/detail/$REGION/$SERVICE_NAME/metrics?project=$PROJECT_ID" -ForegroundColor Cyan
Write-Host "• Logs: https://console.cloud.google.com/logs/query;query=resource.type%3D%22cloud_run_revision%22%0Aresource.labels.service_name%3D%22$SERVICE_NAME%22?project=$PROJECT_ID" -ForegroundColor Cyan
Write-Host "• Monitoring: https://console.cloud.google.com/monitoring/dashboards?project=$PROJECT_ID" -ForegroundColor Cyan
Write-Host "• Backend URL: $BACKEND_URL" -ForegroundColor Cyan

Write-Host "`n✅ Monitoring setup complete!" -ForegroundColor Green
Write-Host "📱 Frontend should connect to: $BACKEND_URL" -ForegroundColor Magenta
