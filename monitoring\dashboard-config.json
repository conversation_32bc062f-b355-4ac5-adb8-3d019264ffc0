{"displayName": "RailGPT Backend Monitoring Dashboard", "mosaicLayout": {"columns": 12, "tiles": [{"width": 6, "height": 4, "widget": {"title": "Request Count", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"railgpt-backend\" AND metric.type=\"run.googleapis.com/request_count\"", "aggregation": {"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM"}}}, "plotType": "LINE"}], "yAxis": {"label": "Requests/sec", "scale": "LINEAR"}}}}, {"width": 6, "height": 4, "xPos": 6, "widget": {"title": "Response Latency", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"railgpt-backend\" AND metric.type=\"run.googleapis.com/request_latencies\"", "aggregation": {"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_DELTA", "crossSeriesReducer": "REDUCE_PERCENTILE_95"}}}, "plotType": "LINE"}], "yAxis": {"label": "Latency (ms)", "scale": "LINEAR"}}}}, {"width": 6, "height": 4, "yPos": 4, "widget": {"title": "Error Rate", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"railgpt-backend\" AND metric.type=\"logging.googleapis.com/log_entry_count\" AND metric.labels.severity=\"ERROR\"", "aggregation": {"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_RATE", "crossSeriesReducer": "REDUCE_SUM"}}}, "plotType": "LINE"}], "yAxis": {"label": "Errors/sec", "scale": "LINEAR"}}}}, {"width": 6, "height": 4, "xPos": 6, "yPos": 4, "widget": {"title": "Instance Count", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"railgpt-backend\" AND metric.type=\"run.googleapis.com/container/instance_count\"", "aggregation": {"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_SUM"}}}, "plotType": "STACKED_AREA"}], "yAxis": {"label": "Instances", "scale": "LINEAR"}}}}, {"width": 6, "height": 4, "yPos": 8, "widget": {"title": "Memory Usage", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"railgpt-backend\" AND metric.type=\"run.googleapis.com/container/memory/utilizations\"", "aggregation": {"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}}}, "plotType": "LINE"}], "yAxis": {"label": "Memory %", "scale": "LINEAR"}}}}, {"width": 6, "height": 4, "xPos": 6, "yPos": 8, "widget": {"title": "CPU Usage", "xyChart": {"dataSets": [{"timeSeriesQuery": {"timeSeriesFilter": {"filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=\"railgpt-backend\" AND metric.type=\"run.googleapis.com/container/cpu/utilizations\"", "aggregation": {"alignmentPeriod": "60s", "perSeriesAligner": "ALIGN_MEAN", "crossSeriesReducer": "REDUCE_MEAN"}}}, "plotType": "LINE"}], "yAxis": {"label": "CPU %", "scale": "LINEAR"}}}}]}}