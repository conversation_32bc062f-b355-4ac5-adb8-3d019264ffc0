#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
sys.path.append('.')

def test_pdf_extraction():
    try:
        # Import the document extractor
        from document_extractor import extract_document
        
        # Path to the ACP PDF
        pdf_path = os.path.join("data", "uploads", "ACP 110V.pdf")
        
        print("🔍 Testing ACP 110V.pdf Extraction")
        print("=" * 50)
        
        if not os.path.exists(pdf_path):
            print(f"❌ File not found: {pdf_path}")
            return
        
        file_size = os.path.getsize(pdf_path)
        print(f"📄 File: {pdf_path}")
        print(f"📏 Size: {file_size} bytes")
        
        print(f"\n🔄 Extracting content...")
        
        # Extract the document
        extracted_items = extract_document(pdf_path)
        
        print(f"✅ Extraction completed")
        print(f"📊 Extracted items: {len(extracted_items)}")
        
        if extracted_items:
            print(f"\n📝 Content Analysis:")
            total_text = 0
            
            for i, item in enumerate(extracted_items[:5]):  # Show first 5 items
                print(f"\n   Item {i+1}:")
                print(f"      Source type: {item.get('source_type', 'unknown')}")
                print(f"      Page: {item.get('page_number', item.get('page', 'N/A'))}")
                
                text_content = item.get('text', '')
                if text_content:
                    total_text += len(text_content)
                    preview = text_content[:200] + "..." if len(text_content) > 200 else text_content
                    print(f"      Text length: {len(text_content)} chars")
                    print(f"      Text preview: {preview}")
                else:
                    print(f"      No text content")
            
            print(f"\n📈 Summary:")
            print(f"   Total text characters: {total_text}")
            
            # Check if any items contain "ACP"
            acp_found = False
            for item in extracted_items:
                text = item.get('text', '')
                if 'ACP' in text.upper():
                    acp_found = True
                    print(f"\n🎯 Found ACP content!")
                    acp_preview = text[:400] + "..." if len(text) > 400 else text
                    print(f"   Preview: {acp_preview}")
                    break
            
            if not acp_found:
                print(f"\n⚠️ No 'ACP' content found in extracted text")
                print(f"   This could mean:")
                print(f"   1. PDF is image-based and needs OCR")
                print(f"   2. ACP term is in images/tables not extracted yet")
                print(f"   3. Extraction method missed the content")
                
                # Show first few lines of each page to help debug
                print(f"\n🔍 First 100 chars from each extracted item:")
                for i, item in enumerate(extracted_items[:3]):
                    text = item.get('text', '')[:100]
                    print(f"   Item {i+1}: {text}")
        
        else:
            print(f"❌ No content extracted!")
            print(f"   This means:")
            print(f"   1. PDF might be corrupted")
            print(f"   2. PDF is image-only and OCR failed")
            print(f"   3. PDF is password protected")
            print(f"   4. Extraction libraries have issues")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print(f"   Trying alternative import...")
        try:
            # Try alternative import
            from document_extractor_fixed import extract_document
            print(f"✅ Using document_extractor_fixed instead")
            # ... rest of the code would be the same
        except ImportError as e2:
            print(f"❌ Both imports failed: {e2}")
    except Exception as e:
        print(f"❌ Extraction error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_pdf_extraction() 