import os
from supabase import create_client, Client
from fastapi import Depends, HTTPException
from functools import lru_cache

@lru_cache()
def get_supabase_url() -> str:
    """Get Supabase URL from environment variables"""
    url = os.environ.get("SUPABASE_URL")
    if not url:
        raise ValueError("SUPABASE_URL environment variable is not set")
    return url

@lru_cache()
def get_supabase_key() -> str:
    """Get Supabase key from environment variables"""
    key = os.environ.get("SUPABASE_KEY")
    if not key:
        raise ValueError("SUPABASE_KEY environment variable is not set")
    return key

def get_supabase_client() -> Client:
    """Create and return a Supabase client"""
    try:
        url = get_supabase_url()
        key = get_supabase_key()
        return create_client(url, key)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error connecting to Supabase: {str(e)}")

def check_supabase_connection() -> bool:
    """Check if we can connect to Supabase"""
    try:
        client = get_supabase_client()
        # Try a simple query to verify connection
        response = client.table("documents").select("id").limit(1).execute()
        return True
    except Exception as e:
        print(f"Supabase connection error: {e}")
        return False
