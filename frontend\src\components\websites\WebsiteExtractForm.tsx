import React, { useState, useEffect } from 'react';
import { addWebsite } from '../../services/api';
import { getWebsiteCategories, getCategoriesOfType, createWebsiteCategory } from '../../services/categoryApi';
import { CategoryHierarchy, CategoryCreate } from '../../types/documents';
import { Alert, AlertDescription, AlertTitle } from "../ui/alert";
import ManageCategoriesModal from '../categories/ManageCategoriesModal';
import { Settings } from 'lucide-react';

// Form validation interface
interface FormErrors {
  websiteUrl?: string;
}

interface WebsiteExtractFormProps {}

const WebsiteExtractForm: React.FC<WebsiteExtractFormProps> = () => {
  // Form state
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [isExtracting, setIsExtracting] = useState(false);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  // Hierarchical category state
  const [categories, setCategories] = useState<CategoryHierarchy[]>([]);
  const [selectedMainCategory, setSelectedMainCategory] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedSubCategory, setSelectedSubCategory] = useState('');
  const [selectedMinorCategory, setSelectedMinorCategory] = useState('');

  // Available options based on selections
  const [mainCategories, setMainCategories] = useState<CategoryHierarchy[]>([]);
  const [availableCategories, setAvailableCategories] = useState<CategoryHierarchy[]>([]);
  const [availableSubCategories, setAvailableSubCategories] = useState<CategoryHierarchy[]>([]);
  const [availableMinorCategories, setAvailableMinorCategories] = useState<CategoryHierarchy[]>([]);

  // Advanced options
  const [extractionDepth, setExtractionDepth] = useState<number>(1);
  const [followLinks, setFollowLinks] = useState<boolean>(true);
  const [extractImages, setExtractImages] = useState<boolean>(false);
  const [extractTables, setExtractTables] = useState<boolean>(true);
  const [maxPages, setMaxPages] = useState<number>(10);
  const [extractorType, setExtractorType] = useState<string>('auto');

  // Extraction status tracking
  const [extractionStatus, setExtractionStatus] = useState<'idle' | 'extracting' | 'processing' | 'success' | 'error'>('idle');
  const [extractionProgress, setExtractionProgress] = useState(0);
  const [extractedChunks, setExtractedChunks] = useState<any[]>([]);

  // Form validation and feedback
  const [errors, setErrors] = useState<FormErrors>({});
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // New category creation state
  const [showNewCategoryInput, setShowNewCategoryInput] = useState(false);
  const [newCategoryInput, setNewCategoryInput] = useState('');
  const [categoryType, setCategoryType] = useState<'Main' | 'Category' | 'Sub' | 'Minor'>('Main');

  // Manage Categories Modal state
  const [showManageCategoriesModal, setShowManageCategoriesModal] = useState(false);



  // Load categories on component mount
  useEffect(() => {
    loadCategories();
  }, []);

  // Update available options when selections change
  useEffect(() => {
    updateAvailableOptions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [categories, selectedMainCategory, selectedCategory, selectedSubCategory]);

  const loadCategories = async () => {
    try {
      const categoryData = await getWebsiteCategories();
      setCategories(categoryData);

      // Get main categories
      const mainCats = getCategoriesOfType(categoryData, 'main_category');
      setMainCategories(mainCats);
    } catch (err) {
      console.error('Failed to load website categories:', err);
    }
  };

  const updateAvailableOptions = () => {
    // Find selected main category
    const mainCat = mainCategories.find(cat => cat.name === selectedMainCategory);
    if (mainCat && mainCat.children) {
      const categoryOptions = mainCat.children.filter(cat => cat.type === 'category');
      setAvailableCategories(categoryOptions);

      // Find selected category within the new available categories
      const cat = categoryOptions.find(cat => cat.name === selectedCategory);
      if (cat && cat.children) {
        const subCategoryOptions = cat.children.filter(cat => cat.type === 'sub_category');
        setAvailableSubCategories(subCategoryOptions);

        // Find selected sub category within the new available sub categories
        const subCat = subCategoryOptions.find(cat => cat.name === selectedSubCategory);
        if (subCat && subCat.children) {
          setAvailableMinorCategories(subCat.children.filter(cat => cat.type === 'minor_category'));
        } else {
          setAvailableMinorCategories([]);
        }
      } else {
        setAvailableSubCategories([]);
        setAvailableMinorCategories([]);
      }
    } else {
      setAvailableCategories([]);
      setAvailableSubCategories([]);
      setAvailableMinorCategories([]);
    }
  };

  const handleMainCategoryChange = (value: string) => {
    setSelectedMainCategory(value);
    setSelectedCategory('');
    setSelectedSubCategory('');
    setSelectedMinorCategory('');
  };

  const handleCategoryChange = (value: string) => {
    setSelectedCategory(value);
    setSelectedSubCategory('');
    setSelectedMinorCategory('');
  };

  const handleSubCategoryChange = (value: string) => {
    setSelectedSubCategory(value);
    setSelectedMinorCategory('');
  };

  // Handle manage categories modal
  const handleManageCategoriesUpdate = () => {
    // Refresh categories
    loadCategories();
  };

  // Effect to validate form
  useEffect(() => {
    const newErrors: FormErrors = {};

    if (touched.websiteUrl) {
      if (!websiteUrl.trim()) {
        newErrors.websiteUrl = 'Website URL is required';
      } else if (!validateUrl(websiteUrl)) {
        newErrors.websiteUrl = 'Please enter a valid URL';
      }
    }

    setErrors(newErrors);
  }, [websiteUrl, touched]);

  // Mark fields as touched when user interacts with them
  const markAsTouched = (field: string) => {
    setTouched(prev => ({ ...prev, [field]: true }));
  };

  // Reset form after successful submission
  const resetForm = () => {
    setWebsiteUrl('');
    setSelectedMainCategory('');
    setSelectedCategory('');
    setSelectedSubCategory('');
    setSelectedMinorCategory('');
    setTouched({});
    setErrors({});
  };

  // Clear success message after 5 seconds
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage('');
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  // Clear error message after 5 seconds
  useEffect(() => {
    if (errorMessage) {
      const timer = setTimeout(() => {
        setErrorMessage('');
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [errorMessage]);

  const validateUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  };

  const handleNewCategorySubmit = async (): Promise<void> => {
    if (!newCategoryInput.trim()) return;

    setIsExtracting(true);
    try {
      // Determine parent ID and category type for API
      let parentId: string | undefined;
      let apiCategoryType: 'main_category' | 'category' | 'sub_category' | 'minor_category';

      // Find the selected categories by ID instead of name
      const mainCat = mainCategories.find(cat => cat.name === selectedMainCategory);
      const cat = availableCategories.find(cat => cat.name === selectedCategory);
      const subCat = availableSubCategories.find(cat => cat.name === selectedSubCategory);

      switch (categoryType) {
        case 'Main':
          apiCategoryType = 'main_category';
          parentId = undefined;
          break;
        case 'Category':
          apiCategoryType = 'category';
          parentId = mainCat?.id || undefined;
          break;
        case 'Sub':
          apiCategoryType = 'sub_category';
          parentId = cat?.id || undefined;
          break;
        case 'Minor':
          apiCategoryType = 'minor_category';
          parentId = subCat?.id || undefined;
          break;
        default:
          apiCategoryType = 'main_category';
          parentId = undefined;
      }

      // Create new category via API
      const categoryData: CategoryCreate = {
        name: newCategoryInput,
        type: apiCategoryType,
        parent_id: parentId,
        description: ''
      };

      const apiResponse = await createWebsiteCategory(categoryData);

      // Dispatch event to notify other components
      window.dispatchEvent(new CustomEvent('categoryUpdated', { detail: apiResponse.category }));

      // Update selected category based on type and refresh the appropriate list
      const newCategoryName = newCategoryInput; // Use the name since selections are by name
      switch (categoryType) {
        case 'Main':
          setSelectedMainCategory(newCategoryName);
          await loadCategories();
          break;
        case 'Category':
          setSelectedCategory(newCategoryName);
          await loadCategories(); // Refresh all categories to include the new one
          break;
        case 'Sub':
          setSelectedSubCategory(newCategoryName);
          await loadCategories();
          break;
        case 'Minor':
          setSelectedMinorCategory(newCategoryName);
          await loadCategories();
          break;
      }

      setSuccessMessage(`Successfully created ${categoryType} category: ${newCategoryInput}`);
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error occurred';
      setErrorMessage(`Failed to create category: ${errorMsg}`);
    } finally {
      setIsExtracting(false);
      setNewCategoryInput('');
      setShowNewCategoryInput(false);
    }
  };



  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Mark URL field as touched to trigger validation
    markAsTouched('websiteUrl');

    // Check if there are any validation errors
    if (Object.keys(errors).length > 0) {
      return;
    }

    // Update UI state
    setIsExtracting(true);
    setExtractionStatus('extracting');
    setExtractionProgress(10);
    setSuccessMessage('');
    setErrorMessage('');
    setExtractedChunks([]);

    // Simulate extraction progress updates
    const progressInterval = setInterval(() => {
      setExtractionProgress(prev => {
        const newProgress = prev + 5;
        if (newProgress >= 90) {
          clearInterval(progressInterval);
          return 90; // Hold at 90% until processing is complete
        }
        return newProgress;
      });
    }, 300);

    // Prepare extraction options
    const extractionOptions = {
      followLinks: followLinks,
      extractionDepth: extractionDepth,
      extractImages: extractImages,
      extractTables: extractTables,
      maxPages: maxPages,
      extractorType: extractorType,
      // Include hierarchical categories
      mainCategory: selectedMainCategory || undefined,
      category: selectedCategory || undefined,
      subCategory: selectedSubCategory || undefined,
      minorCategory: selectedMinorCategory || undefined
    };

    // Add the website
    setExtractionStatus('extracting');
    addWebsite(websiteUrl, '<EMAIL>', extractionOptions)
      .then(response => {
        // Clear the progress interval
        clearInterval(progressInterval);

        if (response.success) {
          // Set progress to 100%
          setExtractionProgress(100);
          setExtractionStatus('success');

          // Store extracted chunks if available
          if (response.chunks && response.chunks.length > 0) {
            setExtractedChunks(response.chunks);
            console.log('Extracted chunks:', response.chunks);
          }

          // Create event data for custom event
          const eventData = {
            detail: {
              url: websiteUrl,
              domain: new URL(websiteUrl).hostname,
              extractedAt: new Date().toISOString(),
              id: response.data?.id || `web-${Date.now()}`,
              status: 'Success',
              // Include hierarchical categories
              mainCategory: selectedMainCategory,
              category: selectedCategory,
              subCategory: selectedSubCategory,
              minorCategory: selectedMinorCategory,
              chunks: response.chunks || [],
              extractedContent: response.chunks ? response.chunks.map(chunk => chunk.text).join('\n\n') : '',
              chunks_extracted: response.chunks_extracted || 0
            }
          };

          // Dispatch a custom event to notify parent components (WebsitesPage) of the extraction
          const extractEvent = new CustomEvent('websiteExtracted', eventData);
          window.dispatchEvent(extractEvent);

          setSuccessMessage(`Website ${websiteUrl} added successfully. ${response.chunks_extracted || 0} chunks extracted.`);

          // Reset form after a short delay to allow user to see success message and extracted content
          setTimeout(() => {
            resetForm();
            setExtractionStatus('idle');
            setExtractionProgress(0);
          }, 5000);
        } else {
          setExtractionStatus('error');
          setErrorMessage(`Extraction failed: ${response.message}`);
        }
      })
      .catch(error => {
        clearInterval(progressInterval);
        console.error('Extraction failed:', error);
        // Set error message (ensure success is cleared)
        setSuccessMessage('');
        setErrorMessage(`Extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        setExtractionStatus('error');
      })
      .finally(() => {
        setIsExtracting(false);
      });
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 transition-colors duration-300">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-900">Extract Website Data</h2>
        <button
          type="button"
          onClick={() => setShowManageCategoriesModal(true)}
          className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300"
        >
          <Settings size={16} />
          Manage Categories
        </button>
      </div>

      {/* Success message */}
      {successMessage && (
        <div className="mb-4">
          <Alert variant="success">
            <AlertTitle>Success</AlertTitle>
            <AlertDescription>{successMessage}</AlertDescription>
          </Alert>
        </div>
      )}

      {/* Error message */}
      {errorMessage && (
        <div className="mb-4">
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{errorMessage}</AlertDescription>
          </Alert>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Website URL */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Website URL <span className="text-red-500">*</span>
          </label>
          <input
            type="url"
            value={websiteUrl}
            onChange={(e) => setWebsiteUrl(e.target.value)}
            onBlur={() => markAsTouched('websiteUrl')}
            className={`w-full p-2 border ${errors.websiteUrl ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
            placeholder="https://example.com"
            required
          />
          {errors.websiteUrl && (
            <p className="mt-1 text-sm text-red-500">{errors.websiteUrl}</p>
          )}
        </div>



        {/* Hierarchical Categories */}
        <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Content Categories</h3>
          <p className="text-xs text-gray-600 mb-4">
            Organize this website content using the hierarchical category system.
          </p>

          <div className="space-y-3">
            {/* Main Category */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Main Category
              </label>
              <div className="flex gap-2">
                <select
                  value={selectedMainCategory}
                  onChange={(e) => handleMainCategoryChange(e.target.value)}
                  className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Main Category</option>
                  {(mainCategories || []).map((cat) => (
                    <option key={cat.id} value={cat.name}>
                      {cat.name}
                    </option>
                  ))}
                </select>
                <button
                  type="button"
                  onClick={() => {
                    setCategoryType('Main');
                    setShowNewCategoryInput(true);
                  }}
                  className="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300"
                >
                  + New
                </button>
              </div>
            </div>

            {/* Category */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <div className="flex gap-2">
                <select
                  value={selectedCategory}
                  onChange={(e) => handleCategoryChange(e.target.value)}
                  className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!selectedMainCategory}
                >
                  <option value="">Select Category</option>
                  {(availableCategories || []).map((cat) => (
                    <option key={cat.id} value={cat.name}>
                      {cat.name}
                    </option>
                  ))}
                </select>
                <button
                  type="button"
                  onClick={() => {
                    setCategoryType('Category');
                    setShowNewCategoryInput(true);
                  }}
                  className="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300"
                  disabled={!selectedMainCategory}
                >
                  + New
                </button>
              </div>
            </div>

            {/* Sub Category */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sub Category
              </label>
              <div className="flex gap-2">
                <select
                  value={selectedSubCategory}
                  onChange={(e) => handleSubCategoryChange(e.target.value)}
                  className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!selectedCategory}
                >
                  <option value="">Select Sub Category</option>
                  {(availableSubCategories || []).map((cat) => (
                    <option key={cat.id} value={cat.name}>
                      {cat.name}
                    </option>
                  ))}
                </select>
                <button
                  type="button"
                  onClick={() => {
                    setCategoryType('Sub');
                    setShowNewCategoryInput(true);
                  }}
                  className="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300"
                  disabled={!selectedCategory}
                >
                  + New
                </button>
              </div>
            </div>

            {/* Minor Category */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Minor Category
              </label>
              <div className="flex gap-2">
                <select
                  value={selectedMinorCategory}
                  onChange={(e) => setSelectedMinorCategory(e.target.value)}
                  className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  disabled={!selectedSubCategory}
                >
                  <option value="">Select Minor Category</option>
                  {(availableMinorCategories || []).map((cat) => (
                    <option key={cat.id} value={cat.name}>
                      {cat.name}
                    </option>
                  ))}
                </select>
                <button
                  type="button"
                  onClick={() => {
                    setCategoryType('Minor');
                    setShowNewCategoryInput(true);
                  }}
                  className="px-3 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors duration-300"
                  disabled={!selectedSubCategory}
                >
                  + New
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Extraction Options */}
        <div className="p-3 bg-gray-50 rounded-md">
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-sm font-medium text-gray-700">Extraction Options</h3>
            <button
              type="button"
              onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium focus:outline-none"
            >
              {showAdvancedOptions ? 'Hide Advanced Options' : 'Show Advanced Options'}
            </button>
          </div>
          <div className="space-y-2">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="follow-links"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={followLinks}
                onChange={(e) => setFollowLinks(e.target.checked)}
              />
              <label htmlFor="follow-links" className="ml-2 block text-sm text-gray-700">
                Follow and extract linked pages
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="extract-images"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={extractImages}
                onChange={(e) => setExtractImages(e.target.checked)}
              />
              <label htmlFor="extract-images" className="ml-2 block text-sm text-gray-700">
                Extract image alt text
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="extract-tables"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                checked={extractTables}
                onChange={(e) => setExtractTables(e.target.checked)}
              />
              <label htmlFor="extract-tables" className="ml-2 block text-sm text-gray-700">
                Extract tables as structured data
              </label>
            </div>

            {showAdvancedOptions && (
              <div className="mt-3 space-y-4 pt-3 border-t border-gray-200">
                <div>
                  <label htmlFor="extraction-depth" className="block text-sm font-medium text-gray-700 mb-1">
                    Extraction Depth: {extractionDepth}
                  </label>
                  <input
                    type="range"
                    id="extraction-depth"
                    min="1"
                    max="5"
                    value={extractionDepth}
                    onChange={(e) => setExtractionDepth(parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>1 (Current page only)</span>
                    <span>5 (Deep crawl)</span>
                  </div>
                </div>

                <div>
                  <label htmlFor="max-pages" className="block text-sm font-medium text-gray-700 mb-1">
                    Maximum Pages: {maxPages}
                  </label>
                  <input
                    type="number"
                    id="max-pages"
                    min="1"
                    max="50"
                    value={maxPages}
                    onChange={(e) => setMaxPages(parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="mt-1 text-xs text-gray-500">Maximum number of pages to extract (1-50)</p>
                </div>

                <div>
                  <label htmlFor="extractor-type" className="block text-sm font-medium text-gray-700 mb-1">
                    Extraction Method
                  </label>
                  <select
                    id="extractor-type"
                    value={extractorType}
                    onChange={(e) => setExtractorType(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="auto">Auto (Recommended)</option>
                    <option value="html">HTML Only</option>
                    <option value="browser">Browser Rendering</option>
                    <option value="api">API-Based (If Available)</option>
                  </select>
                  <p className="mt-1 text-xs text-gray-500">Method used to extract content from the website</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <div>
          <button
            type="submit"
            disabled={isExtracting}
            className={`w-full px-4 py-2 text-white font-medium rounded-md ${
              isExtracting ? 'bg-green-300' : 'bg-green-600 hover:bg-green-700'
            } focus:outline-none focus:ring-2 focus:ring-green-500`}
          >
            {isExtracting ? 'Extracting...' : 'Extract Website'}
          </button>
        </div>

        {/* Extraction Progress */}
        {extractionStatus !== 'idle' && (
          <div className="mt-4">
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm font-medium text-gray-700">
                {extractionStatus === 'extracting' ? 'Extracting website content...' :
                 extractionStatus === 'processing' ? 'Processing extracted content...' :
                 extractionStatus === 'success' ? 'Extraction Complete' :
                 'Extraction Failed'}
              </span>
              <span className="text-sm font-medium text-gray-700">{extractionProgress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className={`h-2.5 rounded-full ${
                  extractionStatus === 'error' ? 'bg-red-600' :
                  extractionStatus === 'success' ? 'bg-green-600' : 'bg-green-600'
                }`}
                style={{ width: `${extractionProgress}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Extracted Chunks Preview (shown after successful extraction) */}
        {extractionStatus === 'success' && extractedChunks.length > 0 && (
          <div className="mt-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Extracted Content Preview:</h3>
            <div className="max-h-40 overflow-y-auto text-sm text-gray-600">
              {extractedChunks.slice(0, 3).map((chunk, index) => (
                <div key={index} className="mb-2 p-2 bg-white rounded border border-gray-200">
                  <p className="text-xs text-gray-500 mb-1">
                    {chunk.source_type === 'website' ?
                      `URL: ${chunk.url || chunk.source || 'Unknown'}` :
                      `Source: ${chunk.source || 'Unknown'}`}
                  </p>
                  <p>{chunk.text?.substring(0, 150)}...</p>
                </div>
              ))}
              {extractedChunks.length > 3 && (
                <p className="text-xs text-gray-500 text-center mt-2">
                  + {extractedChunks.length - 3} more chunks not shown
                </p>
              )}
            </div>
          </div>
        )}
      </form>

      {/* New Category Input Modal */}
      {showNewCategoryInput && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-96">
            <h3 className="text-lg font-semibold mb-4">Create New {categoryType} Category</h3>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category Name
              </label>
              <input
                type="text"
                value={newCategoryInput}
                onChange={(e) => setNewCategoryInput(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder={`Enter ${categoryType.toLowerCase()} category name`}
                autoFocus
              />
            </div>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => {
                  setShowNewCategoryInput(false);
                  setNewCategoryInput('');
                }}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleNewCategorySubmit}
                disabled={!newCategoryInput.trim() || isExtracting}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isExtracting ? 'Creating...' : 'Create'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Manage Categories Modal */}
      <ManageCategoriesModal
        isOpen={showManageCategoriesModal}
        onClose={() => setShowManageCategoriesModal(false)}
        type="website"
        onCategoryUpdated={handleManageCategoriesUpdate}
      />

    </div>
  );
};

export default WebsiteExtractForm;
