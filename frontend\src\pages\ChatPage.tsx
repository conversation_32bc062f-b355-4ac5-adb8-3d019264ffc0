import React, { useState, useEffect } from 'react';
import { useParams, useHistory } from 'react-router-dom';
import ChatInterface from '../components/chat/ChatInterface';
import ChatSidebar from '../components/chat/ChatSidebar';
import { getChatSessionById, createChatSession, ChatMessage, ChatSession } from '../services/supabase';

const ChatPage: React.FC = () => {
  const { sessionId } = useParams<{ sessionId?: string }>();
  const history = useHistory();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  useEffect(() => {
    // Reset state when session ID changes
    setMessages([]);
    setIsLoading(true);
    setError(null);

    // Load chat session if ID is provided
    if (sessionId) {
      loadChatSession(sessionId);
    } else {
      setIsLoading(false);
    }
  }, [sessionId]);

  const loadChatSession = async (id: string) => {
    try {
      const session = await getChatSessionById(id);

      if (session) {
        setMessages(session.messages || []);
      } else {
        setError('Chat session not found');
      }
    } catch (err) {
      console.error('Error loading chat session:', err);
      setError('Failed to load chat session');
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewSession = (newSessionId: string) => {
    // Redirect to the new session URL
    history.replace(`/chat/${newSessionId}`);
  };

  const handleCreateNewChat = async () => {
    try {
      const newSession = await createChatSession('New Chat');
      if (newSession) {
        history.push(`/chat/${newSession.id}`);
      }
    } catch (err) {
      console.error('Error creating new chat session:', err);
    }
  };

  const handleChatSelect = (chatSession: ChatSession) => {
    history.push(`/chat/${chatSession.id}`);
  };

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <div className="flex h-screen bg-gray-100">
      <ChatSidebar 
        isOpen={isSidebarOpen}
        onToggle={toggleSidebar}
        currentChatId={sessionId || ''}
        onChatSelect={handleChatSelect}
        onNewChat={handleCreateNewChat} 
        activeSessionId={sessionId} 
      />

      <div className="flex-1 flex flex-col">
        <header className="bg-white shadow p-4">
          <h1 className="text-xl font-semibold">RailGPT Chat</h1>
        </header>

        <main className="flex-1 overflow-hidden">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-700"></div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full text-red-600">
              <p>{error}</p>
            </div>
          ) : (
            <ChatInterface 
              sessionId={sessionId} 
              onNewSession={handleNewSession}
              initialMessages={messages}
            />
          )}
        </main>
      </div>
    </div>
  );
};

export default ChatPage;
