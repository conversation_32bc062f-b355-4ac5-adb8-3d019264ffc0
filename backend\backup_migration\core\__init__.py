"""
RailGPT Core Module
Contains the core functionality for search, LLM routing, and answer generation
"""

try:
    from .search_engine import search_engine
    from .llm_router import llm_router
    from .answer_generator import answer_generator
    from .source_formatter import source_formatter
    from .visual_content_handler import visual_handler
except ImportError:
    # Fallback to absolute imports when run as script
    from core.search_engine import search_engine
    from core.llm_router import llm_router
    from core.answer_generator import answer_generator
    from core.source_formatter import source_formatter
    from core.visual_content_handler import visual_handler

__all__ = [
    'search_engine',
    'llm_router', 
    'answer_generator',
    'source_formatter',
    'visual_handler'
] 