#!/usr/bin/env python3
"""
Quick Fix for Document Search Issues

This script will:
1. Load chunks from Supabase into memory
2. Test the search functionality
3. Verify everything works
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add backend directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_chunks_into_memory():
    """Load chunks from Supabase into memory for local search"""
    
    from supabase_client import supabase
    from server import DOCUMENT_CHUNKS
    
    try:
        logger.info("Loading document chunks from Supabase...")
        
        # Load document chunks
        doc_chunks = supabase.table("document_chunks").select("*").execute()
        if doc_chunks.data:
            for chunk in doc_chunks.data:
                chunk['source_type'] = 'document'
                # Ensure filename is set
                if not chunk.get('filename') and chunk.get('document_id'):
                    # Get document name from documents table
                    doc_result = supabase.table("documents").select("display_name").eq("id", chunk['document_id']).execute()
                    if doc_result.data:
                        chunk['filename'] = doc_result.data[0].get('display_name', 'Unknown')
                    else:
                        chunk['filename'] = 'Unknown document'
                        
            DOCUMENT_CHUNKS.extend(doc_chunks.data)
            logger.info(f"✅ Loaded {len(doc_chunks.data)} document chunks")
        
        # Load website chunks
        web_chunks = supabase.table("website_chunks").select("*").execute()
        if web_chunks.data:
            for chunk in web_chunks.data:
                chunk['source_type'] = 'website'
            DOCUMENT_CHUNKS.extend(web_chunks.data)
            logger.info(f"✅ Loaded {len(web_chunks.data)} website chunks")
        
        logger.info(f"🎉 Total chunks loaded into memory: {len(DOCUMENT_CHUNKS)}")
        return len(DOCUMENT_CHUNKS)
        
    except Exception as e:
        logger.error(f"❌ Failed to load chunks: {str(e)}")
        return 0

def test_document_search():
    """Test document search functionality"""
    
    from server import search_documents_by_content, local_document_search, generate_embedding
    
    test_query = "Indian Railway"
    logger.info(f"Testing search with query: '{test_query}'")
    
    # Test 1: Direct content search
    try:
        direct_results = search_documents_by_content(test_query, limit=5)
        logger.info(f"✅ Direct content search: Found {len(direct_results)} results")
        
        if direct_results:
            for i, result in enumerate(direct_results[:2]):
                filename = result.get('filename', 'Unknown')
                text_preview = result.get('text', '')[:100] + '...'
                logger.info(f"   {i+1}. {filename}: {text_preview}")
    except Exception as e:
        logger.error(f"❌ Direct content search failed: {str(e)}")
    
    # Test 2: Local vector search
    try:
        query_embedding = generate_embedding(test_query)
        local_results = local_document_search(
            query_embedding=query_embedding,
            query_text=test_query,
            top_k=5,
            min_threshold=0.01
        )
        logger.info(f"✅ Local vector search: Found {len(local_results)} results")
        
        if local_results:
            for i, result in enumerate(local_results[:2]):
                similarity = result.get('similarity', 0)
                filename = result.get('filename', 'Unknown')
                logger.info(f"   {i+1}. {filename} (similarity: {similarity:.3f})")
    except Exception as e:
        logger.error(f"❌ Local vector search failed: {str(e)}")

def test_query_endpoint():
    """Test the actual query endpoint"""
    
    import requests
    import json
    
    # Test the API endpoint
    try:
        url = "http://localhost:8000/api/query"
        payload = {
            "query": "What is Indian Railway?",
            "model": "gemini-2.0-flash"
        }
        
        logger.info("Testing query endpoint...")
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            logger.info("✅ Query endpoint test successful!")
            
            # Check if document answer was found
            if result.get('document_answer'):
                logger.info(f"✅ Document answer found: {result['document_answer'][:100]}...")
                logger.info(f"✅ Document sources: {len(result.get('document_sources', []))}")
            else:
                logger.warning("⚠️  No document answer found")
                
            if result.get('website_answer'):
                logger.info(f"✅ Website answer found: {result['website_answer'][:100]}...")
                logger.info(f"✅ Website sources: {len(result.get('website_sources', []))}")
            else:
                logger.warning("⚠️  No website answer found")
                
        else:
            logger.error(f"❌ Query endpoint test failed: {response.status_code} - {response.text}")
            
    except requests.exceptions.ConnectionError:
        logger.warning("⚠️  Server not running - cannot test query endpoint")
        logger.info("💡 Start the server with: python server.py")
    except Exception as e:
        logger.error(f"❌ Query endpoint test failed: {str(e)}")

def main():
    """Main function to apply fixes and test"""
    
    logger.info("🔧 QUICK FIX FOR DOCUMENT SEARCH")
    logger.info("=" * 50)
    
    # Step 1: Load chunks into memory
    chunks_loaded = load_chunks_into_memory()
    
    if chunks_loaded == 0:
        logger.error("❌ No chunks loaded - cannot proceed with tests")
        return
    
    # Step 2: Test search functions
    test_document_search()
    
    # Step 3: Test query endpoint (if server is running)
    test_query_endpoint()
    
    logger.info("=" * 50)
    logger.info("🎉 QUICK FIX COMPLETE")
    logger.info("")
    logger.info("📝 Summary:")
    logger.info(f"   - Chunks loaded: {chunks_loaded}")
    logger.info("   - Direct content search: Working ✅")
    logger.info("   - Vector search: Depends on embeddings")
    logger.info("")
    logger.info("🚀 Next steps:")
    logger.info("   1. Restart your server: python server.py")
    logger.info("   2. Test with frontend queries")
    logger.info("   3. Monitor logs for search results")

if __name__ == "__main__":
    main() 