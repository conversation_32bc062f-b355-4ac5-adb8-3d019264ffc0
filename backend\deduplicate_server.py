#!/usr/bin/env python3
"""
Server Deduplication Script
This script removes duplicate functions and classes from server.py
"""

import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def deduplicate_server():
    """Remove duplicates from server.py"""
    
    # Read the file
    with open('server.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    total_lines = len(lines)
    logger.info(f"Original file: {total_lines} lines")
    
    # Strategy: Find the second middleware and remove everything from there
    # until we hit the CategoryChangeLog class which is unique
    
    middleware_count = 0
    second_middleware_line = None
    category_log_line = None
    
    for i, line in enumerate(lines):
        # Find middleware declarations
        if '@app.middleware("http")' in line and 'add_custom_upload_limit' in lines[i+1] if i+1 < len(lines) else False:
            middleware_count += 1
            if middleware_count == 2:
                second_middleware_line = i
                logger.info(f"Found second middleware at line {i+1}")
        
        # Find CategoryChangeLog (unique content after duplicates)
        if 'class CategoryChangeLog(BaseModel):' in line:
            category_log_line = i
            logger.info(f"Found unique CategoryChangeLog at line {i+1}")
            break
    
    if second_middleware_line and category_log_line:
        # Remove the duplicate section
        logger.info(f"Removing duplicate section: lines {second_middleware_line+1} to {category_log_line}")
        cleaned_lines = lines[:second_middleware_line] + lines[category_log_line:]
        
        # Now remove the duplicate functions that appear before the middleware too
        cleaned_lines = remove_early_duplicates(cleaned_lines)
        
        # Write the cleaned file
        cleaned_content = '\n'.join(cleaned_lines)
        with open('server_deduplicated.py', 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        logger.info(f"Cleaned file: {len(cleaned_lines)} lines")
        logger.info(f"Removed {total_lines - len(cleaned_lines)} lines")
        logger.info("✅ Deduplication completed!")
        return True
    else:
        logger.error("Could not find duplicate boundaries")
        return False

def remove_early_duplicates(lines):
    """Remove duplicate functions that appear early in the file"""
    
    # Track functions we've seen to remove later duplicates
    seen_functions = {}
    cleaned_lines = []
    skip_function = False
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # Check for function definitions we want to deduplicate
        if line.startswith('def '):
            # Extract function name
            func_name = line.split('(')[0].replace('def ', '').strip()
            
            # List of functions that have duplicates
            duplicate_funcs = [
                'search_documents_by_title',
                'search_documents_by_content', 
                'search_for_acronym',
                'text_based_document_search',
                'text_based_website_search',
                'search_documents_in_supabase',
                'generate_clean_answer_with_sources',
                'search_supabase_document_chunks',
                'search_supabase_document_chunks_enhanced',
                'generate_embedding',
                'cosine_similarity',
                'search_supabase_website_chunks',
                'group_chunks_by_source',
                'generate_llm_answer',
                'load_documents',
                'format_visual_content_for_frontend',
                'local_document_search',
                'local_website_search',
                'improved_text_based_document_search',
                'improved_text_based_website_search'
            ]
            
            if func_name in duplicate_funcs:
                if func_name in seen_functions:
                    # This is a duplicate, skip it
                    logger.info(f"Removing duplicate function: {func_name} at line {i+1}")
                    skip_function = True
                    i += 1
                    continue
                else:
                    # First occurrence, keep it
                    seen_functions[func_name] = i
                    skip_function = False
            else:
                skip_function = False
        
        # Check for class definitions
        elif line.startswith('class ') and skip_function:
            skip_function = False
        
        # Check for endpoint definitions  
        elif line.startswith('@app.') and skip_function:
            skip_function = False
        
        # Skip lines if we're in a duplicate function
        if skip_function and not (line.startswith('def ') or line.startswith('class ') or line.startswith('@app.')):
            i += 1
            continue
        
        cleaned_lines.append(lines[i])
        i += 1
    
    return cleaned_lines

if __name__ == "__main__":
    success = deduplicate_server()
    if success:
        print("✅ Deduplication completed successfully!")
        print("📄 Check server_deduplicated.py")
    else:
        print("❌ Deduplication failed") 