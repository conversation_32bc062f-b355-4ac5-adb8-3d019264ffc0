"""
Performance Monitor for RailGPT Application
Real-time monitoring and enforcement of performance constraints.
"""

import time
import psutil
import threading
import logging
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import asyncio
from collections import deque

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """Container for performance metrics."""
    timestamp: datetime
    memory_usage_mb: float
    cpu_usage_percent: float
    response_time_ms: float
    active_requests: int
    cache_size_mb: float
    model_used: str
    endpoint: str
    success: bool
    error_message: Optional[str] = None

@dataclass
class PerformanceAlert:
    """Performance alert definition."""
    metric: str
    threshold: float
    current_value: float
    severity: str  # 'warning', 'critical'
    timestamp: datetime
    message: str

class PerformanceMonitor:
    """Real-time performance monitoring and alerting system."""
    
    def __init__(self, max_memory_mb: int = 2048, max_response_time_ms: int = 30000):
        self.max_memory_mb = max_memory_mb
        self.max_response_time_ms = max_response_time_ms
        self.metrics_history: deque = deque(maxlen=1000)  # Keep last 1000 metrics
        self.alerts: List[PerformanceAlert] = []
        self.active_requests = 0
        self.request_lock = threading.Lock()
        
        # Performance thresholds
        self.memory_warning_threshold = max_memory_mb * 0.8  # 80% warning
        self.memory_critical_threshold = max_memory_mb * 0.95  # 95% critical
        self.response_warning_threshold = max_response_time_ms * 0.8
        
        # Start monitoring thread
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info(f"Performance monitor started with limits: {max_memory_mb}MB memory, {max_response_time_ms}ms response time")
    
    def _monitor_loop(self):
        """Main monitoring loop running in background thread."""
        while self.monitoring_active:
            try:
                self._collect_system_metrics()
                self._check_alerts()
                time.sleep(5)  # Check every 5 seconds
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(10)  # Wait longer on error
    
    def _collect_system_metrics(self):
        """Collect current system performance metrics."""
        try:
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            cpu_percent = process.cpu_percent()
            
            # Create system metric record
            metric = PerformanceMetrics(
                timestamp=datetime.utcnow(),
                memory_usage_mb=memory_mb,
                cpu_usage_percent=cpu_percent,
                response_time_ms=0,  # System metric, not request-specific
                active_requests=self.active_requests,
                cache_size_mb=0,  # TODO: Implement cache size tracking
                model_used="system",
                endpoint="system",
                success=True
            )
            
            self.metrics_history.append(metric)
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
    
    def _check_alerts(self):
        """Check for performance threshold breaches and generate alerts."""
        if not self.metrics_history:
            return
        
        latest_metric = self.metrics_history[-1]
        current_time = datetime.utcnow()
        
        # Clear old alerts (older than 1 hour)
        self.alerts = [alert for alert in self.alerts 
                      if current_time - alert.timestamp < timedelta(hours=1)]
        
        # Check memory usage
        if latest_metric.memory_usage_mb >= self.memory_critical_threshold:
            self._add_alert("memory", self.memory_critical_threshold, 
                          latest_metric.memory_usage_mb, "critical",
                          f"Memory usage critical: {latest_metric.memory_usage_mb:.1f}MB")
        elif latest_metric.memory_usage_mb >= self.memory_warning_threshold:
            self._add_alert("memory", self.memory_warning_threshold,
                          latest_metric.memory_usage_mb, "warning",
                          f"Memory usage high: {latest_metric.memory_usage_mb:.1f}MB")
        
        # Check active requests
        if self.active_requests > 10:  # More than 10 concurrent requests
            self._add_alert("requests", 10, self.active_requests, "warning",
                          f"High concurrent requests: {self.active_requests}")
    
    def _add_alert(self, metric: str, threshold: float, current_value: float, 
                   severity: str, message: str):
        """Add a new performance alert."""
        # Avoid duplicate alerts within 5 minutes
        recent_alerts = [alert for alert in self.alerts 
                        if alert.metric == metric and 
                        datetime.utcnow() - alert.timestamp < timedelta(minutes=5)]
        
        if not recent_alerts:
            alert = PerformanceAlert(
                metric=metric,
                threshold=threshold,
                current_value=current_value,
                severity=severity,
                timestamp=datetime.utcnow(),
                message=message
            )
            self.alerts.append(alert)
            logger.warning(f"Performance alert: {message}")
    
    def start_request(self, endpoint: str, model: str = "unknown") -> str:
        """Start tracking a new request. Returns request ID."""
        with self.request_lock:
            self.active_requests += 1
            request_id = f"{endpoint}_{int(time.time() * 1000)}"
            return request_id
    
    def end_request(self, request_id: str, endpoint: str, model: str, 
                   start_time: float, success: bool = True, 
                   error_message: Optional[str] = None):
        """End request tracking and record metrics."""
        with self.request_lock:
            self.active_requests = max(0, self.active_requests - 1)
        
        response_time_ms = (time.time() - start_time) * 1000
        
        # Record request metrics
        try:
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            cpu_percent = process.cpu_percent()
            
            metric = PerformanceMetrics(
                timestamp=datetime.utcnow(),
                memory_usage_mb=memory_mb,
                cpu_usage_percent=cpu_percent,
                response_time_ms=response_time_ms,
                active_requests=self.active_requests,
                cache_size_mb=0,  # TODO: Implement
                model_used=model,
                endpoint=endpoint,
                success=success,
                error_message=error_message
            )
            
            self.metrics_history.append(metric)
            
            # Check response time threshold
            if response_time_ms >= self.max_response_time_ms:
                self._add_alert("response_time", self.max_response_time_ms,
                              response_time_ms, "critical",
                              f"Slow response: {response_time_ms:.1f}ms for {endpoint}")
            
        except Exception as e:
            logger.error(f"Error recording request metrics: {e}")
    
    def get_current_stats(self) -> Dict[str, Any]:
        """Get current performance statistics."""
        if not self.metrics_history:
            return {"status": "no_data"}
        
        latest = self.metrics_history[-1]
        recent_metrics = [m for m in self.metrics_history 
                         if datetime.utcnow() - m.timestamp < timedelta(minutes=5)]
        
        if recent_metrics:
            avg_response_time = sum(m.response_time_ms for m in recent_metrics 
                                  if m.response_time_ms > 0) / len([m for m in recent_metrics if m.response_time_ms > 0])
            success_rate = sum(1 for m in recent_metrics if m.success) / len(recent_metrics) * 100
        else:
            avg_response_time = 0
            success_rate = 100
        
        return {
            "current_memory_mb": latest.memory_usage_mb,
            "memory_limit_mb": self.max_memory_mb,
            "memory_usage_percent": (latest.memory_usage_mb / self.max_memory_mb) * 100,
            "active_requests": self.active_requests,
            "avg_response_time_ms": avg_response_time,
            "success_rate_percent": success_rate,
            "total_alerts": len(self.alerts),
            "critical_alerts": len([a for a in self.alerts if a.severity == "critical"]),
            "within_limits": {
                "memory": latest.memory_usage_mb <= self.max_memory_mb,
                "response_time": avg_response_time <= self.max_response_time_ms,
                "requests": self.active_requests <= 10
            }
        }
    
    def get_alerts(self, severity: Optional[str] = None) -> List[PerformanceAlert]:
        """Get current alerts, optionally filtered by severity."""
        if severity:
            return [alert for alert in self.alerts if alert.severity == severity]
        return self.alerts.copy()
    
    def check_memory_limit(self) -> bool:
        """Check if current memory usage is within limits."""
        try:
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            return memory_mb <= self.max_memory_mb
        except:
            return True  # Assume OK if can't check
    
    def should_throttle_requests(self) -> bool:
        """Check if requests should be throttled due to performance constraints."""
        return (self.active_requests >= 5 or 
                not self.check_memory_limit() or
                len([a for a in self.alerts if a.severity == "critical"]) > 0)
    
    def cleanup(self):
        """Stop monitoring and cleanup resources."""
        self.monitoring_active = False
        if self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        logger.info("Performance monitor stopped")

# Decorator for monitoring API endpoints
def monitor_performance(endpoint_name: str, model_name: str = "unknown"):
    """Decorator to monitor performance of API endpoints."""
    def decorator(func: Callable):
        def wrapper(*args, **kwargs):
            monitor = getattr(wrapper, '_monitor', None)
            if not monitor:
                return func(*args, **kwargs)
            
            request_id = monitor.start_request(endpoint_name, model_name)
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                monitor.end_request(request_id, endpoint_name, model_name, start_time, True)
                return result
            except Exception as e:
                monitor.end_request(request_id, endpoint_name, model_name, start_time, False, str(e))
                raise
        
        # Attach monitor instance
        wrapper._monitor = None
        return wrapper
    return decorator

# Global performance monitor instance (initialized when needed)
_global_monitor: Optional[PerformanceMonitor] = None

def get_performance_monitor() -> PerformanceMonitor:
    """Get or create global performance monitor instance."""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = PerformanceMonitor()
    return _global_monitor

def initialize_monitoring(max_memory_mb: int = 2048, max_response_time_ms: int = 30000):
    """Initialize global performance monitoring."""
    global _global_monitor
    if _global_monitor:
        _global_monitor.cleanup()
    _global_monitor = PerformanceMonitor(max_memory_mb, max_response_time_ms) 