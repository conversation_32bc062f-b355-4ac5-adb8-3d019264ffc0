#!/usr/bin/env python3
"""
RailGPT System Audit Script
Comprehensive audit of the RailGPT application to identify vector database search and answer generation issues.
"""

import os
import sys
import json
import logging
import requests
import time
from typing import Dict, List, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_backend_status():
    """Check if the backend server is running"""
    try:
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code == 200:
            logger.info("✅ Backend server is running")
            return True
        else:
            logger.error(f"❌ Backend server returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ Backend server is not accessible: {e}")
        return False

def check_supabase_connection():
    """Check Supabase database connection and data"""
    try:
        # Add backend to path
        sys.path.append(os.path.join(os.getcwd(), 'backend'))
        from supabase_client import SupabaseClient
        
        client = SupabaseClient()
        
        # Check document chunks
        logger.info("🔍 Checking document chunks...")
        doc_chunks = client.execute_query("SELECT COUNT(*) FROM document_chunks")
        logger.info(f"Document chunks result: {doc_chunks}")
        
        # Check website chunks  
        logger.info("🔍 Checking website chunks...")
        web_chunks = client.execute_query("SELECT COUNT(*) FROM website_chunks")
        logger.info(f"Website chunks result: {web_chunks}")
        
        # Check documents
        logger.info("🔍 Checking documents...")
        docs = client.execute_query("SELECT COUNT(*) FROM documents")
        logger.info(f"Documents result: {docs}")
        
        # Check websites
        logger.info("🔍 Checking websites...")
        websites = client.execute_query("SELECT COUNT(*) FROM websites")
        logger.info(f"Websites result: {websites}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error checking Supabase connection: {e}")
        return False

def test_query_endpoint():
    """Test the main query endpoint"""
    test_queries = [
        "FSDS full form",
        "Rapid response app", 
        "VASP development",
        "WDG4G details"
    ]
    
    results = {}
    
    for query in test_queries:
        logger.info(f"🔍 Testing query: {query}")
        try:
            response = requests.post(
                "http://localhost:8000/api/query",
                json={
                    "query": query,
                    "model": "gemini-2.0-flash",
                    "fallback_enabled": True,
                    "use_hybrid_search": True
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                results[query] = {
                    "success": True,
                    "document_sources": len(data.get('document_sources', [])),
                    "website_sources": len(data.get('website_sources', [])),
                    "llm_fallback": data.get('llm_fallback', False),
                    "answer_length": len(data.get('answer', '')),
                    "visual_content": data.get('visual_content_found', False)
                }
                logger.info(f"✅ Query successful - Doc sources: {results[query]['document_sources']}, Web sources: {results[query]['website_sources']}, LLM fallback: {results[query]['llm_fallback']}")
            else:
                results[query] = {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text[:200]}"
                }
                logger.error(f"❌ Query failed: {response.status_code}")
                
        except Exception as e:
            results[query] = {
                "success": False,
                "error": str(e)
            }
            logger.error(f"❌ Query error: {e}")
    
    return results

def main():
    """Main audit function"""
    logger.info("🚀 Starting RailGPT System Audit")
    logger.info("=" * 80)
    
    # Check 1: Backend Status
    logger.info("📋 STEP 1: Checking Backend Server Status")
    backend_running = check_backend_status()
    
    if not backend_running:
        logger.error("❌ Backend server is not running. Please start it first.")
        return False
    
    # Check 2: Supabase Connection
    logger.info("\n📋 STEP 2: Checking Supabase Database Connection")
    supabase_ok = check_supabase_connection()
    
    # Check 3: Query Endpoint Testing
    logger.info("\n📋 STEP 3: Testing Query Endpoint")
    query_results = test_query_endpoint()
    
    # Summary
    logger.info("\n" + "=" * 80)
    logger.info("📊 AUDIT SUMMARY")
    logger.info("=" * 80)
    
    logger.info(f"Backend Server: {'✅ Running' if backend_running else '❌ Not Running'}")
    logger.info(f"Supabase Connection: {'✅ Connected' if supabase_ok else '❌ Failed'}")
    
    logger.info("\nQuery Test Results:")
    for query, result in query_results.items():
        if result['success']:
            status = "✅ Success"
            details = f"Doc: {result['document_sources']}, Web: {result['website_sources']}, LLM: {result['llm_fallback']}"
        else:
            status = "❌ Failed"
            details = result['error'][:50] + "..." if len(result['error']) > 50 else result['error']
        
        logger.info(f"  {query}: {status} - {details}")
    
    # Identify Issues
    logger.info("\n🔍 IDENTIFIED ISSUES:")
    issues = []
    
    if not backend_running:
        issues.append("Backend server not running")
    
    if not supabase_ok:
        issues.append("Supabase connection failed")
    
    failed_queries = [q for q, r in query_results.items() if not r['success']]
    if failed_queries:
        issues.append(f"Failed queries: {', '.join(failed_queries)}")
    
    # Check for LLM fallback overuse
    fallback_queries = [q for q, r in query_results.items() if r.get('success') and r.get('llm_fallback')]
    if len(fallback_queries) > 2:  # More than 2 queries using fallback indicates vector search issues
        issues.append(f"Excessive LLM fallback usage: {', '.join(fallback_queries)}")
    
    # Check for missing sources
    no_source_queries = [q for q, r in query_results.items() if r.get('success') and r.get('document_sources', 0) == 0 and r.get('website_sources', 0) == 0 and not r.get('llm_fallback')]
    if no_source_queries:
        issues.append(f"Queries with no sources: {', '.join(no_source_queries)}")
    
    if issues:
        logger.error("❌ Issues found:")
        for issue in issues:
            logger.error(f"  - {issue}")
        return False
    else:
        logger.info("✅ No critical issues found!")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
