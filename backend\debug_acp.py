#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_acp_query():
    base_url = "http://localhost:8000"
    
    print("🔍 Testing ACP Query Debug")
    print("=" * 50)
    
    # Test 1: Debug search specifically for ACP
    print("\n1. Testing Debug Search for 'ACP'")
    try:
        response = requests.get(f"{base_url}/api/debug/search-test?query=ACP", timeout=30)
        if response.status_code == 200:
            data = response.json()
            print("✅ Debug search completed")
            
            # Document search results
            doc_search = data.get("document_search", {})
            enhanced = doc_search.get("enhanced_search", {})
            print(f"   Enhanced search status: {enhanced.get('status', 'unknown')}")
            print(f"   Enhanced search chunks: {enhanced.get('chunks_found', 0)}")
            
            regular = doc_search.get("regular_search", {})
            print(f"   Regular search status: {regular.get('status', 'unknown')}")
            print(f"   Regular search chunks: {regular.get('chunks_found', 0)}")
            
            # Website search results
            web_search = data.get("website_search", {})
            vector = web_search.get("vector_search", {})
            print(f"   Website vector search status: {vector.get('status', 'unknown')}")
            print(f"   Website vector search chunks: {vector.get('chunks_found', 0)}")
            
        else:
            print(f"❌ Debug search failed: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Debug search error: {str(e)}")
    
    # Test 2: Actual query for ACP
    print("\n2. Testing Actual ACP Query")
    try:
        query_data = {
            "query": "ACP",
            "model": "gemini-2.0-flash",
            "fallback_enabled": True,
            "use_hybrid_search": True
        }
        response = requests.post(f"{base_url}/api/query", json=query_data, timeout=60)
        if response.status_code == 200:
            data = response.json()
            print("✅ ACP query completed")
            
            # Results analysis
            doc_sources = data.get("document_sources", [])
            website_sources = data.get("website_sources", [])
            llm_fallback = data.get("llm_fallback", True)
            answer = data.get("answer", "")
            
            print(f"   Document sources found: {len(doc_sources)}")
            print(f"   Website sources found: {len(website_sources)}")
            print(f"   LLM fallback used: {llm_fallback}")
            print(f"   Answer length: {len(answer)} characters")
            
            # Show first few document sources
            if doc_sources:
                print(f"\n   📄 Document Sources (first 3):")
                for i, source in enumerate(doc_sources[:3]):
                    print(f"      {i+1}. {source.get('filename', 'unknown')} - Page {source.get('page_number', 'N/A')}")
                    print(f"         Chunk ID: {source.get('id', 'N/A')}")
                    content = source.get('content', '')[:100] + "..." if len(source.get('content', '')) > 100 else source.get('content', '')
                    print(f"         Content preview: {content}")
                    print()
            
            # Show first few website sources
            if website_sources:
                print(f"\n   🌐 Website Sources (first 3):")
                for i, source in enumerate(website_sources[:3]):
                    print(f"      {i+1}. {source.get('url', 'unknown')}")
                    print(f"         Chunk ID: {source.get('id', 'N/A')}")
                    content = source.get('content', '')[:100] + "..." if len(source.get('content', '')) > 100 else source.get('content', '')
                    print(f"         Content preview: {content}")
                    print()
            
            if not doc_sources and not website_sources:
                print("\n   ⚠️ NO SOURCES FOUND - This is the problem!")
                
        else:
            print(f"❌ ACP query failed: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ ACP query error: {str(e)}")
    
    # Test 3: Check if server is running properly
    print("\n3. Testing Server Health")
    try:
        response = requests.get(f"{base_url}/api/health", timeout=10)
        if response.status_code == 200:
            print("✅ Server is healthy")
        else:
            print(f"❌ Server health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Server health check error: {str(e)}")

if __name__ == "__main__":
    test_acp_query() 