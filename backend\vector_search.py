"""
Optimized vector search implementation for RailGPT.
This module provides concurrent and scalable search functions for document and website content.
"""
import logging
from typing import List, Dict, Any, Optional
import json
import asyncio
from concurrent.futures import ThreadPoolExecutor
import hashlib
from functools import lru_cache
from supabase_client import supabase
from llm_client import get_llm_response

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configure concurrency settings
MAX_CONCURRENT_SEARCHES = 10
SEARCH_TIMEOUT = 30  # seconds
CACHE_TTL = 3600  # 1 hour in seconds

# Thresholds for different search types
DOCUMENT_SIMILARITY_THRESHOLD = 0.75  # High threshold for document chunks
WEBSITE_SIMILARITY_THRESHOLD = 0.65   # Slightly lower for websites
MIN_FALLBACK_THRESHOLD = 0.4         # Minimum threshold for any results

# Create thread pool for concurrent searches
search_executor = ThreadPoolExecutor(max_workers=MAX_CONCURRENT_SEARCHES)

def generate_cache_key(query_embedding: List[float], query_text: str, source_type: str) -> str:
    """Generate a cache key for search results."""
    key_data = f"{json.dumps(query_embedding)}_{query_text}_{source_type}"
    return hashlib.md5(key_data.encode()).hexdigest()

async def search_document_chunks(query_embedding: List[float], query_text: str, top_k: int = 30) -> List[Dict[str, Any]]:
    """Search document chunks using vector similarity and hybrid search."""
    try:
        # Try direct vector search first
        vector_results = await supabase.rpc(
            "direct_search_document_chunks",
            {
                "query_embedding": query_embedding,
                "match_threshold": DOCUMENT_SIMILARITY_THRESHOLD,
                "match_count": top_k * 2
            }
        ).execute()

        chunks = vector_results.data if hasattr(vector_results, "data") else vector_results
        if not chunks or not isinstance(chunks, list):
            return []

        # Process and validate chunks
        valid_chunks = []
        for chunk in chunks:
            # Ensure all metadata is present
            if not all(k in chunk for k in ["document_id", "page_number", "text"]):
                continue
                
            # Add source information
            chunk["source_type"] = "document"
            chunk["source"] = {
                "document_id": chunk["document_id"],
                "page_number": chunk["page_number"],
                "filename": chunk.get("title", "Unknown Document")
            }
            
            # Check for non-textual content
            metadata = chunk.get("metadata", {})
            if isinstance(metadata, str):
                try:
                    metadata = json.loads(metadata)
                except:
                    metadata = {}
                    
            if metadata.get("has_images") or metadata.get("has_tables") or metadata.get("has_charts"):
                chunk["visual_content"] = {
                    "images": metadata.get("images", []),
                    "tables": metadata.get("tableHtml", []),
                    "charts": metadata.get("chartSvg", [])
                }
            
            valid_chunks.append(chunk)

        # Sort by similarity and return top results
        return sorted(valid_chunks, key=lambda x: float(x.get("similarity", 0)), reverse=True)[:top_k]

    except Exception as e:
        logger.error(f"Error in document chunk search: {str(e)}")
        return []

async def search_website_chunks(query_embedding: List[float], query_text: str, top_k: int = 30) -> List[Dict[str, Any]]:
    """Search website chunks only if no document chunks are found."""
    try:
        vector_results = await supabase.rpc(
            "direct_search_website_chunks",
            {
                "query_embedding": query_embedding,
                "match_threshold": WEBSITE_SIMILARITY_THRESHOLD,
                "match_count": top_k
            }
        ).execute()

        chunks = vector_results.data if hasattr(vector_results, "data") else vector_results
        if not chunks or not isinstance(chunks, list):
            return []

        # Process and validate chunks
        valid_chunks = []
        for chunk in chunks:
            if not chunk.get("text"):
                continue

            chunk["source_type"] = "website"
            chunk["source"] = {
                "url": chunk.get("url", ""),
                "domain": chunk.get("domain", ""),
                "title": chunk.get("title", "Unknown Website")
            }

            valid_chunks.append(chunk)

        return sorted(valid_chunks, key=lambda x: float(x.get("similarity", 0)), reverse=True)[:top_k]

    except Exception as e:
        logger.error(f"Error in website chunk search: {str(e)}")
        return []

async def search_all_sources(query_text: str, query_embedding: List[float], top_k: int = 30) -> Dict[str, Any]:
    """
    Search all sources in priority order:
    1. Document chunks
    2. Website chunks
    3. LLM fallback
    """
    results = {
        "chunks": [],
        "source_types": [],
        "llm_fallback_used": False,
        "total_chunks": 0,
        "debug_info": {}
    }

    # Search document chunks first
    doc_chunks = await search_document_chunks(query_embedding, query_text, top_k)
    
    if doc_chunks:
        results["chunks"] = doc_chunks
        results["source_types"].append("document")
        results["total_chunks"] = len(doc_chunks)
        results["debug_info"]["document_chunks_found"] = len(doc_chunks)
        return results

    # If no document chunks, try website chunks
    web_chunks = await search_website_chunks(query_embedding, query_text, top_k)
    
    if web_chunks:
        results["chunks"] = web_chunks
        results["source_types"].append("website")
        results["total_chunks"] = len(web_chunks)
        results["debug_info"]["website_chunks_found"] = len(web_chunks)
        return results

    # If no chunks found, mark for LLM fallback
    results["llm_fallback_used"] = True
    results["debug_info"]["fallback_reason"] = "No relevant chunks found"
    
    return results
