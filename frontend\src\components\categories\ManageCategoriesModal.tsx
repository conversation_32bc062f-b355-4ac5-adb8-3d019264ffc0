import React, { useState, useEffect, useCallback } from 'react';
import { X, Plus, Edit2, Trash2, ChevronDown, ChevronRight, FolderPlus, Save, AlertTriangle, Check, Settings, Upload } from 'lucide-react';
import { CategoryHierarchy, CategoryCreate, CategoryUpdate } from '../../types/documents';
import { 
  getDocumentCategoriesWithStats, 
  getWebsiteCategoriesWithStats,
  createCategory,
  createWebsiteCategory,
  updateCategory,
  deleteCategory,
  deleteWebsiteCategory,
  getCategoryHierarchy
} from '../../services/categoryApi';
import BulkCategoryImport from './BulkCategoryImport';

interface ManageCategoriesModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'document' | 'website';
  onCategoryUpdated?: () => void;
}

interface CategoryNode extends CategoryHierarchy {
  expanded?: boolean;
  editing?: boolean;
  children: CategoryNode[];
}

interface CategoryStats {
  total_count: number;
  max_depth: number;
  statistics: {
    main_categories: number;
    categories: number;
    sub_categories: number;
    minor_categories: number;
    active: number;
    inactive: number;
  };
}

interface DeleteConfirmation {
  isOpen: boolean;
  category: CategoryNode | null;
  hasChildren: boolean;
  linkedCount: number;
}

const ManageCategoriesModal: React.FC<ManageCategoriesModalProps> = ({
  isOpen,
  onClose,
  type,
  onCategoryUpdated
}) => {
  // State management
  const [categories, setCategories] = useState<CategoryNode[]>([]);
  const [stats, setStats] = useState<CategoryStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Category operations state
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const [editingCategory, setEditingCategory] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const [editingDescription, setEditingDescription] = useState('');

  // Create new category state
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [createParentId, setCreateParentId] = useState<string | null>(null);
  const [createLevel, setCreateLevel] = useState<number>(1);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [newCategoryDescription, setNewCategoryDescription] = useState('');

  // Delete confirmation state
  const [deleteConfirmation, setDeleteConfirmation] = useState<DeleteConfirmation>({
    isOpen: false,
    category: null,
    hasChildren: false,
    linkedCount: 0
  });

  // Bulk import state
  const [showBulkImport, setShowBulkImport] = useState(false);

  // Load categories when modal opens
  useEffect(() => {
    if (isOpen) {
      loadCategories();
    }
  }, [isOpen, type]);

  // Auto-clear messages
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => setSuccess(null), 3000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  const loadCategories = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log(`Loading categories for type: ${type}`);
      const response = type === 'document' 
        ? await getDocumentCategoriesWithStats()
        : await getWebsiteCategoriesWithStats();
      
      console.log('Loaded categories response:', response);

      // Transform categories to include UI state
      const transformedCategories = transformCategoriesToNodes(response.categories);
      
      setCategories(transformedCategories);
      setStats({
        total_count: response.total_count,
        max_depth: response.max_depth,
        statistics: response.statistics
      });

      // Auto-expand first level
      const firstLevelIds = transformedCategories.map(cat => cat.id);
      setExpandedNodes(new Set(firstLevelIds));

    } catch (err: any) {
      console.error('Error loading categories:', err);
      setError(err.message || 'Failed to load categories');
    } finally {
      setLoading(false);
    }
  };

  const transformCategoriesToNodes = (cats: CategoryHierarchy[]): CategoryNode[] => {
    const transform = (category: CategoryHierarchy): CategoryNode => ({
      ...category,
      expanded: false,
      editing: false,
      children: category.children ? category.children.map(transform) : []
    });

    return cats.map(transform);
  };

  const toggleExpanded = (categoryId: string) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId);
      } else {
        newSet.add(categoryId);
      }
      return newSet;
    });
  };

  const startEdit = (category: CategoryNode) => {
    setEditingCategory(category.id);
    setEditingName(category.name);
    setEditingDescription(category.description || '');
  };

  const cancelEdit = () => {
    setEditingCategory(null);
    setEditingName('');
    setEditingDescription('');
  };

  const saveEdit = async () => {
    if (!editingCategory || !editingName.trim()) return;

    try {
      setLoading(true);
      const updates: CategoryUpdate = {
        name: editingName.trim(),
        description: editingDescription.trim() || undefined
      };

      await updateCategory(editingCategory, updates);
      
      setSuccess('Category updated successfully');
      cancelEdit();
      loadCategories();
      onCategoryUpdated?.();

    } catch (err: any) {
      setError(err.message || 'Failed to update category');
    } finally {
      setLoading(false);
    }
  };

  const startCreate = (parentId: string | null = null, level: number = 1) => {
    console.log(`startCreate called with parentId: ${parentId}, level: ${level}`);
    setCreateParentId(parentId);
    setCreateLevel(level);
    setNewCategoryName('');
    setNewCategoryDescription('');
    setShowCreateForm(true);
  };

  const cancelCreate = () => {
    setShowCreateForm(false);
    setCreateParentId(null);
    setNewCategoryName('');
    setNewCategoryDescription('');
  };

  const createNewCategory = async () => {
    if (!newCategoryName.trim()) return;

    try {
      setLoading(true);
      
      // Determine category type based on level
      const typeMap = {
        1: 'main_category',
        2: 'category', 
        3: 'sub_category',
        4: 'minor_category'
      };

      console.log('Creating category with level:', createLevel, 'parent:', createParentId);
      console.log('Type mapping result:', typeMap[createLevel as keyof typeof typeMap]);

      const newCategory: CategoryCreate = {
        name: newCategoryName.trim(),
        type: typeMap[createLevel as keyof typeof typeMap] as 'main_category' | 'category' | 'sub_category' | 'minor_category',
        parent_id: createParentId || undefined,
        description: newCategoryDescription.trim() || undefined,
        sort_order: 0
      };

      console.log('Creating category:', newCategory);

      if (type === 'document') {
        await createCategory(newCategory);
      } else {
        await createWebsiteCategory(newCategory);
      }

      setSuccess('Category created successfully');
      cancelCreate();
      loadCategories();
      onCategoryUpdated?.();

    } catch (err: any) {
      console.error('Category creation error:', err);
      setError(err.message || 'Failed to create category');
    } finally {
      setLoading(false);
    }
  };

  const startDelete = async (category: CategoryNode) => {
    // Check if category has children or linked items
    const hasChildren = category.children && category.children.length > 0;
    
    // TODO: Get actual linked count from API
    const linkedCount = 0; // This would come from checking documents/websites

    setDeleteConfirmation({
      isOpen: true,
      category,
      hasChildren: hasChildren || false,
      linkedCount
    });
  };

  const confirmDelete = async () => {
    if (!deleteConfirmation.category) return;

    try {
      setLoading(true);
      
      if (type === 'document') {
        await deleteCategory(deleteConfirmation.category.id, false);
      } else {
        await deleteWebsiteCategory(deleteConfirmation.category.id, false);
      }

      setSuccess('Category deleted successfully');
      setDeleteConfirmation({ isOpen: false, category: null, hasChildren: false, linkedCount: 0 });
      loadCategories();
      onCategoryUpdated?.();

    } catch (err: any) {
      setError(err.message || 'Failed to delete category');
    } finally {
      setLoading(false);
    }
  };

  const cancelDelete = () => {
    setDeleteConfirmation({ isOpen: false, category: null, hasChildren: false, linkedCount: 0 });
  };

  const handleBulkImportComplete = () => {
    setShowBulkImport(false);
    loadCategories();
    onCategoryUpdated?.();
  };

  // Expand all categories recursively
  const expandAllCategories = () => {
    const getAllCategoryIds = (cats: CategoryNode[]): string[] => {
      const ids: string[] = [];
      cats.forEach(cat => {
        ids.push(cat.id);
        if (cat.children && cat.children.length > 0) {
          ids.push(...getAllCategoryIds(cat.children));
        }
      });
      return ids;
    };
    
    const allIds = getAllCategoryIds(categories);
    setExpandedNodes(new Set(allIds));
  };

  // Collapse all categories
  const collapseAllCategories = () => {
    setExpandedNodes(new Set());
  };

  const getLevelColor = (level: number) => {
    const colors = {
      0: 'text-blue-600 bg-blue-50',
      1: 'text-green-600 bg-green-50', 
      2: 'text-orange-600 bg-orange-50',
      3: 'text-purple-600 bg-purple-50'
    };
    return colors[level as keyof typeof colors] || 'text-gray-600 bg-gray-50';
  };

  const getLevelIcon = (level: number) => {
    const icons = {
      0: '🏷️', // Main Category
      1: '📁', // Category
      2: '📂', // Sub Category
      3: '🗂️'  // Minor Category
    };
    return icons[level as keyof typeof icons] || '📄';
  };

  const renderCategoryNode = (category: CategoryNode, depth: number = 0) => {
    const isExpanded = expandedNodes.has(category.id);
    const isEditing = editingCategory === category.id;
    const hasChildren = category.children && category.children.length > 0;
    const canAddChild = (category.level || 0) < 3; // Max 4 levels (0-3)
    
    console.log(`Rendering category: ${category.name}, Level: ${category.level}, Can add child: ${canAddChild}`);

    return (
      <div key={category.id} className="mb-1">
        {/* Category Row */}
        <div 
          className={`flex items-center group hover:bg-gray-50 rounded-lg transition-colors ${depth > 0 ? 'ml-6' : ''}`}
          style={{ paddingLeft: `${depth * 12}px` }}
        >
          {/* Expand/Collapse Button */}
          <button
            onClick={() => toggleExpanded(category.id)}
            className={`w-6 h-6 flex items-center justify-center mr-2 rounded hover:bg-gray-200 transition-colors ${
              hasChildren ? 'text-gray-600' : 'text-transparent'
            }`}
            disabled={!hasChildren}
          >
            {hasChildren ? (
              isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />
            ) : null}
          </button>

          {/* Category Icon */}
          <span className="text-lg mr-2">{getLevelIcon(category.level || 0)}</span>

          {/* Category Content */}
          <div className="flex-1 min-w-0">
            {isEditing ? (
              <div className="flex flex-col gap-2 py-2">
                <input
                  type="text"
                  value={editingName}
                  onChange={(e) => setEditingName(e.target.value)}
                  className="px-3 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Category name"
                  autoFocus
                />
                <input
                  type="text"
                  value={editingDescription}
                  onChange={(e) => setEditingDescription(e.target.value)}
                  className="px-3 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Description (optional)"
                />
                <div className="flex gap-2">
                  <button
                    onClick={saveEdit}
                    className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors flex items-center gap-1"
                    disabled={loading}
                  >
                    <Save size={14} />
                    Save
                  </button>
                  <button
                    onClick={cancelEdit}
                    className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors flex items-center gap-1"
                  >
                    <X size={14} />
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div className="py-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-gray-900">{category.name}</span>
                  <span className={`px-2 py-1 text-xs rounded-full ${getLevelColor(category.level || 0)}`}>
                    Level {(category.level || 0) + 1}
                  </span>
                  {category.is_active === false && (
                    <span className="px-2 py-1 text-xs bg-red-100 text-red-600 rounded-full">
                      Inactive
                    </span>
                  )}
                </div>
                {category.description && (
                  <p className="text-sm text-gray-600 mt-1">{category.description}</p>
                )}
                {category.full_path && (
                  <p className="text-xs text-gray-500 mt-1">Path: {category.full_path}</p>
                )}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          {!isEditing && (
            <div className="flex items-center gap-1 opacity-100 transition-opacity">
              {canAddChild && (
                <button
                  onClick={() => {
                    const currentLevel = category.level || 0;
                    const nextLevel = currentLevel + 1; // Correct calculation: current level + 1
                    console.log(`Adding subcategory to: ${category.name} (Level ${currentLevel}) -> Next level: ${nextLevel}`);
                    startCreate(category.id, nextLevel + 1); // Add 1 to convert to 1-based indexing for typeMap
                  }}
                  className="p-1 text-green-600 hover:bg-green-100 rounded transition-colors"
                  title="Add subcategory"
                >
                  <FolderPlus size={16} />
                </button>
              )}
              <button
                onClick={() => startEdit(category)}
                className="p-1 text-blue-600 hover:bg-blue-100 rounded transition-colors"
                title="Edit category"
              >
                <Edit2 size={16} />
              </button>
              <button
                onClick={() => startDelete(category)}
                className="p-1 text-red-600 hover:bg-red-100 rounded transition-colors"
                title="Delete category"
              >
                <Trash2 size={16} />
              </button>
            </div>
          )}
        </div>

        {/* Children */}
        {isExpanded && hasChildren && (
          <div className="ml-6 border-l border-gray-200 pl-2">
            {category.children.map(child => renderCategoryNode(child, depth + 1))}
          </div>
        )}
      </div>
    );
  };



  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <Settings className="text-blue-600" size={24} />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Manage {type === 'document' ? 'Document' : 'Website'} Categories
              </h2>
              <p className="text-sm text-gray-600">
                Create, edit, and organize your category hierarchy
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Stats Bar */}
        {stats && (
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <div className="grid grid-cols-2 md:grid-cols-6 gap-4 text-sm">
              <div className="text-center">
                <div className="font-semibold text-gray-900">{stats.statistics.main_categories}</div>
                <div className="text-gray-600">Main Categories</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-gray-900">{stats.statistics.categories}</div>
                <div className="text-gray-600">Categories</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-gray-900">{stats.statistics.sub_categories}</div>
                <div className="text-gray-600">Subcategories</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-gray-900">{stats.statistics.minor_categories}</div>
                <div className="text-gray-600">Minor Categories</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-green-600">{stats.statistics.active}</div>
                <div className="text-gray-600">Active</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-red-600">{stats.statistics.inactive}</div>
                <div className="text-gray-600">Inactive</div>
              </div>
            </div>
          </div>
        )}

        {/* Messages */}
        {error && (
          <div className="mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center gap-2 text-red-700">
              <AlertTriangle size={16} />
              <span className="text-sm">{error}</span>
            </div>
          </div>
        )}

        {success && (
          <div className="mx-6 mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
            <div className="flex items-center gap-2 text-green-700">
              <Check size={16} />
              <span className="text-sm">{success}</span>
            </div>
          </div>
        )}

        {/* Action Bar */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <button
                onClick={() => startCreate(null, 1)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
                disabled={loading}
              >
                <Plus size={16} />
                Add Main Category
              </button>
              <button
                onClick={() => setShowBulkImport(true)}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
                disabled={loading}
              >
                <Upload size={16} />
                Bulk Import
              </button>
              <button
                onClick={loadCategories}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                disabled={loading}
              >
                Refresh
              </button>
              {/* Expand/Collapse All Controls */}
              <div className="flex items-center gap-2 border-l border-gray-300 pl-3">
                <button
                  onClick={expandAllCategories}
                  className="px-3 py-1 text-sm bg-blue-50 text-blue-600 rounded hover:bg-blue-100 transition-colors flex items-center gap-1"
                  disabled={loading || categories.length === 0}
                  title="Expand all categories"
                >
                  <ChevronDown size={14} />
                  Expand All
                </button>
                <button
                  onClick={collapseAllCategories}
                  className="px-3 py-1 text-sm bg-gray-50 text-gray-600 rounded hover:bg-gray-100 transition-colors flex items-center gap-1"
                  disabled={loading || categories.length === 0}
                  title="Collapse all categories"
                >
                  <ChevronRight size={14} />
                  Collapse All
                </button>
              </div>
            </div>
            <div className="text-sm text-gray-600">
              Total: {stats?.total_count || 0} categories
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {loading && !categories.length ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Loading categories...</span>
            </div>
          ) : categories.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 text-lg mb-2">📁</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Categories Found</h3>
              <p className="text-gray-600 mb-4">
                Get started by creating your first main category.
              </p>
              <button
                onClick={() => startCreate(null, 1)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Create First Category
              </button>
            </div>
          ) : (
            <div className="space-y-1">
              {categories.map(category => renderCategoryNode(category))}
            </div>
          )}
        </div>

        {/* Create Form Modal */}
        {showCreateForm && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Create New Category (Level {createLevel})
                  {createParentId && <span className="text-sm text-gray-600"> - Parent ID: {createParentId}</span>}
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category Name *
                    </label>
                    <input
                      type="text"
                      value={newCategoryName}
                      onChange={(e) => setNewCategoryName(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter category name"
                      autoFocus
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      value={newCategoryDescription}
                      onChange={(e) => setNewCategoryDescription(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Optional description"
                      rows={3}
                    />
                  </div>
                  <div className="flex gap-3 justify-end">
                    <button
                      onClick={cancelCreate}
                      className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={createNewCategory}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                      disabled={!newCategoryName.trim() || loading}
                    >
                      Create Category
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {deleteConfirmation.isOpen && deleteConfirmation.category && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
              <div className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <AlertTriangle className="text-red-600" size={24} />
                  <h3 className="text-lg font-semibold text-gray-900">
                    Delete Category
                  </h3>
                </div>
                <div className="space-y-3 mb-6">
                  <p className="text-gray-700">
                    Are you sure you want to delete <strong>"{deleteConfirmation.category.name}"</strong>?
                  </p>
                  {deleteConfirmation.hasChildren && (
                    <p className="text-red-600 text-sm">
                      ⚠️ This category has subcategories that will also be deleted.
                    </p>
                  )}
                  {deleteConfirmation.linkedCount > 0 && (
                    <p className="text-red-600 text-sm">
                      ⚠️ This category is linked to {deleteConfirmation.linkedCount} {type}(s).
                    </p>
                  )}
                  <p className="text-gray-600 text-sm">
                    This action cannot be undone.
                  </p>
                </div>
                <div className="flex gap-3 justify-end">
                  <button
                    onClick={cancelDelete}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={confirmDelete}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                    disabled={loading}
                  >
                    Delete Category
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Bulk Import Modal */}
        <BulkCategoryImport
          isOpen={showBulkImport}
          onClose={() => setShowBulkImport(false)}
          type={type}
          onImportComplete={handleBulkImportComplete}
        />
      </div>
    </div>
  );
};

export default ManageCategoriesModal; 